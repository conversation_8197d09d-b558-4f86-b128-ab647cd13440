#!/bin/bash

# WorldTimeApp 一键部署脚本
# 支持多种部署方案

echo "🚀 WorldTimeApp 部署脚本"
echo "========================"

# 检查是否安装了必要工具
check_requirements() {
    echo "检查系统要求..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装"
        exit 1
    fi
    
    echo "✅ 系统要求检查通过"
}

# Vercel 部署
deploy_vercel() {
    echo "🔄 使用 Vercel 部署..."
    
    if ! command -v vercel &> /dev/null; then
        echo "安装 Vercel CLI..."
        npm install -g vercel
    fi
    
    echo "构建项目..."
    npm run build
    
    echo "部署到 Vercel..."
    vercel --prod
    
    echo "✅ Vercel 部署完成！"
    echo "💡 请在 Vercel 控制台绑定域名 worldtimeapp.online"
}

# Docker 部署
deploy_docker() {
    echo "🔄 使用 Docker 部署..."
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    echo "构建 Docker 镜像..."
    docker build -t worldtimeapp .
    
    echo "停止旧容器..."
    docker stop worldtimeapp 2>/dev/null || true
    docker rm worldtimeapp 2>/dev/null || true
    
    echo "启动新容器..."
    docker run -d -p 3000:3000 --name worldtimeapp worldtimeapp
    
    echo "✅ Docker 部署完成！"
    echo "🌐 应用运行在 http://localhost:3000"
}

# Docker Compose 部署
deploy_docker_compose() {
    echo "🔄 使用 Docker Compose 部署..."
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装"
        exit 1
    fi
    
    echo "启动服务..."
    docker-compose up -d --build
    
    echo "✅ Docker Compose 部署完成！"
    echo "🌐 应用运行在 http://localhost:80"
}

# 本地开发部署
deploy_local() {
    echo "🔄 本地开发部署..."
    
    echo "安装依赖..."
    npm install
    
    echo "构建项目..."
    npm run build
    
    echo "启动生产服务器..."
    npm start
}

# PM2 部署
deploy_pm2() {
    echo "🔄 使用 PM2 部署..."
    
    if ! command -v pm2 &> /dev/null; then
        echo "安装 PM2..."
        npm install -g pm2
    fi
    
    echo "安装依赖..."
    npm install
    
    echo "构建项目..."
    npm run build
    
    echo "停止旧进程..."
    pm2 stop worldtimeapp 2>/dev/null || true
    pm2 delete worldtimeapp 2>/dev/null || true
    
    echo "启动新进程..."
    pm2 start npm --name "worldtimeapp" -- start
    
    echo "保存 PM2 配置..."
    pm2 save
    
    echo "✅ PM2 部署完成！"
    echo "🌐 应用运行在 http://localhost:3000"
    echo "📊 使用 'pm2 status' 查看状态"
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择部署方案："
    echo "1) Vercel 部署 (推荐 - 免费)"
    echo "2) Docker 部署"
    echo "3) Docker Compose 部署"
    echo "4) PM2 部署"
    echo "5) 本地开发部署"
    echo "6) 退出"
    echo ""
    read -p "请输入选项 (1-6): " choice
}

# 主逻辑
main() {
    check_requirements
    
    while true; do
        show_menu
        case $choice in
            1)
                deploy_vercel
                break
                ;;
            2)
                deploy_docker
                break
                ;;
            3)
                deploy_docker_compose
                break
                ;;
            4)
                deploy_pm2
                break
                ;;
            5)
                deploy_local
                break
                ;;
            6)
                echo "退出部署"
                exit 0
                ;;
            *)
                echo "无效选项，请重新选择"
                ;;
        esac
    done
}

# 运行主函数
main 