import { NextRequest, NextResponse } from 'next/server'
import { backendDatabase } from '@/lib/backend-database'

export async function GET(request: NextRequest) {
  try {
    // 确保数据库已初始化
    await backendDatabase.initialize()
    
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const country = searchParams.get('country')
    const continent = searchParams.get('continent')
    const popular = searchParams.get('popular')
    
    let cities
    
    if (search) {
      cities = await backendDatabase.searchCities(search)
    } else if (country) {
      cities = await backendDatabase.getCitiesByCountry(country)
    } else if (continent) {
      cities = await backendDatabase.getCitiesByContinent(continent)
    } else if (popular) {
      const limit = parseInt(popular) || 10
      cities = await backendDatabase.getPopularCities(limit)
    } else {
      cities = await backendDatabase.getAllCities()
    }
    
    return NextResponse.json({
      success: true,
      data: cities,
      count: cities.length
    })
  } catch (error) {
    console.error('获取城市数据失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取城市数据失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await backendDatabase.initialize()
    
    const city = await request.json()
    
    // 验证必需字段
    const requiredFields = ['id', 'city', 'cityEn', 'country', 'countryEn', 'timezone', 'latitude', 'longitude']
    for (const field of requiredFields) {
      if (!city[field]) {
        return NextResponse.json({
          success: false,
          error: `缺少必需字段: ${field}`
        }, { status: 400 })
      }
    }
    
    await backendDatabase.addCity(city)
    
    return NextResponse.json({
      success: true,
      message: '城市添加成功'
    })
  } catch (error) {
    console.error('添加城市失败:', error)
    return NextResponse.json({
      success: false,
      error: '添加城市失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 