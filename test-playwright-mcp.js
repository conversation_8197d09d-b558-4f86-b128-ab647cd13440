// 测试 @playwright/mcp 依赖问题的脚本

console.log('开始测试 @playwright/mcp 依赖...');

try {
  // 尝试导入 @modelcontextprotocol/sdk
  console.log('尝试导入 @modelcontextprotocol/sdk...');
  const sdk = require('@modelcontextprotocol/sdk');
  console.log('✅ @modelcontextprotocol/sdk 导入成功');
  
  // 尝试导入 server/sse.js
  console.log('尝试导入 server/sse.js...');
  const sse = require('@modelcontextprotocol/sdk/server/sse.js');
  console.log('✅ server/sse.js 导入成功');
  
} catch (error) {
  console.error('❌ 导入失败:', error.message);
  console.error('错误详情:', error);
}

try {
  // 尝试导入 @playwright/mcp
  console.log('尝试导入 @playwright/mcp...');
  const playwrightMcp = require('@playwright/mcp');
  console.log('✅ @playwright/mcp 导入成功');
  
} catch (error) {
  console.error('❌ @playwright/mcp 导入失败:', error.message);
  console.error('错误详情:', error);
}

console.log('测试完成');
