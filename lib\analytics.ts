// Google Analytics 4 配置和工具函数

declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

// GA4 测量ID (在生产环境中应该从环境变量获取)
export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX'

// 初始化 Google Analytics
export const initGA = () => {
  if (typeof window === 'undefined') return

  // 创建 dataLayer
  window.dataLayer = window.dataLayer || []
  
  // 定义 gtag 函数
  window.gtag = function() {
    window.dataLayer.push(arguments)
  }

  // 配置 GA4
  window.gtag('js', new Date())
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
    // 启用增强型电子商务
    enhanced_ecommerce: true,
    // 自定义维度
    custom_map: {
      'custom_dimension_1': 'language',
      'custom_dimension_2': 'timezone',
      'custom_dimension_3': 'device_type'
    }
  })
}

// 页面浏览跟踪
export const trackPageView = (url: string, title?: string, language?: string) => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('config', GA_MEASUREMENT_ID, {
    page_path: url,
    page_title: title || document.title,
    custom_dimension_1: language || 'unknown'
  })
}

// 事件跟踪
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number,
  customParameters?: Record<string, any>
) => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
    ...customParameters
  })
}

// 语言切换跟踪
export const trackLanguageChange = (fromLanguage: string, toLanguage: string) => {
  trackEvent('language_change', 'user_interaction', `${fromLanguage}_to_${toLanguage}`)
}

// 城市搜索跟踪
export const trackCitySearch = (searchTerm: string, resultsCount: number, language: string) => {
  trackEvent('search', 'city_search', searchTerm, resultsCount, {
    search_term: searchTerm,
    results_count: resultsCount,
    language: language
  })
}

// 城市查看跟踪
export const trackCityView = (cityName: string, cityId: string, language: string) => {
  trackEvent('view_city', 'content_interaction', cityName, undefined, {
    city_id: cityId,
    city_name: cityName,
    language: language
  })
}

// 城市对比跟踪
export const trackCityComparison = (cities: string[], language: string) => {
  trackEvent('compare_cities', 'feature_usage', cities.join(','), cities.length, {
    cities: cities,
    comparison_count: cities.length,
    language: language
  })
}

// PWA 安装跟踪
export const trackPWAInstall = (method: 'prompt' | 'manual', language: string) => {
  trackEvent('pwa_install', 'app_engagement', method, undefined, {
    install_method: method,
    language: language
  })
}

// 离线使用跟踪
export const trackOfflineUsage = (feature: string, language: string) => {
  trackEvent('offline_usage', 'app_engagement', feature, undefined, {
    offline_feature: feature,
    language: language
  })
}

// 错误跟踪
export const trackError = (errorMessage: string, errorType: string, page: string) => {
  trackEvent('error', 'technical', errorType, undefined, {
    error_message: errorMessage,
    error_type: errorType,
    page: page
  })
}

// 性能跟踪
export const trackPerformance = (metricName: string, value: number, unit: string) => {
  trackEvent('performance', 'technical', metricName, value, {
    metric_name: metricName,
    metric_value: value,
    metric_unit: unit
  })
}

// 用户参与度跟踪
export const trackEngagement = (action: string, duration?: number, language?: string) => {
  trackEvent('engagement', 'user_behavior', action, duration, {
    engagement_action: action,
    duration: duration,
    language: language
  })
}

// 转化跟踪
export const trackConversion = (conversionType: string, value?: number, language?: string) => {
  trackEvent('conversion', 'business', conversionType, value, {
    conversion_type: conversionType,
    language: language
  })
}

// 自定义事件跟踪
export const trackCustomEvent = (
  eventName: string,
  parameters: Record<string, any> = {}
) => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('event', eventName, parameters)
}

// 用户属性设置
export const setUserProperties = (properties: Record<string, any>) => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('config', GA_MEASUREMENT_ID, {
    user_properties: properties
  })
}

// 同意管理
export const updateConsentSettings = (consentSettings: {
  analytics_storage?: 'granted' | 'denied'
  ad_storage?: 'granted' | 'denied'
  functionality_storage?: 'granted' | 'denied'
  personalization_storage?: 'granted' | 'denied'
  security_storage?: 'granted' | 'denied'
}) => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('consent', 'update', consentSettings)
}

// 电子商务跟踪 (如果将来需要)
export const trackPurchase = (transactionId: string, items: any[], value: number, currency: string = 'USD') => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('event', 'purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items
  })
}

// 调试模式
export const enableDebugMode = () => {
  if (typeof window === 'undefined' || !window.gtag) return

  window.gtag('config', GA_MEASUREMENT_ID, {
    debug_mode: true
  })
}

// 获取客户端ID
export const getClientId = (): Promise<string> => {
  return new Promise((resolve) => {
    if (typeof window === 'undefined' || !window.gtag) {
      resolve('unknown')
      return
    }

    window.gtag('get', GA_MEASUREMENT_ID, 'client_id', (clientId: string) => {
      resolve(clientId)
    })
  })
}

// Core Web Vitals 跟踪
export const trackWebVitals = () => {
  if (typeof window === 'undefined') return

  // 使用 web-vitals 库跟踪性能指标
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    getCLS((metric) => {
      trackPerformance('CLS', metric.value, 'score')
    })

    getFID((metric) => {
      trackPerformance('FID', metric.value, 'ms')
    })

    getFCP((metric) => {
      trackPerformance('FCP', metric.value, 'ms')
    })

    getLCP((metric) => {
      trackPerformance('LCP', metric.value, 'ms')
    })

    getTTFB((metric) => {
      trackPerformance('TTFB', metric.value, 'ms')
    })
  }).catch((error) => {
    console.warn('Failed to load web-vitals:', error)
  })
}

// 初始化分析
export const initAnalytics = (language?: string) => {
  if (typeof window === 'undefined') return

  // 初始化 GA
  initGA()

  // 设置用户属性
  if (language) {
    setUserProperties({
      language: language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      device_type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop'
    })
  }

  // 跟踪 Core Web Vitals
  trackWebVitals()

  // 跟踪页面加载时间
  window.addEventListener('load', () => {
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
    trackPerformance('page_load_time', loadTime, 'ms')
  })
}
