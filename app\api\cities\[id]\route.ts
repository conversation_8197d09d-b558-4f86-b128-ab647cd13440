import { NextRequest, NextResponse } from 'next/server'
import { backendDatabase } from '@/lib/backend-database'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await backendDatabase.initialize()
    
    const { id } = await params
    const city = await backendDatabase.getCityById(id)
    
    if (!city) {
      return NextResponse.json({
        success: false,
        error: '城市未找到'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      data: city
    })
  } catch (error) {
    console.error('获取城市失败:', error)
    return NextResponse.json({
      success: false,
      error: '获取城市失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await backendDatabase.initialize()
    
    const { id } = await params
    const updates = await request.json()
    
    // 检查城市是否存在
    const existingCity = await backendDatabase.getCityById(id)
    if (!existingCity) {
      return NextResponse.json({
        success: false,
        error: '城市未找到'
      }, { status: 404 })
    }
    
    await backendDatabase.updateCity(id, updates)
    
    // 返回更新后的城市数据
    const updatedCity = await backendDatabase.getCityById(id)
    
    return NextResponse.json({
      success: true,
      data: updatedCity,
      message: '城市更新成功'
    })
  } catch (error) {
    console.error('更新城市失败:', error)
    return NextResponse.json({
      success: false,
      error: '更新城市失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await backendDatabase.initialize()
    
    const { id } = await params
    
    // 检查城市是否存在
    const existingCity = await backendDatabase.getCityById(id)
    if (!existingCity) {
      return NextResponse.json({
        success: false,
        error: '城市未找到'
      }, { status: 404 })
    }
    
    await backendDatabase.deleteCity(id)
    
    return NextResponse.json({
      success: true,
      message: '城市删除成功'
    })
  } catch (error) {
    console.error('删除城市失败:', error)
    return NextResponse.json({
      success: false,
      error: '删除城市失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 