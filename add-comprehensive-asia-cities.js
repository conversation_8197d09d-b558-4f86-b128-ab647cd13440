const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 导入城市数据
const comprehensiveAsiaCities = require('./comprehensive-asia-cities.js');

// 数据库文件路径
const dbPath = path.join(__dirname, 'data', 'cities.db');

// 确保数据目录存在
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 连接数据库
const db = new Database(dbPath);

// 创建表（如果不存在）
const createTableSQL = `
  CREATE TABLE IF NOT EXISTS cities (
    id TEXT PRIMARY KEY,
    city TEXT NOT NULL,
    cityEn TEXT NOT NULL,
    country TEXT NOT NULL,
    countryEn TEXT NOT NULL,
    flag TEXT NOT NULL,
    timezone TEXT NOT NULL,
    utcOffset TEXT NOT NULL,
    continent TEXT NOT NULL,
    continentEn TEXT NOT NULL,
    iana TEXT NOT NULL,
    dstStatus TEXT NOT NULL,
    dstStatusEn TEXT NOT NULL,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    sunrise TEXT NOT NULL,
    sunset TEXT NOT NULL,
    population INTEGER,
    currency TEXT,
    language TEXT,
    areaCode TEXT,
    elevation INTEGER,
    website TEXT,
    established TEXT,
    mayor TEXT,
    gdp REAL,
    area REAL,
    density REAL,
    nickname TEXT,
    nicknameEn TEXT,
    description TEXT,
    descriptionEn TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE INDEX IF NOT EXISTS idx_city_name ON cities(city, cityEn);
  CREATE INDEX IF NOT EXISTS idx_country ON cities(country, countryEn);
  CREATE INDEX IF NOT EXISTS idx_continent ON cities(continent, continentEn);
  CREATE INDEX IF NOT EXISTS idx_timezone ON cities(timezone);
  CREATE INDEX IF NOT EXISTS idx_population ON cities(population);
`;

try {
  // 创建表和索引
  db.exec(createTableSQL);
  console.log('✅ 数据库表创建成功');

  // 准备插入语句
  const insertSQL = `
    INSERT OR REPLACE INTO cities (
      id, city, cityEn, country, countryEn, flag, timezone, utcOffset,
      continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
      sunrise, sunset, population, currency, language, areaCode, elevation,
      website, established, mayor, gdp, area, density, nickname, nicknameEn,
      description, descriptionEn
    ) VALUES (
      ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
    )
  `;

  const stmt = db.prepare(insertSQL);

  // 开始事务
  const transaction = db.transaction(() => {
    let successCount = 0;
    let errorCount = 0;

    comprehensiveAsiaCities.forEach((city, index) => {
      try {
        stmt.run(
          city.id, city.city, city.cityEn, city.country, city.countryEn,
          city.flag, city.timezone, city.utcOffset, city.continent, city.continentEn,
          city.iana, city.dstStatus, city.dstStatusEn, city.latitude, city.longitude,
          city.sunrise, city.sunset, city.population, city.currency, city.language,
          city.areaCode, city.elevation, city.website, city.established, city.mayor,
          city.gdp, city.area, city.density, city.nickname, city.nicknameEn,
          city.description, city.descriptionEn
        );
        successCount++;
        console.log(`✅ ${index + 1}. ${city.city} (${city.cityEn}) - 添加成功`);
      } catch (error) {
        errorCount++;
        console.error(`❌ ${index + 1}. ${city.city} (${city.cityEn}) - 添加失败:`, error.message);
      }
    });

    return { successCount, errorCount };
  });

  // 执行事务
  const result = transaction();

  console.log('\n🎉 亚洲城市数据添加完成！');
  console.log('='.repeat(50));
  console.log(`✅ 成功添加: ${result.successCount} 个城市`);
  console.log(`❌ 添加失败: ${result.errorCount} 个城市`);
  console.log(`📊 总计处理: ${comprehensiveAsiaCities.length} 个城市`);

  // 按国家统计
  const countryStats = {};
  comprehensiveAsiaCities.forEach(city => {
    if (!countryStats[city.country]) {
      countryStats[city.country] = [];
    }
    countryStats[city.country].push(city.city);
  });

  console.log('\n📍 按国家统计:');
  console.log('-'.repeat(30));
  Object.entries(countryStats).forEach(([country, cities]) => {
    console.log(`${country}: ${cities.length} 个城市`);
    cities.forEach(city => {
      console.log(`  • ${city}`);
    });
  });

  // 验证数据库中的城市数量
  const totalCities = db.prepare('SELECT COUNT(*) as count FROM cities').get();
  console.log(`\n📊 数据库中总城市数量: ${totalCities.count}`);

  // 显示亚洲城市数量
  const asiaCities = db.prepare('SELECT COUNT(*) as count FROM cities WHERE continent = ?').get('亚洲');
  console.log(`🌏 亚洲城市数量: ${asiaCities.count}`);

} catch (error) {
  console.error('❌ 数据库操作失败:', error);
} finally {
  // 关闭数据库连接
  db.close();
  console.log('\n✅ 数据库连接已关闭');
} 