"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { Clock, Globe, MapPin } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <div
      className={cn(
        'border-4 border-blue-500 border-t-transparent rounded-full animate-spin',
        sizeClasses[size],
        className
      )}
    />
  )
}

interface SkeletonProps {
  className?: string
  children?: React.ReactNode
}

export function Skeleton({ className, children }: SkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse bg-gray-200 rounded',
        className
      )}
    >
      {children}
    </div>
  )
}

// 城市卡片骨架屏
export function CityCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-3 mb-4">
        <Skeleton className="w-8 h-8 rounded-full" />
        <div className="flex-1">
          <Skeleton className="h-4 w-24 mb-2" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
      
      <div className="text-center">
        <Skeleton className="h-8 w-32 mx-auto mb-2" />
        <Skeleton className="h-4 w-20 mx-auto mb-1" />
        <Skeleton className="h-3 w-16 mx-auto" />
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex justify-between">
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-12" />
        </div>
      </div>
    </div>
  )
}

// 页面加载状态
interface PageLoadingProps {
  message?: string
  locale?: string
}

export function PageLoading({ message, locale = 'en' }: PageLoadingProps) {
  const loadingMessages = {
    en: "Loading...",
    zh: "正在加载...",
    ja: "読み込み中...",
    ko: "로딩 중...",
    fr: "Chargement...",
    de: "Laden...",
    es: "Cargando...",
    ru: "Загрузка..."
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600 text-lg">
          {message || loadingMessages[locale as keyof typeof loadingMessages] || loadingMessages.en}
        </p>
      </div>
    </div>
  )
}

// 城市数据加载状态
export function CityDataLoading({ locale = 'en' }: { locale?: string }) {
  const loadingMessages = {
    en: "Loading city data...",
    zh: "正在加载城市数据...",
    ja: "都市データを読み込み中...",
    ko: "도시 데이터 로딩 중...",
    fr: "Chargement des données de ville...",
    de: "Stadtdaten werden geladen...",
    es: "Cargando datos de ciudad...",
    ru: "Загрузка данных города..."
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Clock className="w-8 h-8 text-blue-500 animate-pulse mr-2" />
          <Globe className="w-8 h-8 text-green-500 animate-pulse mr-2" />
          <MapPin className="w-8 h-8 text-purple-500 animate-pulse" />
        </div>
        <p className="text-gray-600">
          {loadingMessages[locale as keyof typeof loadingMessages] || loadingMessages.en}
        </p>
      </div>
    </div>
  )
}

// 搜索加载状态
export function SearchLoading() {
  return (
    <div className="flex items-center justify-center py-4">
      <LoadingSpinner size="sm" className="mr-2" />
      <span className="text-sm text-gray-500">Searching...</span>
    </div>
  )
}

// 内容加载骨架屏
export function ContentSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-3/4" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      <Skeleton className="h-4 w-4/5" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
        {Array.from({ length: 6 }).map((_, i) => (
          <CityCardSkeleton key={i} />
        ))}
      </div>
    </div>
  )
}

// 渐进式加载容器
interface ProgressiveLoadingProps {
  isLoading: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
  className?: string
}

export function ProgressiveLoading({ 
  isLoading, 
  children, 
  fallback,
  className 
}: ProgressiveLoadingProps) {
  return (
    <div className={cn('transition-all duration-300', className)}>
      {isLoading ? (
        fallback || <ContentSkeleton />
      ) : (
        <div className="animate-in fade-in duration-300">
          {children}
        </div>
      )}
    </div>
  )
}

// 懒加载容器
interface LazyLoadProps {
  children: React.ReactNode
  threshold?: number
  rootMargin?: string
  className?: string
}

export function LazyLoad({ 
  children, 
  threshold = 0.1, 
  rootMargin = '50px',
  className 
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = React.useState(false)
  const ref = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold, rootMargin }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [threshold, rootMargin])

  return (
    <div ref={ref} className={className}>
      {isVisible ? children : <Skeleton className="h-32 w-full" />}
    </div>
  )
}
