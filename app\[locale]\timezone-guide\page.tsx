import React from 'react'
import type { Metadata } from 'next'
import { Clock, Globe, MapPin, Sun, Moon, Calendar } from 'lucide-react'
import { LanguageSwitchI18n } from '@/components/LanguageSwitchI18n'
import type { Locale } from '../layout'

interface TimezoneGuidePageProps {
  params: Promise<{ locale: Locale }>
}

// 生成元数据
export async function generateMetadata({ params }: TimezoneGuidePageProps): Promise<Metadata> {
  const { locale } = await params
  
  const titles = {
    en: "Complete Timezone Guide - Understanding World Time Zones",
    zh: "时区完整指南 - 了解世界时区",
    ja: "タイムゾーン完全ガイド - 世界の時間帯を理解する",
    ko: "시간대 완전 가이드 - 세계 시간대 이해하기",
    fr: "Guide complet des fuseaux horaires - Comprendre les zones horaires mondiales",
    de: "Vollständiger Zeitzonenführer - Weltzeitzonen verstehen",
    es: "Guía completa de zonas horarias - Entender las zonas horarias mundiales",
    ru: "Полное руководство по часовым поясам - Понимание мировых часовых поясов"
  }

  const descriptions = {
    en: "Learn everything about time zones, UTC, GMT, daylight saving time, and how to calculate time differences around the world.",
    zh: "了解时区、UTC、GMT、夏令时以及如何计算世界各地时差的所有知识。",
    ja: "タイムゾーン、UTC、GMT、夏時間、世界各地の時差の計算方法について学びます。",
    ko: "시간대, UTC, GMT, 일광 절약 시간 및 전 세계 시차 계산 방법에 대한 모든 것을 배우세요.",
    fr: "Apprenez tout sur les fuseaux horaires, UTC, GMT, l'heure d'été et comment calculer les différences horaires dans le monde.",
    de: "Erfahren Sie alles über Zeitzonen, UTC, GMT, Sommerzeit und wie Sie Zeitunterschiede weltweit berechnen.",
    es: "Aprende todo sobre zonas horarias, UTC, GMT, horario de verano y cómo calcular diferencias horarias en todo el mundo.",
    ru: "Изучите все о часовых поясах, UTC, GMT, летнем времени и как рассчитывать временные различия по всему миру."
  }

  return {
    title: titles[locale] || titles.en,
    description: descriptions[locale] || descriptions.en,
    keywords: locale === 'zh' ? 
      '时区, UTC, GMT, 夏令时, 世界时间, 时差计算' :
      'timezone, UTC, GMT, daylight saving time, world time, time difference',
    openGraph: {
      title: titles[locale] || titles.en,
      description: descriptions[locale] || descriptions.en,
      type: 'article',
    }
  }
}

export default function TimezoneGuidePage({ params }: TimezoneGuidePageProps) {
  const { locale } = React.use(params)

  // 多语言内容
  const content = {
    en: {
      title: "Complete Timezone Guide",
      subtitle: "Understanding World Time Zones",
      sections: {
        basics: {
          title: "Timezone Basics",
          content: [
            "A time zone is a region of the Earth that has the same standard time. The world is divided into 24 time zones, each roughly 15 degrees of longitude wide.",
            "Time zones are based on the Earth's rotation and the position of the sun. As the Earth rotates, different parts of the world experience daylight and darkness at different times.",
            "The concept of time zones was first proposed by Sir Sandford Fleming in 1879 and was gradually adopted worldwide."
          ]
        },
        utc: {
          title: "UTC and GMT",
          content: [
            "UTC (Coordinated Universal Time) is the primary time standard by which the world regulates clocks and time. It is the successor to Greenwich Mean Time (GMT).",
            "UTC is not adjusted for daylight saving time and serves as the reference point for all other time zones.",
            "Time zones are expressed as positive or negative offsets from UTC, such as UTC+8 or UTC-5."
          ]
        },
        dst: {
          title: "Daylight Saving Time",
          content: [
            "Daylight Saving Time (DST) is the practice of moving clocks forward by one hour during warmer months to extend evening daylight.",
            "Not all countries observe DST, and those that do may start and end it on different dates.",
            "DST can affect international scheduling and communication, making it important to be aware of when different regions observe it."
          ]
        },
        calculation: {
          title: "Calculating Time Differences",
          content: [
            "To calculate the time difference between two locations, subtract the UTC offset of the first location from the second.",
            "For example, if it's 12:00 PM UTC+8 (Beijing), it would be 4:00 AM UTC+0 (London) on the same day.",
            "Remember to account for daylight saving time when applicable, as it can change the effective time difference."
          ]
        }
      },
      tips: {
        title: "Practical Tips",
        items: [
          "Use world clock apps or websites for accurate time zone conversions",
          "When scheduling international meetings, consider all participants' time zones",
          "Be aware of daylight saving time changes in different countries",
          "Use UTC time for international coordination and documentation",
          "Remember that some countries have multiple time zones within their borders"
        ]
      }
    },
    zh: {
      title: "时区完整指南",
      subtitle: "了解世界时区",
      sections: {
        basics: {
          title: "时区基础",
          content: [
            "时区是地球上具有相同标准时间的区域。世界被分为24个时区，每个时区大约跨越15度经度。",
            "时区基于地球的自转和太阳的位置。随着地球自转，世界不同地区在不同时间经历白天和黑夜。",
            "时区概念最初由桑德福德·弗莱明爵士在1879年提出，并逐渐被全世界采用。"
          ]
        },
        utc: {
          title: "UTC和GMT",
          content: [
            "UTC（协调世界时）是世界调节时钟和时间的主要时间标准。它是格林威治标准时间（GMT）的继承者。",
            "UTC不会因夏令时而调整，作为所有其他时区的参考点。",
            "时区表示为相对于UTC的正负偏移量，如UTC+8或UTC-5。"
          ]
        },
        dst: {
          title: "夏令时",
          content: [
            "夏令时（DST）是在温暖月份将时钟向前调整一小时以延长傍晚日光的做法。",
            "并非所有国家都实行夏令时，实行夏令时的国家可能在不同日期开始和结束。",
            "夏令时可能影响国际日程安排和沟通，因此了解不同地区何时实行夏令时很重要。"
          ]
        },
        calculation: {
          title: "计算时差",
          content: [
            "要计算两个地点之间的时差，用第二个地点的UTC偏移量减去第一个地点的UTC偏移量。",
            "例如，如果北京时间（UTC+8）是下午12:00，那么伦敦时间（UTC+0）就是同一天的凌晨4:00。",
            "记住在适用时要考虑夏令时，因为它可能改变有效时差。"
          ]
        }
      },
      tips: {
        title: "实用技巧",
        items: [
          "使用世界时钟应用或网站进行准确的时区转换",
          "安排国际会议时，考虑所有参与者的时区",
          "注意不同国家的夏令时变化",
          "使用UTC时间进行国际协调和文档记录",
          "记住一些国家在其境内有多个时区"
        ]
      }
    },
    ja: {
      title: "タイムゾーン完全ガイド",
      subtitle: "世界の時間帯を理解する",
      sections: {
        basics: {
          title: "タイムゾーンの基礎",
          content: [
            "タイムゾーンは、同じ標準時を持つ地球上の地域です。世界は24のタイムゾーンに分かれており、それぞれ約15度の経度幅があります。",
            "タイムゾーンは地球の自転と太陽の位置に基づいています。地球が自転するにつれて、世界の異なる部分が異なる時間に昼と夜を経験します。",
            "タイムゾーンの概念は1879年にサンドフォード・フレミング卿によって最初に提案され、徐々に世界中で採用されました。"
          ]
        },
        utc: {
          title: "UTCとGMT",
          content: [
            "UTC（協定世界時）は、世界が時計と時間を調整する主要な時間標準です。これはグリニッジ標準時（GMT）の後継です。",
            "UTCは夏時間に調整されず、他のすべてのタイムゾーンの基準点として機能します。",
            "タイムゾーンはUTCからの正または負のオフセットとして表現されます（UTC+8やUTC-5など）。"
          ]
        },
        dst: {
          title: "夏時間",
          content: [
            "夏時間（DST）は、暖かい月に時計を1時間進めて夕方の日光を延長する慣行です。",
            "すべての国が夏時間を実施しているわけではなく、実施している国でも開始と終了の日付が異なる場合があります。",
            "夏時間は国際的なスケジューリングとコミュニケーションに影響を与える可能性があるため、異なる地域がいつ実施するかを知ることが重要です。"
          ]
        },
        calculation: {
          title: "時差の計算",
          content: [
            "2つの場所間の時差を計算するには、2番目の場所のUTCオフセットから最初の場所のUTCオフセットを引きます。",
            "例えば、北京時間（UTC+8）が午後12:00の場合、ロンドン時間（UTC+0）は同じ日の午前4:00になります。",
            "適用される場合は夏時間を考慮することを忘れないでください。これは実効時差を変更する可能性があります。"
          ]
        }
      },
      tips: {
        title: "実用的なヒント",
        items: [
          "正確なタイムゾーン変換には世界時計アプリやウェブサイトを使用する",
          "国際会議をスケジュールする際は、すべての参加者のタイムゾーンを考慮する",
          "異なる国の夏時間の変更に注意する",
          "国際的な調整と文書化にはUTC時間を使用する",
          "一部の国では国境内に複数のタイムゾーンがあることを覚えておく"
        ]
      }
    },
    ko: {
      title: "시간대 완전 가이드",
      subtitle: "세계 시간대 이해하기",
      sections: {
        basics: {
          title: "시간대 기초",
          content: [
            "시간대는 동일한 표준 시간을 가진 지구상의 지역입니다. 세계는 24개의 시간대로 나뉘며, 각각 약 15도의 경도 폭을 가집니다.",
            "시간대는 지구의 자전과 태양의 위치를 기반으로 합니다. 지구가 자전하면서 세계의 다른 부분들이 서로 다른 시간에 낮과 밤을 경험합니다.",
            "시간대 개념은 1879년 샌드포드 플레밍 경에 의해 처음 제안되었고 점차 전 세계적으로 채택되었습니다."
          ]
        },
        utc: {
          title: "UTC와 GMT",
          content: [
            "UTC(협정 세계시)는 세계가 시계와 시간을 조절하는 주요 시간 표준입니다. 이는 그리니치 표준시(GMT)의 후계자입니다.",
            "UTC는 일광 절약 시간에 맞춰 조정되지 않으며 다른 모든 시간대의 기준점 역할을 합니다.",
            "시간대는 UTC+8 또는 UTC-5와 같이 UTC로부터의 양수 또는 음수 오프셋으로 표현됩니다."
          ]
        },
        dst: {
          title: "일광 절약 시간",
          content: [
            "일광 절약 시간(DST)은 따뜻한 달에 시계를 한 시간 앞당겨 저녁 일광을 연장하는 관행입니다.",
            "모든 국가가 일광 절약 시간을 시행하는 것은 아니며, 시행하는 국가들도 시작과 종료 날짜가 다를 수 있습니다.",
            "일광 절약 시간은 국제적인 일정 계획과 의사소통에 영향을 줄 수 있으므로 다른 지역이 언제 시행하는지 아는 것이 중요합니다."
          ]
        },
        calculation: {
          title: "시차 계산",
          content: [
            "두 지역 간의 시차를 계산하려면 두 번째 지역의 UTC 오프셋에서 첫 번째 지역의 UTC 오프셋을 빼면 됩니다.",
            "예를 들어, 베이징 시간(UTC+8)이 오후 12:00이면 런던 시간(UTC+0)은 같은 날 오전 4:00입니다.",
            "해당되는 경우 일광 절약 시간을 고려하는 것을 잊지 마세요. 이는 실제 시차를 변경할 수 있습니다."
          ]
        }
      },
      tips: {
        title: "실용적인 팁",
        items: [
          "정확한 시간대 변환을 위해 세계 시계 앱이나 웹사이트 사용",
          "국제 회의를 예약할 때 모든 참가자의 시간대 고려",
          "다른 국가의 일광 절약 시간 변경 사항 주의",
          "국제 조정 및 문서화에는 UTC 시간 사용",
          "일부 국가는 국경 내에 여러 시간대가 있다는 것을 기억"
        ]
      }
    }
  }

  const t = content[locale] || content.en

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-800">
                  WorldTimeApp
                </h1>
              </div>
            </div>
            <LanguageSwitchI18n />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Globe className="w-12 h-12 text-blue-600" />
            <Clock className="w-12 h-12 text-green-600" />
            <MapPin className="w-12 h-12 text-purple-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {t.title}
          </h1>
          <p className="text-xl text-gray-600">
            {t.subtitle}
          </p>
        </div>

        {/* Content Sections */}
        <div className="space-y-12">
          {/* Timezone Basics */}
          <section className="bg-white rounded-lg shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <Globe className="w-8 h-8 text-blue-600" />
              <h2 className="text-2xl font-bold text-gray-800">
                {t.sections.basics.title}
              </h2>
            </div>
            <div className="space-y-4">
              {t.sections.basics.content.map((paragraph, index) => (
                <p key={index} className="text-gray-700 leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </section>

          {/* UTC and GMT */}
          <section className="bg-white rounded-lg shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <Clock className="w-8 h-8 text-green-600" />
              <h2 className="text-2xl font-bold text-gray-800">
                {t.sections.utc.title}
              </h2>
            </div>
            <div className="space-y-4">
              {t.sections.utc.content.map((paragraph, index) => (
                <p key={index} className="text-gray-700 leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </section>

          {/* Daylight Saving Time */}
          <section className="bg-white rounded-lg shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <Sun className="w-8 h-8 text-yellow-600" />
              <h2 className="text-2xl font-bold text-gray-800">
                {t.sections.dst.title}
              </h2>
            </div>
            <div className="space-y-4">
              {t.sections.dst.content.map((paragraph, index) => (
                <p key={index} className="text-gray-700 leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </section>

          {/* Calculating Time Differences */}
          <section className="bg-white rounded-lg shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <Calendar className="w-8 h-8 text-purple-600" />
              <h2 className="text-2xl font-bold text-gray-800">
                {t.sections.calculation.title}
              </h2>
            </div>
            <div className="space-y-4">
              {t.sections.calculation.content.map((paragraph, index) => (
                <p key={index} className="text-gray-700 leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>
          </section>

          {/* Practical Tips */}
          <section className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <Moon className="w-8 h-8 text-indigo-600" />
              <h2 className="text-2xl font-bold text-gray-800">
                {t.tips.title}
              </h2>
            </div>
            <ul className="space-y-3">
              {t.tips.items.map((tip, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-gray-700 leading-relaxed">{tip}</span>
                </li>
              ))}
            </ul>
          </section>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12 p-8 bg-white rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            {locale === 'zh' ? '开始探索世界时间' :
             locale === 'ja' ? '世界時間の探索を始める' :
             locale === 'ko' ? '세계 시간 탐색 시작' :
             'Start Exploring World Time'}
          </h3>
          <p className="text-gray-600 mb-6">
            {locale === 'zh' ? '使用我们的世界时钟工具查看全球各地的当前时间' :
             locale === 'ja' ? '私たちの世界時計ツールを使用して世界中の現在時刻を確認' :
             locale === 'ko' ? '우리의 세계 시계 도구를 사용하여 전 세계의 현재 시간 확인' :
             'Use our world clock tool to check current time around the globe'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href={`/${locale}`}
              className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Clock className="w-5 h-5 mr-2" />
              {locale === 'zh' ? '查看世界时钟' :
               locale === 'ja' ? '世界時計を見る' :
               locale === 'ko' ? '세계 시계 보기' :
               'View World Clock'}
            </a>
            <a
              href={`/${locale}/search`}
              className="inline-flex items-center justify-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <MapPin className="w-5 h-5 mr-2" />
              {locale === 'zh' ? '搜索城市' :
               locale === 'ja' ? '都市を検索' :
               locale === 'ko' ? '도시 검색' :
               'Search Cities'}
            </a>
          </div>
        </div>
      </main>
    </div>
  )
}
