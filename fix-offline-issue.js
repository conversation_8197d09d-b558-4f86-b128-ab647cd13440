·#!/usr/bin/env node

/**
 * 修复 Service Worker 离线问题的脚本
 */

console.log('🔧 修复 Service Worker 离线问题...\n');

console.log('📋 解决方案：');
console.log('');

console.log('方法1: 清除浏览器 Service Worker');
console.log('1. 打开浏览器开发者工具 (F12)');
console.log('2. 进入 Application 标签');
console.log('3. 找到 Service Workers');
console.log('4. 点击 "Unregister" 注销 Service Worker');
console.log('5. 刷新页面');
console.log('');

console.log('方法2: 使用无痕模式');
console.log('1. 打开无痕窗口 (Ctrl + Shift + N)');
console.log('2. 访问 http://localhost:3000');
console.log('');

console.log('方法3: 清除浏览器数据');
console.log('1. 按 Ctrl + Shift + Delete');
console.log('2. 选择清除缓存和 Cookie');
console.log('3. 刷新页面');
console.log('');

console.log('方法4: 在控制台运行以下代码');
console.log('```javascript');
console.log('navigator.serviceWorker.getRegistrations().then(function(registrations) {');
console.log('  for(let registration of registrations) {');
console.log('    registration.unregister();');
console.log('    console.log("SW unregistered");');
console.log('  }');
console.log('  location.reload();');
console.log('});');
console.log('```');
console.log('');

console.log('✅ 已更新代码：开发环境下自动清除 Service Worker');
console.log('💡 重启开发服务器后问题应该解决');
console.log('');

console.log('🚀 如果问题仍然存在，请尝试：');
console.log('1. 完全关闭浏览器');
console.log('2. 重新打开浏览器');
console.log('3. 访问 http://localhost:3000');
