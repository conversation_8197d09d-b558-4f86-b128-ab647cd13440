#!/usr/bin/env node

/**
 * 探索 @playwright/mcp 的功能
 */

console.log('🔍 探索 @playwright/mcp 功能...\n');

async function explorePlaywrightMCP() {
  try {
    console.log('📦 导入 @playwright/mcp...');
    const playwrightMcp = await import('@playwright/mcp');
    
    console.log('✅ 导入成功！');
    console.log('📋 可用导出:', Object.keys(playwrightMcp));
    
    // 检查 createConnection 函数
    if (playwrightMcp.createConnection) {
      console.log('🔧 找到 createConnection 函数');
      console.log('📝 函数类型:', typeof playwrightMcp.createConnection);
      
      try {
        console.log('🚀 尝试调用 createConnection...');
        
        // 尝试创建连接
        const connection = await playwrightMcp.createConnection();
        console.log('✅ 连接创建成功:', connection);
        
        // 探索连接对象的方法
        if (connection && typeof connection === 'object') {
          console.log('📋 连接对象方法:', Object.keys(connection));
        }
        
      } catch (error) {
        console.log('❌ createConnection 调用失败:', error.message);
        console.log('💡 可能需要特定的参数或配置');
      }
    }
    
    // 检查其他可能的导出
    for (const [key, value] of Object.entries(playwrightMcp)) {
      console.log(`🔍 ${key}: ${typeof value}`);
      
      if (typeof value === 'function' && key !== 'createConnection') {
        console.log(`  📝 函数 ${key} 的长度: ${value.length} 个参数`);
      }
    }
    
  } catch (error) {
    console.error('❌ 探索失败:', error.message);
    console.error('📋 错误详情:', error);
  }
}

async function testWithBaidu() {
  console.log('\n🌐 尝试将 @playwright/mcp 用于百度测试...');
  
  try {
    const playwrightMcp = await import('@playwright/mcp');
    
    if (playwrightMcp.createConnection) {
      console.log('🔧 尝试为百度测试创建 MCP 连接...');
      
      // 尝试不同的参数组合
      const testConfigs = [
        {},
        { url: 'https://www.baidu.com' },
        { browser: 'chromium' },
        { headless: false }
      ];
      
      for (const config of testConfigs) {
        try {
          console.log(`🧪 测试配置:`, config);
          const connection = await playwrightMcp.createConnection(config);
          console.log('✅ 配置成功:', config);
          
          // 如果成功，尝试使用连接
          if (connection) {
            console.log('🎯 连接创建成功，尝试使用...');
            // 这里可以添加具体的使用逻辑
          }
          
          break; // 如果成功就退出循环
          
        } catch (error) {
          console.log(`❌ 配置失败:`, config, error.message);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 百度测试失败:', error.message);
  }
}

async function checkMCPDocumentation() {
  console.log('\n📚 检查 @playwright/mcp 包信息...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    const packageJsonPath = path.join(process.cwd(), 'node_modules', '@playwright', 'mcp', 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      console.log('📦 包信息:');
      console.log('  - 名称:', packageJson.name);
      console.log('  - 版本:', packageJson.version);
      console.log('  - 描述:', packageJson.description);
      console.log('  - 主入口:', packageJson.main);
      console.log('  - 类型:', packageJson.type);
      
      if (packageJson.scripts) {
        console.log('  - 脚本:', Object.keys(packageJson.scripts));
      }
      
      if (packageJson.dependencies) {
        console.log('  - 依赖:', Object.keys(packageJson.dependencies));
      }
    }
    
    // 检查是否有 README 或其他文档
    const readmePath = path.join(process.cwd(), 'node_modules', '@playwright', 'mcp', 'README.md');
    if (fs.existsSync(readmePath)) {
      console.log('📖 找到 README.md 文件');
      const readme = fs.readFileSync(readmePath, 'utf8');
      console.log('📄 README 内容预览:');
      console.log(readme.substring(0, 500) + '...');
    }
    
  } catch (error) {
    console.error('❌ 检查包信息失败:', error.message);
  }
}

// 主函数
async function main() {
  await explorePlaywrightMCP();
  await testWithBaidu();
  await checkMCPDocumentation();
  
  console.log('\n🎉 探索完成！');
}

// 运行探索
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { explorePlaywrightMCP };
