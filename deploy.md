# 部署方案

## 1. Vercel 部署（推荐 - 免费）

### 方法一：GitHub 自动部署
1. 将代码推送到 GitHub 仓库
2. 访问 [vercel.com](https://vercel.com)
3. 使用 GitHub 账号登录
4. 点击 "New Project"
5. 选择你的 GitHub 仓库
6. 设置自定义域名为 `worldtimeapp.online`
7. 点击 "Deploy"

### 方法二：CLI 部署
```bash
# 1. 登录 Vercel
vercel login

# 2. 部署项目
vercel

# 3. 设置生产环境
vercel --prod

# 4. 绑定自定义域名
vercel domains add worldtimeapp.online
```

## 2. Netlify 部署（免费）

### 步骤：
1. 访问 [netlify.com](https://netlify.com)
2. 连接 GitHub 仓库
3. 设置构建命令：`npm run build`
4. 设置发布目录：`.next`
5. 添加自定义域名 `worldtimeapp.online`

## 3. Railway 部署（简单）

### 步骤：
1. 访问 [railway.app](https://railway.app)
2. 连接 GitHub 仓库
3. 自动检测 Next.js 项目
4. 设置环境变量（如需要）
5. 绑定自定义域名

## 4. 云服务器部署（VPS/云主机）

### 准备工作：
```bash
# 1. 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. 安装 PM2（进程管理器）
npm install -g pm2

# 3. 安装 Nginx（反向代理）
sudo apt update
sudo apt install nginx
```

### 部署步骤：
```bash
# 1. 克隆代码
git clone <your-repo-url>
cd timecha-clone

# 2. 安装依赖
npm install

# 3. 构建项目
npm run build

# 4. 启动应用
pm2 start npm --name "worldtimeapp" -- start

# 5. 设置开机自启
pm2 startup
pm2 save
```

### Nginx 配置：
```nginx
server {
    listen 80;
    server_name worldtimeapp.online www.worldtimeapp.online;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 5. Docker 部署

### Dockerfile：
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 部署命令：
```bash
# 1. 构建镜像
docker build -t worldtimeapp .

# 2. 运行容器
docker run -d -p 3000:3000 --name worldtimeapp worldtimeapp

# 3. 使用 docker-compose
docker-compose up -d
```

## 推荐方案对比

| 方案 | 成本 | 难度 | 性能 | 自定义域名 | 推荐指数 |
|------|------|------|------|------------|----------|
| Vercel | 免费 | ⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐⭐ |
| Netlify | 免费 | ⭐⭐ | ⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐ |
| Railway | 免费额度 | ⭐⭐ | ⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐ |
| 云服务器 | $5-20/月 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐ |
| Docker | 服务器成本 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐ |

## 域名配置

无论选择哪种方案，都需要将域名 `worldtimeapp.online` 的 DNS 记录指向部署平台：

1. **Vercel**: 添加 CNAME 记录指向 `cname.vercel-dns.com`
2. **Netlify**: 添加 CNAME 记录指向 `<site-name>.netlify.app`
3. **Railway**: 添加 CNAME 记录指向提供的域名
4. **自建服务器**: 添加 A 记录指向服务器 IP

## 最快部署方案

**推荐使用 Vercel**：
1. 2分钟内完成部署
2. 自动 HTTPS
3. 全球 CDN 加速
4. 免费自定义域名
5. 自动构建和部署 