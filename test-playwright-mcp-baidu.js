#!/usr/bin/env node

/**
 * 使用 @playwright/mcp 测试百度网站
 * 
 * 这个脚本尝试使用 @playwright/mcp 包来测试百度网站
 */

console.log('🔧 尝试使用 @playwright/mcp 测试百度...\n');

async function testPlaywrightMCP() {
  try {
    console.log('📦 尝试导入 @playwright/mcp...');
    
    // 尝试导入 @playwright/mcp
    const playwrightMcp = await import('@playwright/mcp');
    console.log('✅ @playwright/mcp 导入成功');
    console.log('📋 可用方法:', Object.keys(playwrightMcp));
    
    // 如果导入成功，尝试使用它
    if (playwrightMcp.default) {
      console.log('🚀 尝试使用 @playwright/mcp 功能...');
      // 这里可以添加具体的 MCP 使用逻辑
    }
    
  } catch (error) {
    console.error('❌ @playwright/mcp 导入失败:', error.message);
    console.log('\n🔄 回退到标准 Playwright 方案...');
    
    // 回退到标准 Playwright
    await fallbackToStandardPlaywright();
  }
}

async function fallbackToStandardPlaywright() {
  try {
    console.log('📦 使用标准 Playwright...');
    const { chromium } = require('playwright');
    
    const browser = await chromium.launch({ 
      headless: false,
      slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    console.log('🌐 访问百度首页...');
    await page.goto('https://www.baidu.com');
    await page.waitForLoadState('networkidle');
    
    const title = await page.title();
    console.log('📝 页面标题:', title);
    
    // 截图
    await page.screenshot({ path: 'baidu-mcp-test.png' });
    console.log('📸 已保存截图: baidu-mcp-test.png');
    
    // 搜索测试
    const searchInput = await page.$('#kw');
    if (searchInput) {
      console.log('🔍 进行搜索测试...');
      await searchInput.fill('playwright mcp 测试');
      
      const searchButton = await page.$('#su');
      if (searchButton) {
        await searchButton.click();
        await page.waitForTimeout(3000);
        
        const results = await page.$$('.result');
        console.log(`📊 找到 ${results.length} 个搜索结果`);
        
        await page.screenshot({ path: 'baidu-mcp-search.png' });
        console.log('📸 已保存搜索结果截图: baidu-mcp-search.png');
      }
    }
    
    console.log('✅ 标准 Playwright 测试完成');
    
    // 保持浏览器打开
    console.log('\n🔍 浏览器将保持打开状态，按 Ctrl+C 退出');
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 标准 Playwright 测试失败:', error.message);
  }
}

// 测试依赖导入
async function testDependencies() {
  console.log('🔍 检查依赖状态...');
  
  // 测试 @modelcontextprotocol/sdk
  try {
    const sdk = await import('@modelcontextprotocol/sdk');
    console.log('✅ @modelcontextprotocol/sdk 导入成功');
  } catch (error) {
    console.log('❌ @modelcontextprotocol/sdk 导入失败:', error.message);
  }
  
  // 测试 server/sse.js
  try {
    const sse = await import('@modelcontextprotocol/sdk/server/sse.js');
    console.log('✅ server/sse.js 导入成功');
  } catch (error) {
    console.log('❌ server/sse.js 导入失败:', error.message);
  }
  
  // 测试 zod
  try {
    const zod = await import('zod');
    console.log('✅ zod 导入成功');
  } catch (error) {
    console.log('❌ zod 导入失败:', error.message);
  }
  
  console.log('');
}

// 主函数
async function main() {
  console.log('🎯 开始 Playwright MCP 测试...\n');
  
  // 首先测试依赖
  await testDependencies();
  
  // 然后尝试使用 MCP
  await testPlaywrightMCP();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testPlaywrightMCP, fallbackToStandardPlaywright };
