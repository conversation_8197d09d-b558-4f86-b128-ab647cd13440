#!/usr/bin/env node

/**
 * 正确使用 @playwright/mcp 测试百度网站
 * 
 * 基于调试结果，使用正确的工具结构和连接方式
 */

console.log('🚀 正确使用 @playwright/mcp 测试百度...\n');

async function testBaiduWithMCPCorrectly() {
  try {
    const playwrightMcp = await import('@playwright/mcp');
    const connection = await playwrightMcp.createConnection();
    
    console.log('✅ MCP 连接创建成功');
    
    // 显示所有可用工具
    console.log('\n🛠️  可用工具列表:');
    connection.context.tools.forEach((tool, index) => {
      const schema = tool.schema;
      console.log(`  ${index + 1}. ${schema.name}: ${schema.description}`);
    });
    
    // 查找导航工具
    const navigateTool = connection.context.tools.find(tool => 
      tool.schema.name.includes('navigate') || 
      tool.schema.name.includes('goto') ||
      tool.schema.name.includes('url')
    );
    
    if (navigateTool) {
      console.log('\n🧭 找到导航工具:', navigateTool.schema.name);
      console.log('📝 描述:', navigateTool.schema.description);
      
      try {
        console.log('🚀 导航到百度首页...');
        
        // 直接调用工具的 handle 方法
        const result = await navigateTool.handle({
          url: 'https://www.baidu.com'
        });
        
        console.log('✅ 导航成功:', result);
        
      } catch (error) {
        console.log('❌ 导航失败:', error.message);
      }
    } else {
      console.log('❌ 未找到导航工具');
      
      // 显示所有工具名称以便调试
      console.log('📋 所有工具名称:');
      connection.context.tools.forEach(tool => {
        console.log(`  - ${tool.schema.name}`);
      });
    }
    
    // 查找页面内容获取工具
    const contentTool = connection.context.tools.find(tool => 
      tool.schema.name.includes('content') || 
      tool.schema.name.includes('snapshot') ||
      tool.schema.name.includes('text')
    );
    
    if (contentTool) {
      console.log('\n📄 找到内容工具:', contentTool.schema.name);
      
      try {
        const result = await contentTool.handle({});
        console.log('✅ 获取内容成功');
        console.log('📋 内容类型:', typeof result);
        
        if (typeof result === 'string') {
          console.log('📄 内容预览:', result.substring(0, 300) + '...');
        } else {
          console.log('📋 内容对象:', result);
        }
        
      } catch (error) {
        console.log('❌ 获取内容失败:', error.message);
      }
    }
    
    // 查找截图工具
    const screenshotTool = connection.context.tools.find(tool => 
      tool.schema.name.includes('screenshot') || 
      tool.schema.name.includes('capture')
    );
    
    if (screenshotTool) {
      console.log('\n📸 找到截图工具:', screenshotTool.schema.name);
      
      try {
        const result = await screenshotTool.handle({});
        console.log('✅ 截图成功:', result);
        
      } catch (error) {
        console.log('❌ 截图失败:', error.message);
      }
    }
    
    // 尝试一些基本操作
    console.log('\n🔍 尝试基本浏览器操作...');
    
    // 查找并尝试调用一些基本工具
    const basicTools = [
      'browser_resize',
      'browser_console_messages',
      'page_title',
      'page_url'
    ];
    
    for (const toolName of basicTools) {
      const tool = connection.context.tools.find(t => t.schema.name === toolName);
      if (tool) {
        console.log(`\n🛠️  尝试调用 ${toolName}...`);
        try {
          const result = await tool.handle({});
          console.log(`✅ ${toolName} 成功:`, result);
        } catch (error) {
          console.log(`❌ ${toolName} 失败:`, error.message);
        }
      }
    }
    
    console.log('\n🎉 @playwright/mcp 测试完成！');
    
  } catch (error) {
    console.error('❌ MCP 测试失败:', error.message);
    console.error('📋 错误详情:', error);
  }
}

// 运行测试
if (require.main === module) {
  testBaiduWithMCPCorrectly().catch(console.error);
}

module.exports = { testBaiduWithMCPCorrectly };
