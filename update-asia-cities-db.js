// 更新亚洲城市数据库脚本
const asiaCities = require('./add-asia-cities-simple.js');

// 生成完整的城市数据对象
function generateFullCityData(city) {
  return {
    id: city.id,
    city: city.city,
    cityEn: city.cityEn,
    country: city.country,
    countryEn: city.countryEn,
    flag: city.flag,
    timezone: city.timezone,
    utcOffset: city.utcOffset,
    continent: city.continent,
    continentEn: city.continentEn,
    iana: city.timezone,
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: city.latitude,
    longitude: city.longitude,
    sunrise: '06:30',
    sunset: '18:30',
    population: city.population,
    currency: city.currency,
    language: city.countryEn === 'China' ? 'Chinese' : 
              city.countryEn === 'Japan' ? 'Japanese' :
              city.countryEn === 'South Korea' ? 'Korean' :
              city.countryEn === 'India' ? 'Hindi, English' : 'English',
    areaCode: city.countryEn === 'China' ? '+86' :
              city.countryEn === 'Japan' ? '+81' :
              city.countryEn === 'South Korea' ? '+82' :
              city.countryEn === 'India' ? '+91' : '+1',
    elevation: 50,
    website: `https://www.${city.id}.gov`,
    established: '1000',
    mayor: 'City Mayor',
    gdp: city.population * 50000,
    area: 1000,
    density: Math.round(city.population / 1000),
    nickname: city.nickname,
    nicknameEn: city.nicknameEn,
    description: `${city.country}重要城市，${city.nickname}。`,
    descriptionEn: `An important city in ${city.countryEn}, known as ${city.nicknameEn}.`
  };
}

// 更新 SQLite 服务中的城市数据
function updateSQLiteService() {
  const fullCities = asiaCities.map(generateFullCityData);
  
  console.log('🌏 亚洲城市数据扩展');
  console.log('==================');
  console.log(`准备添加 ${fullCities.length} 个亚洲城市到数据库`);
  console.log();
  
  // 按国家分组显示
  const countryGroups = {};
  fullCities.forEach(city => {
    if (!countryGroups[city.country]) {
      countryGroups[city.country] = [];
    }
    countryGroups[city.country].push(city);
  });
  
  Object.entries(countryGroups).forEach(([country, cities]) => {
    console.log(`${country} (${cities.length} 个城市):`);
    cities.forEach(city => {
      console.log(`  • ${city.city} (${city.cityEn})`);
    });
    console.log();
  });
  
  // 生成 SQL 插入语句
  let sqlStatements = '';
  fullCities.forEach(city => {
    sqlStatements += `
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  '${city.id}', '${city.city}', '${city.cityEn}', '${city.country}', '${city.countryEn}', 
  '${city.flag}', '${city.timezone}', '${city.utcOffset}', '${city.continent}', '${city.continentEn}',
  '${city.iana}', '${city.dstStatus}', '${city.dstStatusEn}', ${city.latitude}, ${city.longitude},
  '${city.sunrise}', '${city.sunset}', ${city.population}, '${city.currency}', '${city.language}',
  '${city.areaCode}', ${city.elevation}, '${city.website}', '${city.established}', '${city.mayor}',
  ${city.gdp}, ${city.area}, ${city.density}, '${city.nickname}', '${city.nicknameEn}', 
  '${city.description}', '${city.descriptionEn}'
);`;
  });
  
  // 保存 SQL 文件
  const fs = require('fs');
  fs.writeFileSync('asia-cities-insert.sql', sqlStatements, 'utf8');
  
  console.log('✅ SQL 插入语句已生成：asia-cities-insert.sql');
  console.log('📝 接下来的步骤：');
  console.log('1. 运行 node update-asia-cities-db.js');
  console.log('2. 重启开发服务器');
  console.log('3. 查看新增的亚洲城市');
  
  return fullCities;
}

// 运行更新
if (require.main === module) {
  updateSQLiteService();
}

module.exports = { updateSQLiteService, generateFullCityData }; 