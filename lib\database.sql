-- 创建城市表
CREATE TABLE cities (
    id TEXT PRIMARY KEY,
    city TEXT NOT NULL,
    city_en TEXT NOT NULL,
    country TEXT NOT NULL,
    country_en TEXT NOT NULL,
    flag TEXT NOT NULL,
    timezone TEXT NOT NULL,
    utc_offset TEXT NOT NULL,
    continent TEXT NOT NULL,
    continent_en TEXT NOT NULL,
    iana TEXT NOT NULL,
    dst_status TEXT NOT NULL,
    dst_status_en TEXT NOT NULL,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    sunrise TEXT NOT NULL,
    sunset TEXT NOT NULL,
    population INTEGER,
    area_code TEXT,
    currency TEXT,
    language TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_cities_city ON cities(city);
CREATE INDEX idx_cities_city_en ON cities(city_en);
CREATE INDEX idx_cities_country ON cities(country);
CREATE INDEX idx_cities_timezone ON cities(timezone);
CREATE INDEX idx_cities_continent ON cities(continent);

-- 创建用户收藏表（未来扩展）
CREATE TABLE user_favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    city_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (city_id) REFERENCES cities(id)
);

-- 创建用户自定义城市表（未来扩展）
CREATE TABLE custom_cities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    timezone TEXT NOT NULL,
    latitude REAL,
    longitude REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入更多城市数据
INSERT INTO cities VALUES
-- 中国城市
('beijing', '北京', 'Beijing', '中国', 'China', '🇨🇳', 'Asia/Shanghai', 'UTC+8', '亚洲', 'Asia', 'Asia/Shanghai', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 39.9042, 116.4074, '06:30', '18:30', 21540000, '+86-10', 'CNY', 'zh-CN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('shanghai', '上海', 'Shanghai', '中国', 'China', '🇨🇳', 'Asia/Shanghai', 'UTC+8', '亚洲', 'Asia', 'Asia/Shanghai', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 31.2304, 121.4737, '06:15', '18:15', 24870000, '+86-21', 'CNY', 'zh-CN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('guangzhou', '广州', 'Guangzhou', '中国', 'China', '🇨🇳', 'Asia/Shanghai', 'UTC+8', '亚洲', 'Asia', 'Asia/Shanghai', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 23.1291, 113.2644, '06:45', '18:45', 15300000, '+86-20', 'CNY', 'zh-CN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('shenzhen', '深圳', 'Shenzhen', '中国', 'China', '🇨🇳', 'Asia/Shanghai', 'UTC+8', '亚洲', 'Asia', 'Asia/Shanghai', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 22.5431, 114.0579, '06:50', '18:50', 12590000, '+86-755', 'CNY', 'zh-CN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('chengdu', '成都', 'Chengdu', '中国', 'China', '🇨🇳', 'Asia/Shanghai', 'UTC+8', '亚洲', 'Asia', 'Asia/Shanghai', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 30.5728, 104.0668, '07:00', '19:00', 16330000, '+86-28', 'CNY', 'zh-CN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 美国城市
('new-york', '纽约', 'New York', '美国', 'United States', '🇺🇸', 'America/New_York', 'UTC-5', '北美洲', 'North America', 'America/New_York', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 40.7128, -74.0060, '07:15', '17:30', 8380000, '+1', 'USD', 'en-US', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('los-angeles', '洛杉矶', 'Los Angeles', '美国', 'United States', '🇺🇸', 'America/Los_Angeles', 'UTC-8', '北美洲', 'North America', 'America/Los_Angeles', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 34.0522, -118.2437, '07:00', '17:00', 3970000, '+1', 'USD', 'en-US', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('chicago', '芝加哥', 'Chicago', '美国', 'United States', '🇺🇸', 'America/Chicago', 'UTC-6', '北美洲', 'North America', 'America/Chicago', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 41.8781, -87.6298, '07:30', '17:15', 2710000, '+1', 'USD', 'en-US', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('miami', '迈阿密', 'Miami', '美国', 'United States', '🇺🇸', 'America/New_York', 'UTC-5', '北美洲', 'North America', 'America/New_York', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 25.7617, -80.1918, '07:00', '18:00', 470000, '+1', 'USD', 'en-US', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('san-francisco', '旧金山', 'San Francisco', '美国', 'United States', '🇺🇸', 'America/Los_Angeles', 'UTC-8', '北美洲', 'North America', 'America/Los_Angeles', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 37.7749, -122.4194, '07:30', '17:30', 870000, '+1', 'USD', 'en-US', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 欧洲城市
('london', '伦敦', 'London', '英国', 'United Kingdom', '🇬🇧', 'Europe/London', 'UTC+0', '欧洲', 'Europe', 'Europe/London', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 51.5074, -0.1278, '08:00', '16:00', 9000000, '+44', 'GBP', 'en-GB', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('paris', '巴黎', 'Paris', '法国', 'France', '🇫🇷', 'Europe/Paris', 'UTC+1', '欧洲', 'Europe', 'Europe/Paris', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 48.8566, 2.3522, '08:30', '17:30', 2165000, '+33', 'EUR', 'fr-FR', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('berlin', '柏林', 'Berlin', '德国', 'Germany', '🇩🇪', 'Europe/Berlin', 'UTC+1', '欧洲', 'Europe', 'Europe/Berlin', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 52.5200, 13.4050, '08:15', '16:30', 3670000, '+49', 'EUR', 'de-DE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('rome', '罗马', 'Rome', '意大利', 'Italy', '🇮🇹', 'Europe/Rome', 'UTC+1', '欧洲', 'Europe', 'Europe/Rome', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 41.9028, 12.4964, '07:30', '17:00', 2870000, '+39', 'EUR', 'it-IT', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('madrid', '马德里', 'Madrid', '西班牙', 'Spain', '🇪🇸', 'Europe/Madrid', 'UTC+1', '欧洲', 'Europe', 'Europe/Madrid', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 40.4168, -3.7038, '08:00', '17:30', 6640000, '+34', 'EUR', 'es-ES', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('amsterdam', '阿姆斯特丹', 'Amsterdam', '荷兰', 'Netherlands', '🇳🇱', 'Europe/Amsterdam', 'UTC+1', '欧洲', 'Europe', 'Europe/Amsterdam', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 52.3676, 4.9041, '08:30', '16:30', 870000, '+31', 'EUR', 'nl-NL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('moscow', '莫斯科', 'Moscow', '俄罗斯', 'Russia', '🇷🇺', 'Europe/Moscow', 'UTC+3', '欧洲', 'Europe', 'Europe/Moscow', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 55.7558, 37.6173, '08:45', '16:15', 12500000, '+7', 'RUB', 'ru-RU', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 亚洲城市
('tokyo', '东京', 'Tokyo', '日本', 'Japan', '🇯🇵', 'Asia/Tokyo', 'UTC+9', '亚洲', 'Asia', 'Asia/Tokyo', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 35.6762, 139.6503, '04:31', '19:00', 13960000, '+81', 'JPY', 'ja-JP', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('seoul', '首尔', 'Seoul', '韩国', 'South Korea', '🇰🇷', 'Asia/Seoul', 'UTC+9', '亚洲', 'Asia', 'Asia/Seoul', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 37.5665, 126.9780, '05:45', '18:45', 9720000, '+82', 'KRW', 'ko-KR', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('singapore', '新加坡', 'Singapore', '新加坡', 'Singapore', '🇸🇬', 'Asia/Singapore', 'UTC+8', '亚洲', 'Asia', 'Asia/Singapore', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 1.3521, 103.8198, '07:00', '19:00', 5850000, '+65', 'SGD', 'en-SG', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('bangkok', '曼谷', 'Bangkok', '泰国', 'Thailand', '🇹🇭', 'Asia/Bangkok', 'UTC+7', '亚洲', 'Asia', 'Asia/Bangkok', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 13.7563, 100.5018, '06:30', '18:30', 10540000, '+66', 'THB', 'th-TH', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('mumbai', '孟买', 'Mumbai', '印度', 'India', '🇮🇳', 'Asia/Kolkata', 'UTC+5:30', '亚洲', 'Asia', 'Asia/Kolkata', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 19.0760, 72.8777, '06:45', '18:45', 20400000, '+91', 'INR', 'hi-IN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('dubai', '迪拜', 'Dubai', '阿联酋', 'United Arab Emirates', '🇦🇪', 'Asia/Dubai', 'UTC+4', '亚洲', 'Asia', 'Asia/Dubai', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 25.2048, 55.2708, '06:30', '18:30', 3400000, '+971', 'AED', 'ar-AE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('jakarta', '雅加达', 'Jakarta', '印尼', 'Indonesia', '🇮🇩', 'Asia/Jakarta', 'UTC+7', '亚洲', 'Asia', 'Asia/Jakarta', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', -6.2088, 106.8456, '06:00', '18:00', 10560000, '+62', 'IDR', 'id-ID', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 大洋洲城市
('sydney', '悉尼', 'Sydney', '澳洲', 'Australia', '🇦🇺', 'Australia/Sydney', 'UTC+11', '大洋洲', 'Oceania', 'Australia/Sydney', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', -33.8688, 151.2093, '05:30', '19:30', 5230000, '+61', 'AUD', 'en-AU', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('melbourne', '墨尔本', 'Melbourne', '澳洲', 'Australia', '🇦🇺', 'Australia/Melbourne', 'UTC+11', '大洋洲', 'Oceania', 'Australia/Melbourne', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', -37.8136, 144.9631, '05:45', '19:45', 5080000, '+61', 'AUD', 'en-AU', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('auckland', '奥克兰', 'Auckland', '新西兰', 'New Zealand', '🇳🇿', 'Pacific/Auckland', 'UTC+13', '大洋洲', 'Oceania', 'Pacific/Auckland', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', -36.8485, 174.7633, '05:00', '20:00', 1660000, '+64', 'NZD', 'en-NZ', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 加拿大城市
('toronto', '多伦多', 'Toronto', '加拿大', 'Canada', '🇨🇦', 'America/Toronto', 'UTC-5', '北美洲', 'North America', 'America/Toronto', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 43.6532, -79.3832, '07:30', '17:00', 2930000, '+1', 'CAD', 'en-CA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vancouver', '温哥华', 'Vancouver', '加拿大', 'Canada', '🇨🇦', 'America/Vancouver', 'UTC-8', '北美洲', 'North America', 'America/Vancouver', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', 49.2827, -123.1207, '07:45', '17:15', 2630000, '+1', 'CAD', 'en-CA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 南美洲城市
('sao-paulo', '圣保罗', 'São Paulo', '巴西', 'Brazil', '🇧🇷', 'America/Sao_Paulo', 'UTC-3', '南美洲', 'South America', 'America/Sao_Paulo', '当前时区实行夏时制', 'Daylight saving time is observed in this time zone', -23.5505, -46.6333, '06:00', '18:00', 12330000, '+55', 'BRL', 'pt-BR', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('buenos-aires', '布宜诺斯艾利斯', 'Buenos Aires', '阿根廷', 'Argentina', '🇦🇷', 'America/Argentina/Buenos_Aires', 'UTC-3', '南美洲', 'South America', 'America/Argentina/Buenos_Aires', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', -34.6118, -58.3960, '06:30', '18:30', 15200000, '+54', 'ARS', 'es-AR', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 非洲城市
('cairo', '开罗', 'Cairo', '埃及', 'Egypt', '🇪🇬', 'Africa/Cairo', 'UTC+2', '非洲', 'Africa', 'Africa/Cairo', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 30.0444, 31.2357, '06:30', '17:30', 20900000, '+20', 'EGP', 'ar-EG', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('johannesburg', '约翰内斯堡', 'Johannesburg', '南非', 'South Africa', '🇿🇦', 'Africa/Johannesburg', 'UTC+2', '非洲', 'Africa', 'Africa/Johannesburg', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', -26.2041, 28.0473, '05:30', '18:30', 5630000, '+27', 'ZAR', 'en-ZA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('lagos', '拉各斯', 'Lagos', '尼日利亚', 'Nigeria', '🇳🇬', 'Africa/Lagos', 'UTC+1', '非洲', 'Africa', 'Africa/Lagos', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 6.5244, 3.3792, '06:45', '18:45', 15400000, '+234', 'NGN', 'en-NG', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 港澳台地区
('hongkong', '香港', 'Hong Kong', '中国香港', 'Hong Kong', '🇭🇰', 'Asia/Hong_Kong', 'UTC+8', '亚洲', 'Asia', 'Asia/Hong_Kong', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 22.3193, 114.1694, '06:45', '17:45', 7500000, '+852', 'HKD', 'zh-HK', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('macau', '澳门', 'Macau', '中国澳门', 'Macau', '🇲🇴', 'Asia/Macau', 'UTC+8', '亚洲', 'Asia', 'Asia/Macau', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 22.1987, 113.5439, '06:50', '17:50', 680000, '+853', 'MOP', 'zh-MO', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('taipei', '台北', 'Taipei', '中国台湾', 'Taiwan', '🇹🇼', 'Asia/Taipei', 'UTC+8', '亚洲', 'Asia', 'Asia/Taipei', '当前时区不实行夏时制', 'Daylight saving time is not observed in this time zone', 25.0330, 121.5654, '06:30', '17:30', 2640000, '+886', 'TWD', 'zh-TW', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP); 