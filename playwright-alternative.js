// 替代 @playwright/mcp 的 Playwright 测试方案
// 这个方案避免了 MCP 依赖问题，直接使用 Playwright

const { chromium } = require('playwright');

async function testWebsite() {
  console.log('🚀 启动 Playwright 测试...');
  
  const browser = await chromium.launch({ 
    headless: false,  // 设置为 true 可以无头模式运行
    slowMo: 1000      // 减慢操作速度以便观察
  });
  
  const page = await browser.newPage();
  
  try {
    // 测试主页
    console.log('📄 访问主页...');
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题
    const title = await page.title();
    console.log('📝 页面标题:', title);
    
    // 截图
    await page.screenshot({ path: 'homepage.png' });
    console.log('📸 已保存截图: homepage.png');
    
    // 检查控制台错误
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // 等待一段时间收集错误
    await page.waitForTimeout(3000);
    
    if (errors.length > 0) {
      console.log('❌ 发现控制台错误:');
      errors.forEach(error => console.log('  -', error));
    } else {
      console.log('✅ 没有控制台错误');
    }
    
    // 测试搜索功能（如果存在）
    const searchInput = await page.$('input[type="search"], input[placeholder*="搜索"], input[placeholder*="search"]');
    if (searchInput) {
      console.log('🔍 测试搜索功能...');
      await searchInput.fill('北京');
      await page.keyboard.press('Enter');
      await page.waitForTimeout(2000);
      console.log('✅ 搜索功能测试完成');
    }
    
    // 测试语言切换（如果存在）
    const langButton = await page.$('button[data-lang], [data-testid="language-switcher"], button:has-text("EN"), button:has-text("中文")');
    if (langButton) {
      console.log('🌐 测试语言切换...');
      await langButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ 语言切换测试完成');
    }
    
    console.log('🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 保持浏览器打开以便查看结果
    console.log('🔍 浏览器将保持打开状态，按 Ctrl+C 退出');
    
    // 等待用户手动关闭
    await new Promise(() => {});
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔧 检查服务器状态...');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ 服务器未运行在 http://localhost:3000');
    console.log('请先运行: npm run dev');
    process.exit(1);
  }
  
  console.log('✅ 服务器正在运行');
  await testWebsite();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testWebsite, checkServer };
