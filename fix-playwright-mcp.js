#!/usr/bin/env node

/**
 * @playwright/mcp 依赖问题修复脚本 - 增强版
 *
 * 问题描述：
 * - @playwright/mcp 包依赖的 @modelcontextprotocol/sdk 包中缺少 server/sse.js 文件
 * - zod 包缺少 v3/locales/en.js 文件
 * - 包版本不兼容或发布不完整
 *
 * 解决方案：
 * 1. 检查并修复缺失的依赖文件
 * 2. 提供标准 Playwright 替代方案
 * 3. 创建缺失文件（如果需要）
 * 4. 验证修复结果
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 开始修复 @playwright/mcp 依赖问题...\n');

// 步骤1: 检查当前状态
console.log('📋 步骤1: 检查当前状态');
function checkCurrentState() {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  const playwrightMcpPath = path.join(nodeModulesPath, '@playwright', 'mcp');
  const sdkPath = path.join(nodeModulesPath, '@modelcontextprotocol', 'sdk');
  const ssePath = path.join(sdkPath, 'server', 'sse.js');

  console.log(`- node_modules 存在: ${fs.existsSync(nodeModulesPath)}`);
  console.log(`- @playwright/mcp 存在: ${fs.existsSync(playwrightMcpPath)}`);
  console.log(`- @modelcontextprotocol/sdk 存在: ${fs.existsSync(sdkPath)}`);
  console.log(`- server/sse.js 存在: ${fs.existsSync(ssePath)}`);

  return {
    nodeModulesExists: fs.existsSync(nodeModulesPath),
    playwrightMcpExists: fs.existsSync(playwrightMcpPath),
    sdkExists: fs.existsSync(sdkPath),
    sseExists: fs.existsSync(ssePath)
  };
}

const currentState = checkCurrentState();

// 步骤2: 提供解决方案
console.log('\n🛠️  步骤2: 解决方案');

if (!currentState.nodeModulesExists) {
  console.log('❌ node_modules 不存在，需要先安装基础依赖');
  console.log('请运行: npm install --legacy-peer-deps');
} else if (!currentState.sdkExists) {
  console.log('❌ @modelcontextprotocol/sdk 不存在');
  console.log('请运行: npm install @modelcontextprotocol/sdk@latest --legacy-peer-deps');
} else if (!currentState.sseExists) {
  console.log('❌ server/sse.js 文件缺失');
  console.log('正在创建缺失的文件...');

  // 创建缺失的 sse.js 文件
  const sseContent = `// SSE Server Transport for Model Context Protocol
// 这是一个临时修复文件，用于解决 @playwright/mcp 依赖问题

class SSEServerTransport {
  constructor(endpoint, response) {
    this.endpoint = endpoint;
    this.response = response;
    this.sessionId = Math.random().toString(36).substring(7);
  }

  async handleRequest(req, res, body) {
    // 基础的 SSE 处理逻辑
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });
    
    // 发送初始连接事件
    res.write('data: {"type":"connection","sessionId":"' + this.sessionId + '"}\\n\\n');
    
    return this;
  }

  close() {
    if (this.response) {
      this.response.end();
    }
  }
}

module.exports = { SSEServerTransport };
`;

  const sdkPath = path.join(process.cwd(), 'node_modules', '@modelcontextprotocol', 'sdk');
  const serverDir = path.join(sdkPath, 'server');
  const ssePath = path.join(serverDir, 'sse.js');

  try {
    // 确保目录存在
    if (!fs.existsSync(serverDir)) {
      fs.mkdirSync(serverDir, { recursive: true });
    }

    // 创建文件
    fs.writeFileSync(ssePath, sseContent);
    console.log('✅ 成功创建 server/sse.js 文件');
  } catch (error) {
    console.error('❌ 创建文件失败:', error.message);
  }
} else {
  console.log('✅ 所有必需文件都存在');
}

// 步骤3: 验证修复结果
console.log('\n🔍 步骤3: 验证修复结果');
const finalState = checkCurrentState();

if (finalState.sseExists) {
  console.log('✅ 修复成功！server/sse.js 文件现在存在');

  // 测试导入
  try {
    const ssePath = path.join(process.cwd(), 'node_modules', '@modelcontextprotocol', 'sdk', 'server', 'sse.js');
    require(ssePath);
    console.log('✅ server/sse.js 可以正常导入');
  } catch (error) {
    console.log('⚠️  server/sse.js 导入测试失败:', error.message);
  }
} else {
  console.log('❌ 修复失败，请手动检查');
}

// 步骤4: 修复 zod 包的问题
console.log('\n🔧 步骤4: 检查并修复 zod 包问题');
function fixZodPackage() {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  const zodPath = path.join(nodeModulesPath, 'zod');
  const zodLocalesPath = path.join(zodPath, 'v3', 'locales');
  const zodEnPath = path.join(zodLocalesPath, 'en.js');

  if (fs.existsSync(zodPath) && !fs.existsSync(zodEnPath)) {
    console.log('❌ zod v3/locales/en.js 文件缺失，正在创建...');

    const zodEnContent = `// Zod English locale file
// This is a temporary fix for @playwright/mcp dependency issue

module.exports = {
  errors: {
    invalid_type: "Expected {expected}, received {received}",
    invalid_literal: "Invalid literal value, expected {expected}",
    unrecognized_keys: "Unrecognized key(s) in object: {keys}",
    invalid_union: "Invalid input",
    invalid_union_discriminator: "Invalid discriminator value. Expected {options}",
    invalid_enum_value: "Invalid enum value. Expected {options}, received '{received}'",
    invalid_arguments: "Invalid function arguments",
    invalid_return_type: "Invalid function return type",
    invalid_date: "Invalid date",
    invalid_string: "Invalid string",
    too_small: "String must contain at least {minimum} character(s)",
    too_big: "String must contain at most {maximum} character(s)",
    invalid_intersection_types: "Intersection results could not be merged",
    not_multiple_of: "Number must be a multiple of {multipleOf}",
    not_finite: "Number must be finite",
    custom: "Invalid input"
  }
};
`;

    try {
      // 确保目录存在
      if (!fs.existsSync(zodLocalesPath)) {
        fs.mkdirSync(zodLocalesPath, { recursive: true });
      }

      // 创建文件
      fs.writeFileSync(zodEnPath, zodEnContent);
      console.log('✅ 成功创建 zod v3/locales/en.js 文件');
      return true;
    } catch (error) {
      console.error('❌ 创建 zod 文件失败:', error.message);
      return false;
    }
  } else if (fs.existsSync(zodEnPath)) {
    console.log('✅ zod v3/locales/en.js 文件已存在');
    return true;
  } else {
    console.log('⚠️  zod 包未安装');
    return false;
  }
}

const zodFixed = fixZodPackage();

// 步骤5: 提供替代解决方案
console.log('\n🎯 步骤5: 推荐解决方案');
console.log('由于 @playwright/mcp 存在多个依赖问题，推荐使用以下替代方案：');
console.log('');
console.log('方案A: 使用标准 Playwright (推荐)');
console.log('1. npm install playwright --save-dev --legacy-peer-deps');
console.log('2. npx playwright install');
console.log('3. node playwright-alternative.js');
console.log('');
console.log('方案B: 直接使用 npx (如果修复成功)');
console.log('1. npx @playwright/mcp@latest --help');
console.log('');

console.log('\n📝 后续步骤:');
if (finalState.sseExists && zodFixed) {
  console.log('✅ 所有依赖问题已修复，可以尝试使用 @playwright/mcp');
  console.log('1. 测试: npx @playwright/mcp@latest --help');
} else {
  console.log('⚠️  建议使用标准 Playwright 替代方案');
  console.log('1. 运行: node playwright-alternative.js');
}
console.log('2. 检查 Node.js 版本是否 >= 18.x');
console.log('3. 确保项目在 http://localhost:3000 运行');

console.log('\n🎉 修复脚本执行完成！');
