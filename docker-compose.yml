version: '3.8'

services:
  worldtimeapp:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: worldtimeapp
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    restart: unless-stopped
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: worldtimeapp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - worldtimeapp
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge 