import React from "react"
import type { Metada<PERSON> } from "next"
import { Inter, Noto_Sans_SC, Noto_Sans_JP, Noto_Sans_KR } from "next/font/google"
import "../globals.css"
import { HomePageStructuredData } from "@/components/StructuredData"
import { PWAInstall, PWAStatus } from "@/components/PWAInstall"
import { Analytics, CookieConsent, PerformanceMonitor } from "@/components/Analytics"

// 优化字体配置，支持多语言
const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  variable: '--font-inter'
})

const notoSansSC = Noto_Sans_SC({
  subsets: ["latin"],
  display: 'swap',
  preload: false, // 按需加载
  variable: '--font-noto-sc'
})

const notoSansJP = Noto_Sans_JP({
  subsets: ["latin"],
  display: 'swap',
  preload: false, // 按需加载
  variable: '--font-noto-jp'
})

const notoSansKR = Noto_Sans_KR({
  subsets: ["latin"],
  display: 'swap',
  preload: false, // 按需加载
  variable: '--font-noto-kr'
})

// 支持的语言列表
const locales = ['en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'ru'] as const
export type Locale = typeof locales[number]

// 语言到地区的映射
const localeToRegion: Record<Locale, string> = {
  en: 'en_US',
  zh: 'zh_CN',
  ja: 'ja_JP',
  ko: 'ko_KR',
  fr: 'fr_FR',
  de: 'de_DE',
  es: 'es_ES',
  ru: 'ru_RU'
}

// 生成语言alternates
function generateLanguageAlternates(path: string = '') {
  const alternates: Record<string, string> = {}
  
  locales.forEach(locale => {
    // 所有语言都包含语言前缀，包括英文
    const prefix = `/${locale}`
    alternates[localeToRegion[locale]] = `https://worldtimeapp.online${prefix}${path}`
  })
  
  return alternates
}

// 多语言元数据配置
const metadataConfig: Record<Locale, {
  title: string
  description: string
  keywords: string
}> = {
  en: {
    title: "World Time - Global Timezone Checker | WorldTimeApp",
    description: "Free global timezone checker tool. Check real-time for major cities worldwide, timezone conversion, daylight saving time information. Accurate display of current time in Beijing, Tokyo, New York, London, Paris and other cities. Multi-language interface supported.",
    keywords: "world time,timezone converter,global time,time zone,beijing time,tokyo time,new york time,london time,paris time,timezone checker"
  },
  zh: {
    title: "世界时间 - 全球时区查询器 | WorldTimeApp",
    description: "免费的全球时区查询工具。查看世界主要城市的实时时间，时区转换，夏令时信息。准确显示北京时间、东京时间、纽约时间、伦敦时间、巴黎时间等城市的当前时间。支持多语言界面。",
    keywords: "世界时间,时区转换,全球时间,时区查询,北京时间,东京时间,纽约时间,伦敦时间,巴黎时间,时区查看器"
  },
  ja: {
    title: "世界時間 - グローバルタイムゾーンチェッカー | WorldTimeApp",
    description: "無料のグローバルタイムゾーンチェッカーツール。世界主要都市のリアルタイム時間、タイムゾーン変換、夏時間情報を確認。北京、東京、ニューヨーク、ロンドン、パリなどの都市の現在時刻を正確に表示。多言語インターフェース対応。",
    keywords: "世界時間,タイムゾーン変換,グローバル時間,時差,北京時間,東京時間,ニューヨーク時間,ロンドン時間,パリ時間"
  },
  ko: {
    title: "세계 시간 - 글로벌 시간대 확인기 | WorldTimeApp",
    description: "무료 글로벌 시간대 확인 도구. 전 세계 주요 도시의 실시간 시간, 시간대 변환, 일광 절약 시간 정보를 확인하세요. 베이징, 도쿄, 뉴욕, 런던, 파리 등 도시의 현재 시간을 정확하게 표시. 다국어 인터페이스 지원.",
    keywords: "세계시간,시간대변환,글로벌시간,시차,베이징시간,도쿄시간,뉴욕시간,런던시간,파리시간"
  },
  fr: {
    title: "Heure Mondiale - Vérificateur de Fuseau Horaire Global | WorldTimeApp",
    description: "Outil gratuit de vérification des fuseaux horaires mondiaux. Vérifiez l'heure en temps réel des principales villes du monde, conversion des fuseaux horaires, informations sur l'heure d'été. Affichage précis de l'heure actuelle à Pékin, Tokyo, New York, Londres, Paris et autres villes. Interface multilingue prise en charge.",
    keywords: "heure mondiale,convertisseur fuseau horaire,heure globale,décalage horaire,heure pékin,heure tokyo,heure new york,heure londres,heure paris"
  },
  de: {
    title: "Weltzeit - Globaler Zeitzonenchecker | WorldTimeApp",
    description: "Kostenloses globales Zeitzonenchecker-Tool. Überprüfen Sie die Echtzeit der wichtigsten Städte weltweit, Zeitzonenkonvertierung, Sommerzeitinformationen. Genaue Anzeige der aktuellen Zeit in Peking, Tokio, New York, London, Paris und anderen Städten. Mehrsprachige Benutzeroberfläche unterstützt.",
    keywords: "weltzeit,zeitzonenkonverter,globale zeit,zeitunterschied,peking zeit,tokio zeit,new york zeit,london zeit,paris zeit"
  },
  es: {
    title: "Hora Mundial - Verificador de Zona Horaria Global | WorldTimeApp",
    description: "Herramienta gratuita de verificación de zonas horarias globales. Verifique la hora en tiempo real de las principales ciudades del mundo, conversión de zonas horarias, información de horario de verano. Visualización precisa de la hora actual en Beijing, Tokio, Nueva York, Londres, París y otras ciudades. Interfaz multiidioma compatible.",
    keywords: "hora mundial,convertidor zona horaria,hora global,diferencia horaria,hora beijing,hora tokio,hora nueva york,hora londres,hora paris"
  },
  ru: {
    title: "Мировое Время - Глобальный Проверщик Часовых Поясов | WorldTimeApp",
    description: "Бесплатный инструмент проверки глобальных часовых поясов. Проверьте время в реальном времени основных городов мира, преобразование часовых поясов, информацию о летнем времени. Точное отображение текущего времени в Пекине, Токио, Нью-Йорке, Лондоне, Париже и других городах. Поддерживается многоязычный интерфейс.",
    keywords: "мировое время,конвертер часовых поясов,глобальное время,разница во времени,время пекин,время токио,время нью-йорк,время лондон,время париж"
  }
}

export async function generateMetadata({ params }: { params: Promise<{ locale: Locale }> }): Promise<Metadata> {
  const { locale } = await params
  const config = metadataConfig[locale] || metadataConfig.en
  // 所有语言都包含语言前缀，包括英文
  const canonicalPath = `/${locale}`

  return {
    title: config.title,
    description: config.description,
    keywords: config.keywords,
    authors: [{ name: "WorldTimeApp Team" }],
    creator: "WorldTimeApp",
    publisher: "WorldTimeApp",
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://worldtimeapp.online'),
    alternates: {
      canonical: `https://worldtimeapp.online${canonicalPath}`,
      languages: generateLanguageAlternates()
    },
    openGraph: {
      type: 'website',
      locale: localeToRegion[locale],
      url: `https://worldtimeapp.online${canonicalPath}`,
      title: config.title,
      description: config.description,
      siteName: 'WorldTimeApp',
      images: [
        {
          url: '/timecha-screenshot.png',
          width: 1200,
          height: 630,
          alt: 'WorldTimeApp - Global Timezone Checker Interface',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: config.title,
      description: config.description,
      images: ['/timecha-screenshot.png'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
  }
}

// 生成静态参数
export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export default function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: Locale }>
}) {
  const { locale } = React.use(params)

  // 根据语言选择合适的字体
  const getFontClass = (locale: Locale) => {
    switch (locale) {
      case 'zh':
        return `${inter.variable} ${notoSansSC.variable} font-sans`
      case 'ja':
        return `${inter.variable} ${notoSansJP.variable} font-sans`
      case 'ko':
        return `${inter.variable} ${notoSansKR.variable} font-sans`
      default:
        return `${inter.variable} font-sans`
    }
  }

  return (
    <html lang={locale}>
      <head>
        <HomePageStructuredData locale={locale} />
        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="WorldTimeApp" />
        <link rel="apple-touch-icon" href="/icon-192x192.png" />

        {/* 字体预加载优化 */}
        <link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        {locale === 'zh' && (
          <link rel="preload" href="/fonts/noto-sans-sc.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        )}
        {locale === 'ja' && (
          <link rel="preload" href="/fonts/noto-sans-jp.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        )}
        {locale === 'ko' && (
          <link rel="preload" href="/fonts/noto-sans-kr.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        )}
      </head>
      <body className={getFontClass(locale)}>
        {children}
        <PWAInstall locale={locale} />
        <PWAStatus locale={locale} />
        <Analytics locale={locale} />
        <CookieConsent locale={locale} />
        <PerformanceMonitor />

        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                // 检测是否为开发环境
                const isDevelopment = window.location.hostname === 'localhost' ||
                                    window.location.hostname === '127.0.0.1' ||
                                    window.location.hostname.includes('localhost');

                if (isDevelopment) {
                  // 开发环境：清除现有的 Service Worker
                  navigator.serviceWorker.getRegistrations().then(function(registrations) {
                    for(let registration of registrations) {
                      registration.unregister();
                      console.log('SW unregistered in development mode');
                    }
                  });
                } else {
                  // 生产环境：注册 Service Worker
                  window.addEventListener('load', function() {
                    navigator.serviceWorker.register('/sw.js')
                      .then(function(registration) {
                        console.log('SW registered: ', registration);
                      })
                      .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                      });
                  });
                }
              }
            `,
          }}
        />
      </body>
    </html>
  )
}