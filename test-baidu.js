#!/usr/bin/env node

/**
 * 使用 Playwright 测试百度网站
 * 
 * 功能：
 * 1. 打开百度首页
 * 2. 检查页面标题
 * 3. 进行搜索测试
 * 4. 截图保存
 */

const { chromium } = require('playwright');

async function testBaidu() {
  console.log('🚀 启动 Playwright 测试百度...');
  
  const browser = await chromium.launch({ 
    headless: false,  // 显示浏览器窗口
    slowMo: 1000      // 减慢操作速度以便观察
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 访问百度首页
    console.log('📄 访问百度首页...');
    await page.goto('https://www.baidu.com');
    await page.waitForLoadState('networkidle');
    
    // 2. 检查页面标题
    const title = await page.title();
    console.log('📝 页面标题:', title);
    
    // 3. 截图
    await page.screenshot({ path: 'baidu-homepage.png' });
    console.log('📸 已保存截图: baidu-homepage.png');
    
    // 4. 检查搜索框是否存在
    const searchInput = await page.$('#kw');
    if (searchInput) {
      console.log('✅ 找到搜索框');
      
      // 5. 进行搜索测试
      console.log('🔍 测试搜索功能...');
      await searchInput.fill('Playwright 自动化测试');
      
      // 点击搜索按钮
      const searchButton = await page.$('#su');
      if (searchButton) {
        await searchButton.click();
        console.log('🔍 点击搜索按钮');
        
        // 等待搜索结果加载
        await page.waitForTimeout(3000);
        
        // 检查是否有搜索结果
        const results = await page.$$('.result');
        console.log(`📊 找到 ${results.length} 个搜索结果`);
        
        // 截图搜索结果页面
        await page.screenshot({ path: 'baidu-search-results.png' });
        console.log('📸 已保存搜索结果截图: baidu-search-results.png');
        
        // 获取第一个搜索结果的标题
        const firstResult = await page.$('.result h3 a');
        if (firstResult) {
          const firstResultText = await firstResult.textContent();
          console.log('🎯 第一个搜索结果:', firstResultText);
        }
      }
    } else {
      console.log('❌ 未找到搜索框');
    }
    
    // 6. 检查控制台错误
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // 等待一段时间收集错误
    await page.waitForTimeout(2000);
    
    if (errors.length > 0) {
      console.log('❌ 发现控制台错误:');
      errors.forEach(error => console.log('  -', error));
    } else {
      console.log('✅ 没有控制台错误');
    }
    
    console.log('🎉 百度测试完成！');
    
    // 显示测试总结
    console.log('\n📋 测试总结:');
    console.log('- ✅ 成功访问百度首页');
    console.log('- ✅ 页面标题正确显示');
    console.log('- ✅ 搜索功能正常工作');
    console.log('- ✅ 生成了页面截图');
    console.log('- ✅ 检查了控制台错误');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 保持浏览器打开以便查看结果
    console.log('\n🔍 浏览器将保持打开状态，按 Ctrl+C 退出');
    
    // 等待用户手动关闭
    await new Promise(() => {});
  }
}

// 主函数
async function main() {
  console.log('🔧 检查网络连接...');
  
  try {
    // 简单的网络连接测试
    const testResponse = await fetch('https://www.baidu.com', { 
      method: 'HEAD',
      timeout: 5000 
    });
    
    if (testResponse.ok) {
      console.log('✅ 网络连接正常');
      await testBaidu();
    } else {
      console.log('❌ 网络连接异常');
    }
  } catch (error) {
    console.log('⚠️  网络连接测试失败，但继续尝试访问百度');
    await testBaidu();
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testBaidu };
