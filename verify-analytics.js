#!/usr/bin/env node

/**
 * Google Analytics 配置验证脚本
 */

const fs = require('fs');
const path = require('path');

function verifyAnalytics() {
  console.log('🔍 验证 Google Analytics 配置...\n');

  const envPath = path.join(process.cwd(), '.env.local');
  
  // 检查 .env.local 文件
  if (!fs.existsSync(envPath)) {
    console.log('❌ 未找到 .env.local 文件');
    console.log('💡 请运行：npm run setup-analytics G-YOUR-MEASUREMENT-ID');
    return false;
  }

  // 读取环境变量
  const envContent = fs.readFileSync(envPath, 'utf8');
  const gaMatch = envContent.match(/NEXT_PUBLIC_GA_MEASUREMENT_ID=(.+)/);
  
  if (!gaMatch) {
    console.log('❌ 未找到 NEXT_PUBLIC_GA_MEASUREMENT_ID 配置');
    console.log('💡 请运行：npm run setup-analytics G-YOUR-MEASUREMENT-ID');
    return false;
  }

  const measurementId = gaMatch[1].trim();
  
  // 验证测量ID格式
  if (!measurementId.match(/^G-[A-Z0-9]+$/)) {
    console.log(`❌ 测量ID格式错误: ${measurementId}`);
    console.log('💡 正确格式应为: G-XXXXXXXXXX');
    return false;
  }

  if (measurementId === 'G-XXXXXXXXXX') {
    console.log('⚠️  使用的是示例测量ID，请替换为真实的测量ID');
    console.log('💡 请运行：npm run setup-analytics G-YOUR-REAL-MEASUREMENT-ID');
    return false;
  }

  // 检查 Analytics 组件
  const analyticsPath = path.join(process.cwd(), 'components', 'Analytics.tsx');
  if (!fs.existsSync(analyticsPath)) {
    console.log('❌ 未找到 Analytics 组件');
    return false;
  }

  // 检查 analytics 库
  const libPath = path.join(process.cwd(), 'lib', 'analytics.ts');
  if (!fs.existsSync(libPath)) {
    console.log('❌ 未找到 analytics 库');
    return false;
  }

  console.log('✅ Google Analytics 配置验证通过！');
  console.log(`📊 测量ID: ${measurementId}`);
  console.log('');
  console.log('🚀 下一步验证：');
  console.log('1. 启动开发服务器：npm run dev');
  console.log('2. 访问 http://localhost:3000');
  console.log('3. 打开浏览器开发者工具，检查控制台是否有 GA 日志');
  console.log('4. 在 Google Analytics 实时报告中查看数据');
  console.log('');
  console.log('📖 详细指南：查看 GOOGLE_ANALYTICS_SETUP.md');
  
  return true;
}

// 运行验证
verifyAnalytics();
