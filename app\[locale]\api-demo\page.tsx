import React from "react"
import { <PERSON>, ChevronRight, ArrowLeft, Code, Globe } from "lucide-react"
import Link from "next/link"
import type { Locale } from "../layout"
import { generateLocalizedMetadata } from "@/lib/metadata"

// 生成静态参数
export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'zh' },
    { locale: 'ja' },
    { locale: 'ko' },
    { locale: 'fr' },
    { locale: 'de' },
    { locale: 'es' },
    { locale: 'ru' },
  ]
}

// 生成多语言元数据
export async function generateMetadata({ params }: { params: Promise<{ locale: Locale }> }) {
  const { locale } = await params
  return generateLocalizedMetadata('api-demo', locale)
}

export default function APIDemoPage({ params }: { params: Promise<{ locale: Locale }> }) {
  const { locale } = React.use(params)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                                 <Link href={`/${locale}`} className="text-xl font-semibold text-gray-800 hover:text-blue-600">
                   WorldTimeApp
                 </Link>
               </div>
             </div>
 
             <div className="flex items-center gap-4">
               <Link href={`/${locale}/faq`} className="text-gray-600 hover:text-gray-800 text-sm">
                 {locale === 'zh' ? '常见问题' : 'FAQ'}
               </Link>
             </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-100 px-4 py-2">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center gap-2 text-sm text-gray-600">
                         <Link href={`/${locale}`} className="hover:text-blue-600 flex items-center gap-1">
               <ArrowLeft className="w-3 h-3" />
               {locale === 'zh' ? '首页' : 'Home'}
             </Link>
            <ChevronRight className="w-4 h-4" />
            <span className="text-gray-800 font-medium">API Demo</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            {locale === 'zh' ? 'API 演示' : 'API Demo'}
          </h1>
          <p className="text-gray-600 text-lg">
            {locale === 'zh' 
              ? '探索WorldTimeApp的API接口，了解如何在您的应用中集成时区数据'
              : 'Explore WorldTimeApp API interfaces and learn how to integrate timezone data into your applications'
            }
          </p>
        </div>

        {/* API Endpoints */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Cities API */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center gap-3 mb-4">
              <Globe className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-800">
                {locale === 'zh' ? '城市数据 API' : 'Cities Data API'}
              </h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">
                  {locale === 'zh' ? '获取所有城市' : 'Get All Cities'}
                </h3>
                <code className="block bg-gray-100 p-3 rounded text-sm">
                  GET /api/cities
                </code>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-700 mb-2">
                  {locale === 'zh' ? '获取城市详情' : 'Get City Details'}
                </h3>
                <code className="block bg-gray-100 p-3 rounded text-sm">
                  GET /api/cities/[id]
                </code>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-700 mb-2">
                  {locale === 'zh' ? '按名称搜索城市' : 'Search Cities by Name'}
                </h3>
                <code className="block bg-gray-100 p-3 rounded text-sm">
                  GET /api/cities/name/[name]
                </code>
              </div>
            </div>
          </div>

          {/* Response Example */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center gap-3 mb-4">
              <Code className="w-6 h-6 text-green-600" />
              <h2 className="text-xl font-semibold text-gray-800">
                {locale === 'zh' ? '响应示例' : 'Response Example'}
              </h2>
            </div>
            
            <div className="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-x-auto">
              <pre>{`{
  "id": "tokyo",
  "city": "东京",
  "cityEn": "Tokyo",
  "country": "日本",
  "countryEn": "Japan",
  "flag": "🇯🇵",
  "timezone": "Asia/Tokyo",
  "utcOffset": "UTC+9",
  "continent": "亚洲",
  "continentEn": "Asia",
  "latitude": 35.6762,
  "longitude": 139.6503,
  "population": 13960000,
  "currency": "JPY",
  "language": "Japanese"
}`}</pre>
            </div>
          </div>
        </div>

        {/* Code Examples */}
        <div className="space-y-8">
          {/* JavaScript Example */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? 'JavaScript 示例' : 'JavaScript Example'}
            </h2>
            
            <div className="bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto">
              <pre>{`// ${locale === 'zh' ? '获取城市数据' : 'Fetch city data'}
async function getCityTime(cityId) {
  try {
    const response = await fetch(\`/api/cities/\${cityId}\`);
    const city = await response.json();
    
    // ${locale === 'zh' ? '获取当前时间' : 'Get current time'}
    const now = new Date();
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: city.timezone,
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    
    const time = formatter.format(now);
    console.log(\`\${city.cityEn}: \${time}\`);
    
    return { city, time };
  } catch (error) {
    console.error('Error fetching city data:', error);
  }
}

// ${locale === 'zh' ? '使用示例' : 'Usage example'}
getCityTime('tokyo');
getCityTime('new-york');
getCityTime('london');`}</pre>
            </div>
          </div>

          {/* Python Example */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? 'Python 示例' : 'Python Example'}
            </h2>
            
            <div className="bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto">
              <pre>{`import requests
from datetime import datetime
import pytz

def get_city_time(city_id):
    """${locale === 'zh' ? '获取城市时间信息' : 'Get city time information'}"""
    try:
        # ${locale === 'zh' ? '获取城市数据' : 'Fetch city data'}
        response = requests.get(f'https://worldtimeapp.online/api/cities/{city_id}')
        city = response.json()
        
        # ${locale === 'zh' ? '获取时区信息' : 'Get timezone info'}
        timezone = pytz.timezone(city['timezone'])
        current_time = datetime.now(timezone)
        
        print(f"{city['cityEn']}: {current_time.strftime('%H:%M:%S')}")
        
        return {
            'city': city,
            'time': current_time.strftime('%H:%M:%S'),
            'date': current_time.strftime('%Y-%m-%d')
        }
    except Exception as e:
        print(f"Error: {e}")
        return None

# ${locale === 'zh' ? '使用示例' : 'Usage example'}
get_city_time('tokyo')
get_city_time('new-york')
get_city_time('london')`}</pre>
            </div>
          </div>

          {/* React Example */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? 'React 组件示例' : 'React Component Example'}
            </h2>
            
            <div className="bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto">
              <pre>{`import React, { useState, useEffect } from 'react';

function WorldClock({ cityId }) {
  const [cityData, setCityData] = useState(null);
  const [currentTime, setCurrentTime] = useState('');

  useEffect(() => {
    // ${locale === 'zh' ? '获取城市数据' : 'Fetch city data'}
    fetch(\`/api/cities/\${cityId}\`)
      .then(response => response.json())
      .then(data => setCityData(data))
      .catch(error => console.error('Error:', error));
  }, [cityId]);

  useEffect(() => {
    if (!cityData) return;

    // ${locale === 'zh' ? '更新时间' : 'Update time'}
    const updateTime = () => {
      const now = new Date();
      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: cityData.timezone,
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      setCurrentTime(formatter.format(now));
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [cityData]);

  if (!cityData) return <div>Loading...</div>;

  return (
    <div className="city-clock">
      <h3>{cityData.cityEn}</h3>
      <div className="time">{currentTime}</div>
      <div className="timezone">{cityData.timezone}</div>
      <div className="country">{cityData.countryEn}</div>
    </div>
  );
}

export default WorldClock;`}</pre>
            </div>
          </div>
        </div>

        {/* API Documentation */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            {locale === 'zh' ? 'API 文档' : 'API Documentation'}
          </h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">
                {locale === 'zh' ? '基础 URL' : 'Base URL'}
              </h3>
              <code className="block bg-gray-100 p-3 rounded">
                https://worldtimeapp.online/api
              </code>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 mb-2">
                {locale === 'zh' ? '支持的格式' : 'Supported Formats'}
              </h3>
              <p className="text-gray-600">JSON</p>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 mb-2">
                {locale === 'zh' ? '请求限制' : 'Rate Limits'}
              </h3>
              <p className="text-gray-600">
                {locale === 'zh' 
                  ? '每分钟最多60次请求'
                  : 'Maximum 60 requests per minute'
                }
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 mb-2">
                {locale === 'zh' ? '错误处理' : 'Error Handling'}
              </h3>
              <p className="text-gray-600">
                {locale === 'zh' 
                  ? 'API返回标准的HTTP状态码。404表示城市未找到，500表示服务器错误。'
                  : 'API returns standard HTTP status codes. 404 for city not found, 500 for server errors.'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            {locale === 'zh' ? '开始使用' : 'Get Started'}
          </h2>
          <p className="text-gray-600 mb-6">
            {locale === 'zh' 
              ? '立即开始使用我们的API，为您的应用添加全球时区功能'
              : 'Start using our API today to add global timezone functionality to your applications'
            }
          </p>
          <Link 
            href={`/${locale}`}
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Globe className="w-5 h-5" />
            {locale === 'zh' ? '查看实时演示' : 'View Live Demo'}
          </Link>
        </div>
      </main>
    </div>
  )
} 