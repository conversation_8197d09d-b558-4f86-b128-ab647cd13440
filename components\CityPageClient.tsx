"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Search, Clock, ChevronRight, Download, Sun, ArrowLeft, Globe, Check, Plus, BarChart3 } from "lucide-react"
import { Input } from "@/components/ui/input"
import Link from "next/link"
import { useI18n } from "@/hooks/useI18n"
import { LanguageSwitchI18n } from "@/components/LanguageSwitchI18n"
import { useTranslation } from "react-i18next"
import { CitiesService, CityDetail } from "@/lib/cities-service"
import { LocalStorageService } from "@/lib/local-storage-service"
import type { Locale } from "../app/[locale]/layout"
import { CityPageStructuredData } from "@/components/StructuredData"

interface CityPageClientProps {
  initialCity: CityDetail
  locale: Locale
}

export function CityPageClient({ initialCity, locale }: CityPageClientProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [cityDetail, setCityDetail] = useState<CityDetail>(initialCity)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<CityDetail[]>([])
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isAddedToHomepage, setIsAddedToHomepage] = useState(false)
  const [showNotification, setShowNotification] = useState(false)
  const [notificationMessage, setNotificationMessage] = useState("")
  const [isDefaultTimezone, setIsDefaultTimezone] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const { language, t, isInitialized, changeLanguage } = useI18n()
  const { t: tDetail } = useTranslation()
  const router = useRouter()

  // 客户端渲染标志
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 确保i18n语言与URL locale同步
  React.useEffect(() => {
    if (isInitialized && language !== locale) {
      changeLanguage(locale)
    }
  }, [locale, language, changeLanguage, isInitialized])

  // 相关城市状态
  const [relatedCities, setRelatedCities] = useState<{
    sameCountryCities: CityDetail[],
    sameTimezoneCities: CityDetail[],
    popularCities: CityDetail[]
  }>({
    sameCountryCities: [],
    sameTimezoneCities: [],
    popularCities: []
  })

  useEffect(() => {
    // 等待 i18n 初始化完成
    if (!isInitialized) return

    const loadRelatedData = async () => {
      try {
        // 检查城市是否已添加到主页
        const userCities = LocalStorageService.getUserCities()
        const isAlreadyAdded = userCities?.some(c => c.id === cityDetail.id) || false
        setIsAddedToHomepage(isAlreadyAdded)
        
        // 检查是否为默认时区
        const defaultTimezone = LocalStorageService.getDefaultTimezone()
        setIsDefaultTimezone(defaultTimezone === cityDetail.timezone)
        
        // 获取相关城市
        const related = await CitiesService.getRelatedCities(cityDetail, 4)
        setRelatedCities(related)
      } catch (error) {
        console.error('加载相关数据失败:', error)
      }
    }

    loadRelatedData()
  }, [isInitialized, cityDetail])

  // 时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // 搜索功能
  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.trim().length > 0) {
      try {
        const results = await CitiesService.searchCities(query)
        setSearchResults(results.slice(0, 8))
        setShowSearchResults(true)
      } catch (error) {
        console.error('搜索失败:', error)
        setSearchResults([])
      }
    } else {
      setSearchResults([])
      setShowSearchResults(false)
    }
  }

  // 添加到主页
  const handleAddToHomepage = () => {
    try {
      if (isAddedToHomepage) {
        LocalStorageService.removeUserCity(cityDetail.id)
        setIsAddedToHomepage(false)
        setNotificationMessage(t('removedFromHomepage'))
      } else {
        LocalStorageService.addUserCity(cityDetail)
        setIsAddedToHomepage(true)
        setNotificationMessage(t('addedToHomepage'))
      }
      setShowNotification(true)
      setTimeout(() => setShowNotification(false), 3000)
    } catch (error) {
      console.error('操作失败:', error)
    }
  }

  // 设置默认时区
  const handleSetDefaultTimezone = () => {
    try {
      if (isDefaultTimezone) {
        LocalStorageService.clearDefaultTimezone()
        setIsDefaultTimezone(false)
        setNotificationMessage(t('removedDefaultTimezone'))
      } else {
        LocalStorageService.setDefaultTimezone(cityDetail.timezone)
        setIsDefaultTimezone(true)
        setNotificationMessage(t('setAsDefaultTimezone'))
      }
      setShowNotification(true)
      setTimeout(() => setShowNotification(false), 3000)
    } catch (error) {
      console.error('设置默认时区失败:', error)
    }
  }

  // 面包屑点击处理
  const handleBreadcrumbClick = (type: 'country' | 'continent', value: string) => {
    const searchQuery = encodeURIComponent(value)
    router.push(`/${locale}/?search=${searchQuery}`)
  }

  // 获取时区时间
  const getTimeForTimezone = (timezone: string) => {
    try {
      const now = currentTime // 使用组件状态中的当前时间
      
      const timeOptions: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: timezone
      }
      
      const dateOptions: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: timezone
      }
      
      const weekdayOptions: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        timeZone: timezone
      }
      
      const localeMap: Record<string, string> = {
        'zh': 'zh-CN',
        'ja': 'ja-JP',
        'ko': 'ko-KR',
        'fr': 'fr-FR',
        'de': 'de-DE',
        'es': 'es-ES',
        'ru': 'ru-RU',
        'en': 'en-US'
      }
      
      const currentLocale = localeMap[locale] || 'en-US'

      return {
        time: now.toLocaleTimeString(currentLocale, timeOptions),
        date: now.toLocaleDateString(currentLocale, dateOptions),
        weekday: now.toLocaleDateString(currentLocale, weekdayOptions)
      }
    } catch (error) {
      console.error('获取时区时间失败:', error)
      return {
        time: '--:--:--',
        date: '--',
        weekday: '--'
      }
    }
  }

  // 获取日出日落时间
  const getSunTimes = (city: CityDetail) => {
    try {
      const now = new Date()
      const timeZone = city.timezone
      
      // 这里应该使用真实的日出日落计算，暂时使用静态数据
      return {
        sunrise: city.sunrise || '06:00',
        sunset: city.sunset || '18:00'
      }
    } catch (error) {
      console.error('获取日出日落时间失败:', error)
      return {
        sunrise: '06:00',
        sunset: '18:00'
      }
    }
  }

  // 获取与参考时区的时差
  const getTimeDifferenceWithReference = (timezone: string, defaultTimezone: string = 'Asia/Shanghai') => {
    if (timezone === defaultTimezone) {
      // 如果是北京时间，显示"与北京时间相同"，否则显示"与默认时区相同"
      return defaultTimezone === 'Asia/Shanghai' ? t('sameAsBeijing') : t('sameAsDefault')
    }
    
    try {
      const now = new Date()
      const cityTime = new Date(now.toLocaleString("en-US", { timeZone: timezone }))
      const referenceTime = new Date(now.toLocaleString("en-US", { timeZone: defaultTimezone }))
      
      const diffMs = cityTime.getTime() - referenceTime.getTime()
      const diffHours = Math.round(diffMs / (1000 * 60 * 60))
      
      if (diffHours === 0) {
        return defaultTimezone === 'Asia/Shanghai' ? t('sameAsBeijing') : t('sameAsDefault')
      } else if (diffHours > 0) {
        const laterText = defaultTimezone === 'Asia/Shanghai' ? t('laterThan') : t('laterThanDefault')
        return `${laterText} ${Math.abs(diffHours)} ${t('hours')}`
      } else {
        const earlierText = defaultTimezone === 'Asia/Shanghai' ? t('earlierThan') : t('earlierThanDefault')
        return `${earlierText} ${Math.abs(diffHours)} ${t('hours')}`
      }
    } catch (error) {
      console.error('计算时差失败:', error)
      return '--'
    }
  }

  // 获取本地化城市名称
  const getLocalizedCityName = (city: CityDetail) => {
    const cityNameMap: { [key: string]: { [lang: string]: string } } = {
      'beijing': {
        'zh': '北京',
        'en': 'Beijing',
        'ja': '北京',
        'ko': '베이징',
        'fr': 'Pékin',
        'de': 'Peking',
        'es': 'Pekín',
        'ru': 'Пекин'
      },
      'tokyo': {
        'zh': '东京',
        'en': 'Tokyo',
        'ja': '東京',
        'ko': '도쿄',
        'fr': 'Tokyo',
        'de': 'Tokio',
        'es': 'Tokio',
        'ru': 'Токио'
      },
      'new-york': {
        'zh': '纽约',
        'en': 'New York',
        'ja': 'ニューヨーク',
        'ko': '뉴욕',
        'fr': 'New York',
        'de': 'New York',
        'es': 'Nueva York',
        'ru': 'Нью-Йорк'
      },
      'london': {
        'zh': '伦敦',
        'en': 'London',
        'ja': 'ロンドン',
        'ko': '런던',
        'fr': 'Londres',
        'de': 'London',
        'es': 'Londres',
        'ru': 'Лондон'
      },
      'paris': {
        'zh': '巴黎',
        'en': 'Paris',
        'ja': 'パリ',
        'ko': '파리',
        'fr': 'Paris',
        'de': 'Paris',
        'es': 'París',
        'ru': 'Париж'
      }
    }

    const cityTranslations = cityNameMap[city.id]
    if (cityTranslations && cityTranslations[locale]) {
      return cityTranslations[locale]
    }
    
    return locale === "zh" ? city.city : city.cityEn
  }

  // 获取本地化国家名称
  const getLocalizedCountryName = (city: CityDetail) => {
    return locale === "zh" ? city.country : city.countryEn
  }

  // 获取时区的友好名称
  const getTimezoneFriendlyName = (timezone: string) => {
    const timezoneNames: { [key: string]: { [lang: string]: string } } = {
      'Asia/Shanghai': {
        'zh': '北京时间',
        'en': 'Beijing Time',
        'ja': '北京時間',
        'ko': '베이징 시간',
        'fr': 'Heure de Pékin',
        'de': 'Peking-Zeit',
        'es': 'Hora de Pekín',
        'ru': 'Пекинское время'
      },
      'Europe/London': {
        'zh': '伦敦时间',
        'en': 'London Time',
        'ja': 'ロンドン時間',
        'ko': '런던 시간',
        'fr': 'Heure de Londres',
        'de': 'London-Zeit',
        'es': 'Hora de Londres',
        'ru': 'Лондонское время'
      },
      'America/New_York': {
        'zh': '纽约时间',
        'en': 'New York Time',
        'ja': 'ニューヨーク時間',
        'ko': '뉴욕 시간',
        'fr': 'Heure de New York',
        'de': 'New York-Zeit',
        'es': 'Hora de Nueva York',
        'ru': 'Нью-Йоркское время'
      },
      'Asia/Tokyo': {
        'zh': '东京时间',
        'en': 'Tokyo Time',
        'ja': '東京時間',
        'ko': '도쿄 시간',
        'fr': 'Heure de Tokyo',
        'de': 'Tokio-Zeit',
        'es': 'Hora de Tokio',
        'ru': 'Токийское время'
      },
      'Europe/Paris': {
        'zh': '巴黎时间',
        'en': 'Paris Time',
        'ja': 'パリ時間',
        'ko': '파리 시간',
        'fr': 'Heure de Paris',
        'de': 'Paris-Zeit',
        'es': 'Hora de París',
        'ru': 'Парижское время'
      }
    }

    const names = timezoneNames[timezone]
    if (names && names[locale]) {
      return names[locale]
    }

    // 如果没有找到友好名称，返回时区ID
    return timezone
  }

  const time = getTimeForTimezone(cityDetail.timezone)
  const sunTimes = getSunTimes(cityDetail)

  // 获取用户设置的默认时区，如果没有设置则使用北京时间
  const userDefaultTimezone = isClient ? LocalStorageService.getDefaultTimezone() || 'Asia/Shanghai' : 'Asia/Shanghai'
  const timeDiff = getTimeDifferenceWithReference(cityDetail.timezone, userDefaultTimezone)

  // 生成面包屑导航数据
  const breadcrumbs = [
    {
      name: t('home'),
      url: `https://worldtimeapp.online/${locale === 'en' ? '' : locale + '/'}`
    },
    {
      name: getLocalizedCountryName(cityDetail),
      url: `https://worldtimeapp.online/${locale === 'en' ? '' : locale + '/'}?country=${encodeURIComponent(getLocalizedCountryName(cityDetail))}`
    },
    {
      name: getLocalizedCityName(cityDetail),
      url: `https://worldtimeapp.online/${locale === 'en' ? '' : locale + '/'}city/${cityDetail.id}`
    }
  ]

  // 如果还没有客户端渲染或i18n未初始化，显示加载状态
  if (!isClient || !isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 结构化数据 */}
      <CityPageStructuredData
        city={cityDetail}
        locale={locale}
        breadcrumbs={breadcrumbs}
      />

      {/* Notification */}
      {showNotification && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <Check className="w-4 h-4" />
          {notificationMessage}
        </div>
      )}

      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <Link href={`/${locale}`} className="text-xl font-semibold text-gray-800 hover:text-blue-600">
                  WorldTimeApp
                </Link>
              </div>

              {/* Search Bar */}
              <div className="relative ml-8">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder={t('searchPlaceholder')}
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    onFocus={() => searchQuery && setShowSearchResults(true)}
                    onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
                    className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Search Results */}
                {showSearchResults && searchResults.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
                    {searchResults.map((city) => (
                      <Link
                        key={city.id}
                        href={`/${locale}/city/${city.id}`}
                        className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                      >
                        <span className="text-lg">{city.flag}</span>
                        <div>
                          <div className="font-medium text-gray-800">
                            {getLocalizedCityName(city)}
                          </div>
                          <div className="text-sm text-gray-600">
                            {getLocalizedCountryName(city)}
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            // 添加到主页逻辑
                          }}
                          className="ml-auto p-1 hover:bg-gray-200 rounded-full transition-colors"
                        >
                          <Plus className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <LanguageSwitchI18n />
              <Link href={`/${locale}/faq`} className="text-gray-600 hover:text-gray-800 text-sm">
                {t('faq')}
              </Link>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden">
            {/* Top Row: Logo and Action Buttons */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <Link href={`/${locale}`} className="text-lg font-semibold text-gray-800 hover:text-blue-600">
                  WTA
                </Link>
              </div>

              <div className="flex items-center gap-2">
                <LanguageSwitchI18n />
                <Link
                  href={`/${locale}/faq`}
                  className="text-gray-600 hover:text-gray-800 text-sm px-2 py-1"
                >
                  FAQ
                </Link>
              </div>
            </div>

            {/* Bottom Row: Search Bar */}
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder={t('searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  onFocus={() => searchQuery && setShowSearchResults(true)}
                  onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Search Results */}
              {showSearchResults && searchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
                  {searchResults.map((city) => (
                    <Link
                      key={city.id}
                      href={`/${locale}/city/${city.id}`}
                      className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                    >
                      <span className="text-lg">{city.flag}</span>
                      <div>
                        <div className="font-medium text-gray-800">
                          {getLocalizedCityName(city)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {getLocalizedCountryName(city)}
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          // 添加到主页逻辑
                        }}
                        className="ml-auto p-1 hover:bg-gray-200 rounded-full transition-colors"
                      >
                        <Plus className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                      </button>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="bg-gray-100 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <nav className="flex items-center gap-2 text-sm text-gray-600">
            <Link href={`/${locale}`} className="hover:text-blue-600 transition-colors">
              {t('home')}
            </Link>
            <ChevronRight className="w-4 h-4" />
            <button 
              onClick={() => handleBreadcrumbClick('country', locale === "zh" ? cityDetail.country : cityDetail.countryEn)}
              className="hover:text-blue-600 transition-colors"
            >
              {getLocalizedCountryName(cityDetail)}
            </button>
            <ChevronRight className="w-4 h-4" />
            <span className="text-gray-800 font-medium">{getLocalizedCityName(cityDetail)}</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-4 md:py-8">
        {/* City Title */}
        <div className="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6 md:mb-8">
          <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-500 rounded-full"></div>
          <h1 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-800">
            {getLocalizedCountryName(cityDetail)}，
            {getLocalizedCityName(cityDetail)}
          </h1>
        </div>

        {/* Main Clock Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 mb-4 sm:mb-6 md:mb-8">
          {/* Large Clock */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6 md:p-8 text-center">
              <h2 className="text-sm sm:text-base md:text-lg text-gray-600 mb-1 sm:mb-2">{t('currentLocalTime')}</h2>
              <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-800 mb-1">
                {getLocalizedCityName(cityDetail)}
              </h3>
              <p className="text-sm sm:text-base text-gray-600 mb-2 sm:mb-3 md:mb-4">{getLocalizedCountryName(cityDetail)}</p>
              <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 md:mb-6">
                {time.date}，{time.weekday}
              </p>
              <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-3 sm:mb-4">
                {isClient ? time.time : '--:--:--'}
              </div>
              <div className="text-gray-600 mb-4 sm:mb-6">
                <p className="mb-1 text-sm sm:text-base">{timeDiff}</p>
                {userDefaultTimezone !== 'Asia/Shanghai' && (
                  <p className="text-xs sm:text-sm text-gray-500">
                    {t('defaultTimezoneLabel')}: {getTimezoneFriendlyName(userDefaultTimezone)}
                  </p>
                )}
              </div>
              
              {/* Action Buttons */}
              <div className="flex justify-center gap-2 sm:gap-3 md:gap-4 flex-wrap">
                <button
                  onClick={handleAddToHomepage}
                  className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 rounded-lg transition-colors text-xs sm:text-sm ${
                    isAddedToHomepage
                      ? 'bg-green-100 text-green-700 border border-green-300'
                      : 'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'
                  }`}
                >
                  {isAddedToHomepage ? <Check className="w-3 h-3 sm:w-4 sm:h-4" /> : <Plus className="w-3 h-3 sm:w-4 sm:h-4" />}
                  <span className="hidden sm:inline">{isAddedToHomepage ? t('addedToHomepage') : t('addToHomepage')}</span>
                  <span className="sm:hidden">{isAddedToHomepage ? 'Added' : 'Add'}</span>
                </button>

                <button
                  onClick={handleSetDefaultTimezone}
                  className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 rounded-lg transition-colors text-xs sm:text-sm ${
                    isDefaultTimezone
                      ? 'bg-green-100 text-green-700 border border-green-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  <Globe className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">{isDefaultTimezone ? t('defaultTimezone') : t('setAsDefaultTimezone')}</span>
                  <span className="sm:hidden">{isDefaultTimezone ? 'Default' : 'Set Default'}</span>
                </button>

                <a
                  href={`/${locale}/compare/`}
                  className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 rounded-lg transition-colors bg-purple-100 text-purple-700 border border-purple-300 hover:bg-purple-200 text-xs sm:text-sm"
                >
                  <BarChart3 className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">{t('compareTitle')}</span>
                  <span className="sm:hidden">Compare</span>
                </a>
              </div>
            </div>
          </div>

          {/* City Info */}
          <div className="space-y-3 sm:space-y-4 md:space-y-6">
            {/* Basic Info */}
            <div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 md:p-6">
              <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4">{t('cityInfo')}</h3>
              <div className="space-y-2 sm:space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">{t('timezone')}:</span>
                  <span className="font-medium text-sm sm:text-base truncate ml-2">{cityDetail.timezone}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">{t('utcOffset')}:</span>
                  <span className="font-medium text-sm sm:text-base">{cityDetail.utcOffset}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">{t('coordinates')}:</span>
                  <span className="font-medium text-sm sm:text-base">{cityDetail.latitude}, {cityDetail.longitude}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">{t('dstStatus')}:</span>
                  <span className="font-medium text-xs sm:text-sm truncate ml-2">{locale === "zh" ? cityDetail.dstStatus : cityDetail.dstStatusEn}</span>
                </div>
              </div>
            </div>

            {/* Sun Times */}
            <div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 md:p-6">
              <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center gap-2">
                <Sun className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-500" />
                {t('sunTimes')}
              </h3>
              <div className="space-y-2 sm:space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">{t('sunrise')}:</span>
                  <span className="font-medium text-sm sm:text-base">{sunTimes.sunrise}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">{t('sunset')}:</span>
                  <span className="font-medium text-sm sm:text-base">{sunTimes.sunset}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Cities */}
        {(relatedCities.sameCountryCities.length > 0 || relatedCities.sameTimezoneCities.length > 0 || relatedCities.popularCities.length > 0) && (
          <div className="space-y-4 sm:space-y-6 md:space-y-8">
            {/* Same Country Cities */}
            {relatedCities.sameCountryCities.length > 0 && (
              <div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4">
                  {t('sameCountryCities')} - {getLocalizedCountryName(cityDetail)}
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  {relatedCities.sameCountryCities.map((city) => (
                    <Link
                      key={city.id}
                      href={`/${locale}/city/${city.id}`}
                      className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center gap-2 sm:gap-3 mb-2">
                        <span className="text-lg sm:text-xl">{city.flag}</span>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-gray-800 text-sm sm:text-base truncate">
                            {getLocalizedCityName(city)}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-600 truncate">
                            {city.timezone}
                          </div>
                        </div>
                      </div>
                      <div className="text-base sm:text-lg font-semibold text-gray-800">
                        {getTimeForTimezone(city.timezone).time}
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Same Timezone Cities */}
            {relatedCities.sameTimezoneCities.length > 0 && (
              <div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4">
                  {t('sameTimezoneCities')} - {cityDetail.timezone}
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  {relatedCities.sameTimezoneCities.map((city) => (
                    <Link
                      key={city.id}
                      href={`/${locale}/city/${city.id}`}
                      className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center gap-2 sm:gap-3 mb-2">
                        <span className="text-lg sm:text-xl">{city.flag}</span>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-gray-800 text-sm sm:text-base truncate">
                            {getLocalizedCityName(city)}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-600 truncate">
                            {getLocalizedCountryName(city)}
                          </div>
                        </div>
                      </div>
                      <div className="text-base sm:text-lg font-semibold text-gray-800">
                        {getTimeForTimezone(city.timezone).time}
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Popular Cities */}
            {relatedCities.popularCities.length > 0 && (
              <div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4">
                  {t('popularCities')}
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  {relatedCities.popularCities.map((city) => (
                    <Link
                      key={city.id}
                      href={`/${locale}/city/${city.id}`}
                      className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center gap-2 sm:gap-3 mb-2">
                        <span className="text-lg sm:text-xl">{city.flag}</span>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-gray-800 text-sm sm:text-base truncate">
                            {getLocalizedCityName(city)}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-600 truncate">
                            {getLocalizedCountryName(city)}
                          </div>
                        </div>
                      </div>
                      <div className="text-base sm:text-lg font-semibold text-gray-800">
                        {getTimeForTimezone(city.timezone).time}
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  )
}
