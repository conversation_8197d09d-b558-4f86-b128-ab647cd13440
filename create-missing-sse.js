#!/usr/bin/env node

/**
 * 创建缺失的 server/sse.js 文件
 * 这个脚本会在 npx 缓存目录中创建缺失的文件
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔧 正在修复 @modelcontextprotocol/sdk 缺失的 server/sse.js 文件...\n');

// SSE Server Transport 的完整实现
const sseContent = `// SSE Server Transport for Model Context Protocol
// Auto-generated fix for missing server/sse.js file

import { EventEmitter } from 'events';

export class SSEServerTransport extends EventEmitter {
  constructor(endpoint, response) {
    super();
    this.endpoint = endpoint;
    this.response = response;
    this.sessionId = Math.random().toString(36).substring(7);
    this.closed = false;
  }

  async handleRequest(req, res, body) {
    if (req.method === 'GET') {
      // Handle SSE connection
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control, mcp-session-id',
        'Mcp-Session-Id': this.sessionId
      });
      
      this.response = res;
      
      // Send initial connection event
      this.sendEvent('connection', { sessionId: this.sessionId });
      
      // Handle client disconnect
      req.on('close', () => {
        this.close();
      });
      
      return this;
    } else if (req.method === 'POST') {
      // Handle message
      if (body) {
        this.emit('message', body);
      }
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ status: 'ok' }));
    }
    
    return this;
  }

  sendEvent(type, data) {
    if (this.closed || !this.response) return;
    
    try {
      const eventData = JSON.stringify({ type, data });
      this.response.write(`data: ${ eventData }\\n\\n`);
    } catch (error) {
      console.error('Error sending SSE event:', error);
    }
  }

  send(message) {
    this.sendEvent('message', message);
  }

  close() {
    if (this.closed) return;
    
    this.closed = true;
    if (this.response) {
      try {
        this.response.end();
      } catch (error) {
        // Ignore errors when closing
      }
      this.response = null;
    }
    this.emit('close');
  }
}

// CommonJS compatibility
export default SSEServerTransport;
`;

// 查找 npx 缓存目录
function findNpxCacheDir() {
  const userHome = os.homedir();
  const npmCacheDir = path.join(userHome, 'AppData', 'Local', 'npm-cache', '_npx');

  if (!fs.existsSync(npmCacheDir)) {
    return null;
  }

  // 查找包含 @playwright/mcp 的目录
  const dirs = fs.readdirSync(npmCacheDir);
  for (const dir of dirs) {
    const playwrightMcpPath = path.join(npmCacheDir, dir, 'node_modules', '@playwright', 'mcp');
    if (fs.existsSync(playwrightMcpPath)) {
      return path.join(npmCacheDir, dir, 'node_modules');
    }
  }

  return null;
}

// 查找本地 node_modules
function findLocalNodeModules() {
  const localPath = path.join(process.cwd(), 'node_modules');
  return fs.existsSync(localPath) ? localPath : null;
}

// 创建缺失的文件
function createMissingFile(nodeModulesPath) {
  const sdkPath = path.join(nodeModulesPath, '@modelcontextprotocol', 'sdk');
  const serverDir = path.join(sdkPath, 'server');
  const ssePath = path.join(serverDir, 'sse.js');

  console.log(`检查路径: ${sdkPath}`);

  if (!fs.existsSync(sdkPath)) {
    console.log('❌ @modelcontextprotocol/sdk 不存在');
    return false;
  }

  if (!fs.existsSync(serverDir)) {
    console.log('📁 创建 server 目录...');
    fs.mkdirSync(serverDir, { recursive: true });
  }

  if (fs.existsSync(ssePath)) {
    console.log('✅ server/sse.js 已存在');
    return true;
  }

  console.log('📝 创建 server/sse.js 文件...');
  fs.writeFileSync(ssePath, sseContent);
  console.log('✅ 成功创建 server/sse.js 文件');

  return true;
}

// 主执行逻辑
function main() {
  let fixed = false;

  // 尝试修复 npx 缓存
  const npxCacheDir = findNpxCacheDir();
  if (npxCacheDir) {
    console.log('🔍 找到 npx 缓存目录:', npxCacheDir);
    if (createMissingFile(npxCacheDir)) {
      fixed = true;
    }
  }

  // 尝试修复本地 node_modules
  const localNodeModules = findLocalNodeModules();
  if (localNodeModules) {
    console.log('🔍 找到本地 node_modules:', localNodeModules);
    if (createMissingFile(localNodeModules)) {
      fixed = true;
    }
  }

  if (!fixed) {
    console.log('❌ 未找到需要修复的目录');
    console.log('请先运行: npm install @modelcontextprotocol/sdk');
  } else {
    console.log('\\n🎉 修复完成！现在可以尝试运行:');
    console.log('npx @playwright/mcp@latest --help');
  }
}

main();
