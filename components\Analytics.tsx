"use client"

import React, { useEffect } from 'react'
import Script from 'next/script'
import { usePathname } from 'next/navigation'
import { GA_MEASUREMENT_ID, initAnalytics, trackPageView, trackError } from '@/lib/analytics'

interface AnalyticsProps {
  locale?: string
}

export function Analytics({ locale = 'en' }: AnalyticsProps) {
  const pathname = usePathname()

  // 跟踪页面浏览
  useEffect(() => {
    if (pathname) {
      trackPageView(pathname, document.title, locale)
    }
  }, [pathname, locale])

  // 初始化分析
  useEffect(() => {
    initAnalytics(locale)
  }, [locale])

  // 错误边界
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      trackError(event.message, 'javascript_error', pathname)
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackError(event.reason?.toString() || 'Promise rejection', 'promise_rejection', pathname)
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [pathname])

  // 如果没有 GA ID，不渲染
  if (!GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {
    return null
  }

  return (
    <>
      {/* Google Analytics Script */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
        strategy="afterInteractive"
      />
      
      {/* GA4 Configuration */}
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            // 同意设置
            gtag('consent', 'default', {
              'analytics_storage': 'granted',
              'ad_storage': 'denied',
              'functionality_storage': 'granted',
              'personalization_storage': 'granted',
              'security_storage': 'granted'
            });
            
            // 配置 GA4
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_title: document.title,
              page_location: window.location.href,
              custom_map: {
                'custom_dimension_1': 'language',
                'custom_dimension_2': 'timezone',
                'custom_dimension_3': 'device_type'
              },
              // 增强型测量
              enhanced_measurements: {
                scrolls: true,
                outbound_clicks: true,
                site_search: true,
                video_engagement: true,
                file_downloads: true
              }
            });
            
            // 设置用户属性
            gtag('config', '${GA_MEASUREMENT_ID}', {
              user_properties: {
                language: '${locale}',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                device_type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop'
              }
            });
          `,
        }}
      />
    </>
  )
}

// Cookie 同意横幅组件
export function CookieConsent({ locale = 'en' }: AnalyticsProps) {
  const [showBanner, setShowBanner] = React.useState(false)
  const [consentGiven, setConsentGiven] = React.useState<boolean | null>(null)

  // 多语言文本
  const texts = {
    en: {
      title: "We use cookies",
      description: "We use cookies to improve your experience and analyze site usage. You can manage your preferences below.",
      acceptAll: "Accept All",
      rejectAll: "Reject All",
      customize: "Customize",
      necessary: "Necessary",
      analytics: "Analytics",
      marketing: "Marketing",
      save: "Save Preferences"
    },
    zh: {
      title: "我们使用Cookie",
      description: "我们使用Cookie来改善您的体验并分析网站使用情况。您可以在下方管理您的偏好。",
      acceptAll: "接受全部",
      rejectAll: "拒绝全部",
      customize: "自定义",
      necessary: "必要",
      analytics: "分析",
      marketing: "营销",
      save: "保存偏好"
    },
    ja: {
      title: "Cookieを使用しています",
      description: "体験を向上させ、サイトの使用状況を分析するためにCookieを使用しています。以下で設定を管理できます。",
      acceptAll: "すべて受け入れる",
      rejectAll: "すべて拒否",
      customize: "カスタマイズ",
      necessary: "必要",
      analytics: "分析",
      marketing: "マーケティング",
      save: "設定を保存"
    },
    ko: {
      title: "쿠키를 사용합니다",
      description: "경험을 개선하고 사이트 사용량을 분석하기 위해 쿠키를 사용합니다. 아래에서 기본 설정을 관리할 수 있습니다.",
      acceptAll: "모두 수락",
      rejectAll: "모두 거부",
      customize: "사용자 정의",
      necessary: "필수",
      analytics: "분석",
      marketing: "마케팅",
      save: "기본 설정 저장"
    }
  }

  const t = texts[locale as keyof typeof texts] || texts.en

  useEffect(() => {
    const consent = localStorage.getItem('cookie-consent')
    if (consent === null) {
      setShowBanner(true)
    } else {
      setConsentGiven(consent === 'true')
    }
  }, [])

  const handleAcceptAll = () => {
    setConsentGiven(true)
    setShowBanner(false)
    localStorage.setItem('cookie-consent', 'true')
    
    // 更新 GA 同意设置
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted',
        'ad_storage': 'granted'
      })
    }
  }

  const handleRejectAll = () => {
    setConsentGiven(false)
    setShowBanner(false)
    localStorage.setItem('cookie-consent', 'false')
    
    // 更新 GA 同意设置
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied',
        'ad_storage': 'denied'
      })
    }
  }

  if (!showBanner) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-800 mb-1">{t.title}</h3>
            <p className="text-sm text-gray-600">{t.description}</p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <button
              onClick={handleRejectAll}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              {t.rejectAll}
            </button>
            <button
              onClick={handleAcceptAll}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {t.acceptAll}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// 性能监控组件
export function PerformanceMonitor() {
  useEffect(() => {
    // 监控 Core Web Vitals
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS((metric) => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'CLS',
              value: Math.round(metric.value * 1000)
            })
          }
        })

        getFID((metric) => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'FID',
              value: Math.round(metric.value)
            })
          }
        })

        getFCP((metric) => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'FCP',
              value: Math.round(metric.value)
            })
          }
        })

        getLCP((metric) => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'LCP',
              value: Math.round(metric.value)
            })
          }
        })

        getTTFB((metric) => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'TTFB',
              value: Math.round(metric.value)
            })
          }
        })
      }).catch(() => {
        // web-vitals 库加载失败，忽略
      })
    }
  }, [])

  return null
}
