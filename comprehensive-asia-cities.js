// 全面的亚洲城市数据集
const comprehensiveAsiaCities = [
  // 中国主要城市
  {
    id: 'guangzhou', city: '广州', cityEn: 'Guangzhou', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 23.1291, longitude: 113.2644, sunrise: '06:30', sunset: '18:30',
    population: 15300000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 21, website: 'http://www.gz.gov.cn/', established: '214 BC',
    mayor: '郭永航', gdp: 2873000000000, area: 7434, density: 2058,
    nickname: '花城', nicknameEn: 'Flower City',
    description: '中国南方重要的中心城市，素有"花城"美誉。',
    descriptionEn: 'An important central city in southern China, known as the "Flower City".'
  },
  {
    id: 'shenzhen', city: '深圳', cityEn: 'Shenzhen', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 22.5431, longitude: 114.0579, sunrise: '06:30', sunset: '18:30',
    population: 12590000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 81, website: 'http://www.sz.gov.cn/', established: '1979',
    mayor: '覃伟中', gdp: 3238000000000, area: 1997, density: 6306,
    nickname: '鹏城', nicknameEn: 'Peng City',
    description: '中国改革开放的窗口城市，现代化国际化城市。',
    descriptionEn: 'A window city of China\'s reform and opening-up, a modern international city.'
  },
  {
    id: 'chengdu', city: '成都', cityEn: 'Chengdu', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 30.5728, longitude: 104.0668, sunrise: '07:00', sunset: '19:00',
    population: 16330000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 505, website: 'http://www.chengdu.gov.cn/', established: '316 BC',
    mayor: '王凤朝', gdp: 1991000000000, area: 14335, density: 1139,
    nickname: '天府之国', nicknameEn: 'Land of Abundance',
    description: '四川省省会，西南地区重要的中心城市。',
    descriptionEn: 'Capital of Sichuan Province, an important central city in Southwest China.'
  },
  {
    id: 'hangzhou', city: '杭州', cityEn: 'Hangzhou', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 30.2741, longitude: 120.1551, sunrise: '06:00', sunset: '18:00',
    population: 11940000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 19, website: 'http://www.hangzhou.gov.cn/', established: '589 AD',
    mayor: '姚高员', gdp: 1810000000000, area: 16596, density: 719,
    nickname: '人间天堂', nicknameEn: 'Paradise on Earth',
    description: '浙江省省会，著名的历史文化名城和风景旅游城市。',
    descriptionEn: 'Capital of Zhejiang Province, a famous historical and cultural city and scenic tourist city.'
  },
  {
    id: 'xian', city: '西安', cityEn: 'Xi\'an', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 34.3416, longitude: 108.9398, sunrise: '07:00', sunset: '19:00',
    population: 12950000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 405, website: 'http://www.xa.gov.cn/', established: '1100 BC',
    mayor: '李明远', gdp: 1020000000000, area: 10752, density: 1204,
    nickname: '古都', nicknameEn: 'Ancient Capital',
    description: '陕西省省会，中国历史文化名城，十三朝古都。',
    descriptionEn: 'Capital of Shaanxi Province, a famous historical and cultural city, ancient capital of thirteen dynasties.'
  },
  {
    id: 'nanjing', city: '南京', cityEn: 'Nanjing', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 32.0603, longitude: 118.7969, sunrise: '06:15', sunset: '18:15',
    population: 9280000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 35, website: 'http://www.nanjing.gov.cn/', established: '472 BC',
    mayor: '陈之常', gdp: 1641000000000, area: 6587, density: 1409,
    nickname: '六朝古都', nicknameEn: 'Ancient Capital of Six Dynasties',
    description: '江苏省省会，中国四大古都之一，历史文化名城。',
    descriptionEn: 'Capital of Jiangsu Province, one of China\'s four ancient capitals, a famous historical and cultural city.'
  },
  {
    id: 'wuhan', city: '武汉', cityEn: 'Wuhan', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 30.5928, longitude: 114.3055, sunrise: '06:45', sunset: '18:45',
    population: 11080000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 37, website: 'http://www.wuhan.gov.cn/', established: '1400 BC',
    mayor: '程用文', gdp: 1777000000000, area: 8569, density: 1293,
    nickname: '九省通衢', nicknameEn: 'Thoroughfare of Nine Provinces',
    description: '湖北省省会，中国中部重要的中心城市。',
    descriptionEn: 'Capital of Hubei Province, an important central city in central China.'
  },
  {
    id: 'tianjin', city: '天津', cityEn: 'Tianjin', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 39.1422, longitude: 117.1767, sunrise: '06:30', sunset: '18:30',
    population: 13870000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 5, website: 'http://www.tj.gov.cn/', established: '1404',
    mayor: '张工', gdp: 1407000000000, area: 11966, density: 1159,
    nickname: '津门', nicknameEn: 'Jin Gate',
    description: '中国四大直辖市之一，北方重要的港口城市。',
    descriptionEn: 'One of China\'s four municipalities, an important port city in northern China.'
  },
  {
    id: 'chongqing', city: '重庆', cityEn: 'Chongqing', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 29.5630, longitude: 106.5516, sunrise: '07:15', sunset: '19:15',
    population: 32120000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 259, website: 'http://www.cq.gov.cn/', established: '1189',
    mayor: '胡衡华', gdp: 2502000000000, area: 82403, density: 390,
    nickname: '山城', nicknameEn: 'Mountain City',
    description: '中国四大直辖市之一，西南地区重要的中心城市。',
    descriptionEn: 'One of China\'s four municipalities, an important central city in Southwest China.'
  },
  {
    id: 'qingdao', city: '青岛', cityEn: 'Qingdao', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Shanghai', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 36.0671, longitude: 120.3826, sunrise: '06:00', sunset: '18:00',
    population: 10710000, currency: 'CNY', language: 'Chinese', areaCode: '+86',
    elevation: 25, website: 'http://www.qingdao.gov.cn/', established: '1891',
    mayor: '赵豪志', gdp: 1413000000000, area: 11282, density: 949,
    nickname: '帆船之都', nicknameEn: 'Sailing City',
    description: '山东省重要城市，著名的海滨城市和啤酒城。',
    descriptionEn: 'An important city in Shandong Province, famous seaside city and beer city.'
  },

  // 日本主要城市
  {
    id: 'osaka', city: '大阪', cityEn: 'Osaka', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tokyo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 34.6937, longitude: 135.5023, sunrise: '05:30', sunset: '18:30',
    population: 2690000, currency: 'JPY', language: 'Japanese', areaCode: '+81',
    elevation: 5, website: 'https://www.city.osaka.lg.jp/', established: '645',
    mayor: 'Ichiro Matsui', gdp: 341000000000, area: 225, density: 11958,
    nickname: '天下の台所', nicknameEn: 'Nation\'s Kitchen',
    description: '日本关西地区的经济中心，以美食文化闻名。',
    descriptionEn: 'Economic center of Japan\'s Kansai region, famous for its food culture.'
  },
  {
    id: 'kyoto', city: '京都', cityEn: 'Kyoto', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tokyo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 35.0116, longitude: 135.7681, sunrise: '05:45', sunset: '18:45',
    population: 1460000, currency: 'JPY', language: 'Japanese', areaCode: '+81',
    elevation: 56, website: 'https://www.city.kyoto.lg.jp/', established: '794',
    mayor: 'Daisaku Kadokawa', gdp: 60000000000, area: 827, density: 1765,
    nickname: '千年古都', nicknameEn: 'Ancient Capital',
    description: '日本古都，拥有众多历史文化遗产和传统建筑。',
    descriptionEn: 'Ancient capital of Japan with numerous historical and cultural heritage sites and traditional architecture.'
  },
  {
    id: 'yokohama', city: '横滨', cityEn: 'Yokohama', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tokyo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 35.4437, longitude: 139.6380, sunrise: '05:00', sunset: '18:00',
    population: 3750000, currency: 'JPY', language: 'Japanese', areaCode: '+81',
    elevation: 40, website: 'https://www.city.yokohama.lg.jp/', established: '1859',
    mayor: 'Takeharu Yamanaka', gdp: 158000000000, area: 438, density: 8564,
    nickname: '港湾都市', nicknameEn: 'Port City',
    description: '日本重要的港口城市，东京都市圈的重要组成部分。',
    descriptionEn: 'Important port city of Japan, a key component of the Tokyo metropolitan area.'
  },
  {
    id: 'nagoya', city: '名古屋', cityEn: 'Nagoya', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tokyo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 35.1815, longitude: 136.9066, sunrise: '05:30', sunset: '18:30',
    population: 2330000, currency: 'JPY', language: 'Japanese', areaCode: '+81',
    elevation: 51, website: 'https://www.city.nagoya.jp/', established: '1610',
    mayor: 'Takashi Kawamura', gdp: 156000000000, area: 326, density: 7145,
    nickname: '中京', nicknameEn: 'Chukyo',
    description: '日本中部地区的中心城市，重要的工业城市。',
    descriptionEn: 'Central city of Japan\'s Chubu region, an important industrial city.'
  },
  {
    id: 'sapporo', city: '札幌', cityEn: 'Sapporo', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tokyo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 43.0642, longitude: 141.3469, sunrise: '04:30', sunset: '19:30',
    population: 1950000, currency: 'JPY', language: 'Japanese', areaCode: '+81',
    elevation: 29, website: 'https://www.city.sapporo.jp/', established: '1868',
    mayor: 'Katsuhiro Akimoto', gdp: 70000000000, area: 1121, density: 1740,
    nickname: '雪の都', nicknameEn: 'Snow City',
    description: '北海道的政治经济中心，以雪节和啤酒闻名。',
    descriptionEn: 'Political and economic center of Hokkaido, famous for snow festival and beer.'
  },
  {
    id: 'fukuoka', city: '福冈', cityEn: 'Fukuoka', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tokyo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 33.5904, longitude: 130.4017, sunrise: '06:00', sunset: '19:00',
    population: 1610000, currency: 'JPY', language: 'Japanese', areaCode: '+81',
    elevation: 3, website: 'https://www.city.fukuoka.lg.jp/', established: '1889',
    mayor: 'Soichiro Takashima', gdp: 58000000000, area: 343, density: 4693,
    nickname: '九州の玄関', nicknameEn: 'Gateway to Kyushu',
    description: '九州地区的中心城市，重要的交通枢纽。',
    descriptionEn: 'Central city of Kyushu region, an important transportation hub.'
  },

  // 韩国主要城市
  {
    id: 'seoul', city: '首尔', cityEn: 'Seoul', country: '韩国', countryEn: 'South Korea', flag: '🇰🇷',
    timezone: 'Asia/Seoul', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Seoul', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 37.5665, longitude: 126.9780, sunrise: '05:30', sunset: '19:30',
    population: 9720000, currency: 'KRW', language: 'Korean', areaCode: '+82',
    elevation: 38, website: 'https://www.seoul.go.kr/', established: '18 BC',
    mayor: 'Oh Se-hoon', gdp: 394000000000, area: 605, density: 16061,
    nickname: '한강의 기적', nicknameEn: 'Miracle on the Han River',
    description: '韩国首都，朝鲜半岛最大的城市和经济中心。',
    descriptionEn: 'Capital of South Korea, the largest city and economic center of the Korean Peninsula.'
  },
  {
    id: 'busan', city: '釜山', cityEn: 'Busan', country: '韩国', countryEn: 'South Korea', flag: '🇰🇷',
    timezone: 'Asia/Seoul', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Seoul', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 35.1796, longitude: 129.0756, sunrise: '05:45', sunset: '19:45',
    population: 3450000, currency: 'KRW', language: 'Korean', areaCode: '+82',
    elevation: 30, website: 'https://www.busan.go.kr/', established: '1876',
    mayor: 'Park Heong-joon', gdp: 92000000000, area: 770, density: 4481,
    nickname: '해양수도', nicknameEn: 'Maritime Capital',
    description: '韩国第二大城市，重要的港口城市和海洋城市。',
    descriptionEn: 'Second largest city in South Korea, important port city and maritime city.'
  },
  {
    id: 'incheon', city: '仁川', cityEn: 'Incheon', country: '韩国', countryEn: 'South Korea', flag: '🇰🇷',
    timezone: 'Asia/Seoul', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Seoul', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 37.4563, longitude: 126.7052, sunrise: '05:30', sunset: '19:30',
    population: 2960000, currency: 'KRW', language: 'Korean', areaCode: '+82',
    elevation: 70, website: 'https://www.incheon.go.kr/', established: '1883',
    mayor: 'Yoo Jeong-bok', gdp: 68000000000, area: 1063, density: 2784,
    nickname: '관문도시', nicknameEn: 'Gateway City',
    description: '韩国重要的港口城市，首尔都市圈的重要组成部分。',
    descriptionEn: 'Important port city of South Korea, a key component of the Seoul metropolitan area.'
  },
  {
    id: 'daegu', city: '大邱', cityEn: 'Daegu', country: '韩国', countryEn: 'South Korea', flag: '🇰🇷',
    timezone: 'Asia/Seoul', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Seoul', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 35.8714, longitude: 128.6014, sunrise: '05:45', sunset: '19:45',
    population: 2430000, currency: 'KRW', language: 'Korean', areaCode: '+82',
    elevation: 57, website: 'https://www.daegu.go.kr/', established: '1601',
    mayor: 'Hong Joon-pyo', gdp: 55000000000, area: 884, density: 2749,
    nickname: '섬유의 도시', nicknameEn: 'Textile City',
    description: '韩国第四大城市，重要的纺织工业中心。',
    descriptionEn: 'Fourth largest city in South Korea, important textile industrial center.'
  },

  // 印度主要城市
  {
    id: 'mumbai', city: '孟买', cityEn: 'Mumbai', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kolkata', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 19.0760, longitude: 72.8777, sunrise: '06:30', sunset: '18:30',
    population: 20410000, currency: 'INR', language: 'Hindi, English', areaCode: '+91',
    elevation: 14, website: 'https://www.mcgm.gov.in/', established: '1507',
    mayor: 'Kishori Pednekar', gdp: ************, area: 603, density: 33838,
    nickname: 'बॉलीवुड की राजधानी', nicknameEn: 'Bollywood Capital',
    description: '印度金融首都，宝莱坞电影工业中心。',
    descriptionEn: 'Financial capital of India, center of Bollywood film industry.'
  },
  {
    id: 'delhi', city: '德里', cityEn: 'Delhi', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kolkata', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 28.7041, longitude: 77.1025, sunrise: '06:00', sunset: '18:00',
    population: 32900000, currency: 'INR', language: 'Hindi, English', areaCode: '+91',
    elevation: 216, website: 'https://delhi.gov.in/', established: '1200 BC',
    mayor: 'Arvind Kejriwal', gdp: ************, area: 1484, density: 22169,
    nickname: 'दिल्ली', nicknameEn: 'Heart of India',
    description: '印度首都，国家政治和文化中心。',
    descriptionEn: 'Capital of India, national political and cultural center.'
  },
  {
    id: 'bangalore', city: '班加罗尔', cityEn: 'Bangalore', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kolkata', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 12.9716, longitude: 77.5946, sunrise: '06:15', sunset: '18:15',
    population: 12330000, currency: 'INR', language: 'Hindi, English', areaCode: '+91',
    elevation: 920, website: 'https://www.bbmp.gov.in/', established: '1537',
    mayor: 'Gangambike Mallikarjun', gdp: 110000000000, area: 741, density: 16637,
    nickname: 'सिलिकॉन वैली', nicknameEn: 'Silicon Valley of India',
    description: '印度信息技术中心，被称为"印度硅谷"。',
    descriptionEn: 'Information technology center of India, known as "Silicon Valley of India".'
  },
  {
    id: 'kolkata', city: '加尔各答', cityEn: 'Kolkata', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kolkata', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 22.5726, longitude: 88.3639, sunrise: '05:45', sunset: '17:45',
    population: 14850000, currency: 'INR', language: 'Hindi, English', areaCode: '+91',
    elevation: 17, website: 'https://www.kmcgov.in/', established: '1690',
    mayor: 'Firhad Hakim', gdp: 150000000000, area: 205, density: 72446,
    nickname: 'सांस्कृतिक राजधानी', nicknameEn: 'Cultural Capital',
    description: '印度文化首都，重要的商业和教育中心。',
    descriptionEn: 'Cultural capital of India, important commercial and educational center.'
  },
  {
    id: 'chennai', city: '金奈', cityEn: 'Chennai', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kolkata', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 13.0827, longitude: 80.2707, sunrise: '06:00', sunset: '18:00',
    population: 10970000, currency: 'INR', language: 'Hindi, English', areaCode: '+91',
    elevation: 6, website: 'https://www.chennaicorporation.gov.in/', established: '1639',
    mayor: 'R. Priya', gdp: 78000000000, area: 426, density: 25744,
    nickname: 'दक्षिण भारत का प्रवेश द्वार', nicknameEn: 'Gateway to South India',
    description: '印度南部重要城市，汽车工业中心。',
    descriptionEn: 'Important city in South India, automotive industry center.'
  },
  {
    id: 'hyderabad', city: '海得拉巴', cityEn: 'Hyderabad', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kolkata', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 17.3850, longitude: 78.4867, sunrise: '06:15', sunset: '18:15',
    population: 10010000, currency: 'INR', language: 'Hindi, English', areaCode: '+91',
    elevation: 542, website: 'https://www.ghmc.gov.in/', established: '1591',
    mayor: 'Gadwal Vijayalakshmi', gdp: 74000000000, area: 650, density: 15400,
    nickname: 'सिटी ऑफ पर्ल्स', nicknameEn: 'City of Pearls',
    description: '印度重要的IT和制药中心，被称为"珍珠城"。',
    descriptionEn: 'Important IT and pharmaceutical center of India, known as "City of Pearls".'
  },

  // 东南亚国家城市
  {
    id: 'bangkok', city: '曼谷', cityEn: 'Bangkok', country: '泰国', countryEn: 'Thailand', flag: '🇹🇭',
    timezone: 'Asia/Bangkok', utcOffset: '+07:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Bangkok', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 13.7563, longitude: 100.5018, sunrise: '06:00', sunset: '18:00',
    population: 10540000, currency: 'THB', language: 'Thai', areaCode: '+66',
    elevation: 1, website: 'https://www.bangkok.go.th/', established: '1782',
    mayor: 'Chadchart Sittipunt', gdp: 171000000000, area: 1569, density: 6719,
    nickname: 'กรุงเทพมหานคร', nicknameEn: 'City of Angels',
    description: '泰国首都，东南亚重要的政治、经济、文化中心。',
    descriptionEn: 'Capital of Thailand, important political, economic and cultural center of Southeast Asia.'
  },
  {
    id: 'singapore', city: '新加坡', cityEn: 'Singapore', country: '新加坡', countryEn: 'Singapore', flag: '🇸🇬',
    timezone: 'Asia/Singapore', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Singapore', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 1.3521, longitude: 103.8198, sunrise: '07:00', sunset: '19:00',
    population: 5890000, currency: 'SGD', language: 'English, Malay, Chinese', areaCode: '+65',
    elevation: 15, website: 'https://www.gov.sg/', established: '1819',
    mayor: 'Lee Hsien Loong', gdp: 372000000000, area: 719, density: 8192,
    nickname: '狮城', nicknameEn: 'Lion City',
    description: '东南亚金融中心，现代化的城市国家。',
    descriptionEn: 'Financial center of Southeast Asia, a modern city-state.'
  },
  {
    id: 'kuala-lumpur', city: '吉隆坡', cityEn: 'Kuala Lumpur', country: '马来西亚', countryEn: 'Malaysia', flag: '🇲🇾',
    timezone: 'Asia/Kuala_Lumpur', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kuala_Lumpur', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 3.1390, longitude: 101.6869, sunrise: '07:15', sunset: '19:15',
    population: 1780000, currency: 'MYR', language: 'Malay, English', areaCode: '+60',
    elevation: 66, website: 'https://www.dbkl.gov.my/', established: '1857',
    mayor: 'Mahadi Che Ngah', gdp: 73000000000, area: 243, density: 7327,
    nickname: 'KL', nicknameEn: 'KL',
    description: '马来西亚首都，东南亚重要的商业和金融中心。',
    descriptionEn: 'Capital of Malaysia, important commercial and financial center of Southeast Asia.'
  },
  {
    id: 'jakarta', city: '雅加达', cityEn: 'Jakarta', country: '印度尼西亚', countryEn: 'Indonesia', flag: '🇮🇩',
    timezone: 'Asia/Jakarta', utcOffset: '+07:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Jakarta', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: -6.2088, longitude: 106.8456, sunrise: '06:00', sunset: '18:00',
    population: 10770000, currency: 'IDR', language: 'Indonesian', areaCode: '+62',
    elevation: 8, website: 'https://jakarta.go.id/', established: '1527',
    mayor: 'Anies Baswedan', gdp: 171000000000, area: 664, density: 16225,
    nickname: 'Big Durian', nicknameEn: 'Big Durian',
    description: '印度尼西亚首都，东南亚最大的城市之一。',
    descriptionEn: 'Capital of Indonesia, one of the largest cities in Southeast Asia.'
  },
  {
    id: 'manila', city: '马尼拉', cityEn: 'Manila', country: '菲律宾', countryEn: 'Philippines', flag: '🇵🇭',
    timezone: 'Asia/Manila', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Manila', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 14.5995, longitude: 120.9842, sunrise: '06:00', sunset: '18:00',
    population: 13480000, currency: 'PHP', language: 'Filipino, English', areaCode: '+63',
    elevation: 16, website: 'https://manila.gov.ph/', established: '1571',
    mayor: 'Isko Moreno', gdp: 86000000000, area: 43, density: 313488,
    nickname: 'Pearl of the Orient', nicknameEn: 'Pearl of the Orient',
    description: '菲律宾首都，东南亚重要的港口城市。',
    descriptionEn: 'Capital of Philippines, important port city of Southeast Asia.'
  },
  {
    id: 'ho-chi-minh-city', city: '胡志明市', cityEn: 'Ho Chi Minh City', country: '越南', countryEn: 'Vietnam', flag: '🇻🇳',
    timezone: 'Asia/Ho_Chi_Minh', utcOffset: '+07:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Ho_Chi_Minh', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 10.8231, longitude: 106.6297, sunrise: '06:00', sunset: '18:00',
    population: 9000000, currency: 'VND', language: 'Vietnamese', areaCode: '+84',
    elevation: 19, website: 'https://www.hochiminhcity.gov.vn/', established: '1698',
    mayor: 'Phan Van Mai', gdp: 55000000000, area: 2061, density: 4368,
    nickname: 'Saigon', nicknameEn: 'Saigon',
    description: '越南最大城市，重要的经济中心。',
    descriptionEn: 'Largest city of Vietnam, important economic center.'
  },
  {
    id: 'hanoi', city: '河内', cityEn: 'Hanoi', country: '越南', countryEn: 'Vietnam', flag: '🇻🇳',
    timezone: 'Asia/Ho_Chi_Minh', utcOffset: '+07:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Ho_Chi_Minh', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 21.0285, longitude: 105.8542, sunrise: '06:00', sunset: '18:00',
    population: 8050000, currency: 'VND', language: 'Vietnamese', areaCode: '+84',
    elevation: 12, website: 'https://hanoi.gov.vn/', established: '1010',
    mayor: 'Chu Ngoc Anh', gdp: 44000000000, area: 3359, density: 2398,
    nickname: 'Thang Long', nicknameEn: 'Ascending Dragon',
    description: '越南首都，政治和文化中心。',
    descriptionEn: 'Capital of Vietnam, political and cultural center.'
  },

  // 中亚和西亚城市
  {
    id: 'almaty', city: '阿拉木图', cityEn: 'Almaty', country: '哈萨克斯坦', countryEn: 'Kazakhstan', flag: '🇰🇿',
    timezone: 'Asia/Almaty', utcOffset: '+06:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Almaty', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 43.2220, longitude: 76.8512, sunrise: '06:30', sunset: '18:30',
    population: 1917000, currency: 'KZT', language: 'Kazakh, Russian', areaCode: '+7',
    elevation: 772, website: 'https://almaty.gov.kz/', established: '1854',
    mayor: 'Yerbolat Dossayev', gdp: 31000000000, area: 682, density: 2810,
    nickname: 'Южная столица', nicknameEn: 'Southern Capital',
    description: '哈萨克斯坦最大城市，重要的金融和文化中心。',
    descriptionEn: 'Largest city of Kazakhstan, important financial and cultural center.'
  },
  {
    id: 'tashkent', city: '塔什干', cityEn: 'Tashkent', country: '乌兹别克斯坦', countryEn: 'Uzbekistan', flag: '🇺🇿',
    timezone: 'Asia/Tashkent', utcOffset: '+05:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tashkent', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 41.2995, longitude: 69.2401, sunrise: '06:00', sunset: '18:00',
    population: 2570000, currency: 'UZS', language: 'Uzbek, Russian', areaCode: '+998',
    elevation: 455, website: 'https://tashkent.uz/', established: '595',
    mayor: 'Jahongir Artikhodjayev', gdp: 21000000000, area: 334, density: 7695,
    nickname: 'Bosh shahar', nicknameEn: 'Capital City',
    description: '乌兹别克斯坦首都，中亚重要的政治和经济中心。',
    descriptionEn: 'Capital of Uzbekistan, important political and economic center of Central Asia.'
  },
  {
    id: 'tehran', city: '德黑兰', cityEn: 'Tehran', country: '伊朗', countryEn: 'Iran', flag: '🇮🇷',
    timezone: 'Asia/Tehran', utcOffset: '+03:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Tehran', dstStatus: '实行夏令时', dstStatusEn: 'DST observed',
    latitude: 35.6892, longitude: 51.3890, sunrise: '06:15', sunset: '18:15',
    population: 8694000, currency: 'IRR', language: 'Persian', areaCode: '+98',
    elevation: 1200, website: 'https://tehran.ir/', established: '3000 BC',
    mayor: 'Alireza Zakani', gdp: 74000000000, area: 686, density: 12676,
    nickname: 'تهران', nicknameEn: 'Tehran',
    description: '伊朗首都，中东重要的政治和经济中心。',
    descriptionEn: 'Capital of Iran, important political and economic center of Middle East.'
  },
  {
    id: 'istanbul', city: '伊斯坦布尔', cityEn: 'Istanbul', country: '土耳其', countryEn: 'Turkey', flag: '🇹🇷',
    timezone: 'Europe/Istanbul', utcOffset: '+03:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Europe/Istanbul', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 41.0082, longitude: 28.9784, sunrise: '06:30', sunset: '18:30',
    population: 15460000, currency: 'TRY', language: 'Turkish', areaCode: '+90',
    elevation: 39, website: 'https://www.ibb.istanbul/', established: '660 BC',
    mayor: 'Ekrem İmamoğlu', gdp: 249000000000, area: 5461, density: 2831,
    nickname: 'İstanbul', nicknameEn: 'City of Two Continents',
    description: '土耳其最大城市，连接欧亚两大洲的桥梁。',
    descriptionEn: 'Largest city of Turkey, bridge connecting Europe and Asia.'
  },

  // 其他重要亚洲城市
  {
    id: 'kabul', city: '喀布尔', cityEn: 'Kabul', country: '阿富汗', countryEn: 'Afghanistan', flag: '🇦🇫',
    timezone: 'Asia/Kabul', utcOffset: '+04:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kabul', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 34.5553, longitude: 69.2075, sunrise: '06:00', sunset: '18:00',
    population: 4220000, currency: 'AFN', language: 'Dari, Pashto', areaCode: '+93',
    elevation: 1790, website: 'https://kabul.gov.af/', established: '1500 BC',
    mayor: 'Hamdullah Nomani', gdp: 3000000000, area: 1028, density: 4105,
    nickname: 'کابل', nicknameEn: 'Heart of Asia',
    description: '阿富汗首都，历史悠久的古城。',
    descriptionEn: 'Capital of Afghanistan, an ancient city with a long history.'
  },
  {
    id: 'dhaka', city: '达卡', cityEn: 'Dhaka', country: '孟加拉国', countryEn: 'Bangladesh', flag: '🇧🇩',
    timezone: 'Asia/Dhaka', utcOffset: '+06:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Dhaka', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 23.8103, longitude: 90.4125, sunrise: '06:00', sunset: '18:00',
    population: 9540000, currency: 'BDT', language: 'Bengali', areaCode: '+880',
    elevation: 6, website: 'https://www.dncc.gov.bd/', established: '1608',
    mayor: 'Atiqul Islam', gdp: 78000000000, area: 306, density: 31167,
    nickname: 'ঢাকা', nicknameEn: 'City of Mosques',
    description: '孟加拉国首都，南亚重要的商业中心。',
    descriptionEn: 'Capital of Bangladesh, important commercial center of South Asia.'
  },
  {
    id: 'colombo', city: '科伦坡', cityEn: 'Colombo', country: '斯里兰卡', countryEn: 'Sri Lanka', flag: '🇱🇰',
    timezone: 'Asia/Colombo', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Colombo', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 6.9271, longitude: 79.8612, sunrise: '06:00', sunset: '18:00',
    population: 753000, currency: 'LKR', language: 'Sinhala, Tamil', areaCode: '+94',
    elevation: 1, website: 'https://www.colombo.mc.gov.lk/', established: '1505',
    mayor: 'Rosy Senanayake', gdp: 8000000000, area: 37, density: 20351,
    nickname: 'කොළඹ', nicknameEn: 'Commercial Capital',
    description: '斯里兰卡商业首都，重要的港口城市。',
    descriptionEn: 'Commercial capital of Sri Lanka, important port city.'
  },
  {
    id: 'kathmandu', city: '加德满都', cityEn: 'Kathmandu', country: '尼泊尔', countryEn: 'Nepal', flag: '🇳🇵',
    timezone: 'Asia/Kathmandu', utcOffset: '+05:45', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Kathmandu', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 27.7172, longitude: 85.3240, sunrise: '06:00', sunset: '18:00',
    population: 1003000, currency: 'NPR', language: 'Nepali', areaCode: '+977',
    elevation: 1400, website: 'https://www.kathmandu.gov.np/', established: '723',
    mayor: 'Balendra Shah', gdp: 4000000000, area: 49, density: 20469,
    nickname: 'काठमाडौं', nicknameEn: 'City of Temples',
    description: '尼泊尔首都，喜马拉雅山脚下的古城。',
    descriptionEn: 'Capital of Nepal, ancient city at the foot of the Himalayas.'
  },
  {
    id: 'thimphu', city: '廷布', cityEn: 'Thimphu', country: '不丹', countryEn: 'Bhutan', flag: '🇧🇹',
    timezone: 'Asia/Thimphu', utcOffset: '+06:00', continent: '亚洲', continentEn: 'Asia',
    iana: 'Asia/Thimphu', dstStatus: '不实行夏令时', dstStatusEn: 'No DST',
    latitude: 27.4728, longitude: 89.6393, sunrise: '06:00', sunset: '18:00',
    population: 115000, currency: 'BTN', language: 'Dzongkha', areaCode: '+975',
    elevation: 2320, website: 'https://www.thimphu.bt/', established: '1955',
    mayor: 'Kinlay Dorjee', gdp: 500000000, area: 26, density: 4423,
    nickname: 'ཐིམ་ཕུ', nicknameEn: 'Mountain Capital',
    description: '不丹首都，世界上唯一没有红绿灯的首都。',
    descriptionEn: 'Capital of Bhutan, the only capital city in the world without traffic lights.'
  }
];

module.exports = comprehensiveAsiaCities; 