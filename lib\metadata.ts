import type { Metadata } from "next"

// 支持的语言类型
export type Locale = 'en' | 'zh' | 'ja' | 'ko' | 'fr' | 'de' | 'es' | 'ru'

// 语言到地区的映射
export const localeToRegion: Record<Locale, string> = {
  en: 'en_US',
  zh: 'zh_CN',
  ja: 'ja_JP',
  ko: 'ko_KR',
  fr: 'fr_FR',
  de: 'de_DE',
  es: 'es_ES',
  ru: 'ru_RU'
}

// 基础网站信息
export const siteConfig = {
  name: 'WorldTimeApp',
  domain: 'https://worldtimeapp.online',
  defaultLocale: 'en' as Locale,
  locales: ['en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'ru'] as const
}

// 多语言SEO关键词配置
export const seoKeywords: Record<Locale, {
  primary: string[]
  secondary: string[]
  cityRelated: string[]
}> = {
  en: {
    primary: ['world time', 'timezone converter', 'global time', 'time zone'],
    secondary: ['current time', 'time checker', 'world clock', 'timezone tool'],
    cityRelated: ['time in', 'current time in', 'timezone of', 'local time']
  },
  zh: {
    primary: ['世界时间', '时区转换器', '全球时间', '时区查询'],
    secondary: ['当前时间', '时间查询', '世界时钟', '时区工具'],
    cityRelated: ['时间', '当前时间', '时区', '本地时间']
  },
  ja: {
    primary: ['世界時間', 'タイムゾーン変換', 'グローバル時間', 'タイムゾーン'],
    secondary: ['現在時刻', '時間チェッカー', '世界時計', 'タイムゾーンツール'],
    cityRelated: ['時間', '現在時刻', 'タイムゾーン', '現地時間']
  },
  ko: {
    primary: ['세계 시간', '시간대 변환기', '글로벌 시간', '시간대'],
    secondary: ['현재 시간', '시간 확인', '세계 시계', '시간대 도구'],
    cityRelated: ['시간', '현재 시간', '시간대', '현지 시간']
  },
  fr: {
    primary: ['heure mondiale', 'convertisseur fuseau horaire', 'heure globale', 'fuseau horaire'],
    secondary: ['heure actuelle', 'vérificateur heure', 'horloge mondiale', 'outil fuseau'],
    cityRelated: ['heure à', 'heure actuelle à', 'fuseau horaire de', 'heure locale']
  },
  de: {
    primary: ['weltzeit', 'zeitzonenkonverter', 'globale zeit', 'zeitzone'],
    secondary: ['aktuelle zeit', 'zeitprüfer', 'weltuhr', 'zeitzonentool'],
    cityRelated: ['zeit in', 'aktuelle zeit in', 'zeitzone von', 'ortszeit']
  },
  es: {
    primary: ['hora mundial', 'convertidor zona horaria', 'hora global', 'zona horaria'],
    secondary: ['hora actual', 'verificador hora', 'reloj mundial', 'herramienta zona'],
    cityRelated: ['hora en', 'hora actual en', 'zona horaria de', 'hora local']
  },
  ru: {
    primary: ['мировое время', 'конвертер часовых поясов', 'глобальное время', 'часовой пояс'],
    secondary: ['текущее время', 'проверка времени', 'мировые часы', 'инструмент времени'],
    cityRelated: ['время в', 'текущее время в', 'часовой пояс', 'местное время']
  }
}

// 多语言页面元数据配置
export const pageMetadata: Record<string, Record<Locale, {
  title: string
  description: string
  keywords: string
}>> = {
  home: {
    en: {
      title: "World Time - Global Timezone Checker | WorldTimeApp",
      description: "Free global timezone checker tool. Check real-time for major cities worldwide, timezone conversion, daylight saving time information. Accurate display of current time in Beijing, Tokyo, New York, London, Paris and other cities. Multi-language interface supported.",
      keywords: "world time,timezone converter,global time,time zone,beijing time,tokyo time,new york time,london time,paris time,timezone checker"
    },
    zh: {
      title: "世界时间 - 全球时区查询工具 | WorldTimeApp",
      description: "免费的全球时区查询工具。实时查看世界各大城市时间，时区转换，夏令时信息。准确显示北京、东京、纽约、伦敦、巴黎等城市当前时间。支持多语言界面。",
      keywords: "世界时间,时区转换器,全球时间,时区查询,北京时间,东京时间,纽约时间,伦敦时间,巴黎时间,时区工具"
    },
    ja: {
      title: "世界時間 - グローバルタイムゾーンチェッカー | WorldTimeApp",
      description: "無料のグローバルタイムゾーンチェッカーツール。世界の主要都市のリアルタイム時間確認、タイムゾーン変換、夏時間情報。北京、東京、ニューヨーク、ロンドン、パリなどの都市の現在時刻を正確に表示。多言語インターフェース対応。",
      keywords: "世界時間,タイムゾーン変換,グローバル時間,タイムゾーン,北京時間,東京時間,ニューヨーク時間,ロンドン時間,パリ時間,タイムゾーンチェッカー"
    },
    ko: {
      title: "세계 시간 - 글로벌 시간대 확인기 | WorldTimeApp",
      description: "무료 글로벌 시간대 확인 도구. 전 세계 주요 도시의 실시간 시간 확인, 시간대 변환, 일광 절약 시간 정보. 베이징, 도쿄, 뉴욕, 런던, 파리 등 도시의 현재 시간을 정확하게 표시. 다국어 인터페이스 지원.",
      keywords: "세계 시간,시간대 변환기,글로벌 시간,시간대,베이징 시간,도쿄 시간,뉴욕 시간,런던 시간,파리 시간,시간대 확인기"
    },
    fr: {
      title: "Heure Mondiale - Vérificateur de Fuseau Horaire Global | WorldTimeApp",
      description: "Outil gratuit de vérification des fuseaux horaires mondiaux. Vérifiez l'heure en temps réel des grandes villes du monde, conversion de fuseau horaire, informations sur l'heure d'été. Affichage précis de l'heure actuelle à Pékin, Tokyo, New York, Londres, Paris et autres villes. Interface multilingue prise en charge.",
      keywords: "heure mondiale,convertisseur fuseau horaire,heure globale,fuseau horaire,heure pékin,heure tokyo,heure new york,heure londres,heure paris,vérificateur fuseau"
    },
    de: {
      title: "Weltzeit - Globaler Zeitzonenprüfer | WorldTimeApp",
      description: "Kostenloses globales Zeitzonenprüfungstool. Überprüfen Sie die Echtzeit für große Städte weltweit, Zeitzonenkonvertierung, Sommerzeitinformationen. Genaue Anzeige der aktuellen Zeit in Peking, Tokio, New York, London, Paris und anderen Städten. Mehrsprachige Benutzeroberfläche unterstützt.",
      keywords: "weltzeit,zeitzonenkonverter,globale zeit,zeitzone,peking zeit,tokio zeit,new york zeit,london zeit,paris zeit,zeitzonenprüfer"
    },
    es: {
      title: "Hora Mundial - Verificador de Zona Horaria Global | WorldTimeApp",
      description: "Herramienta gratuita de verificación de zonas horarias globales. Verifique el tiempo real de las principales ciudades del mundo, conversión de zona horaria, información de horario de verano. Visualización precisa de la hora actual en Beijing, Tokio, Nueva York, Londres, París y otras ciudades. Interfaz multiidioma compatible.",
      keywords: "hora mundial,convertidor zona horaria,hora global,zona horaria,hora beijing,hora tokio,hora nueva york,hora londres,hora paris,verificador zona"
    },
    ru: {
      title: "Мировое Время - Глобальный Проверщик Часовых Поясов | WorldTimeApp",
      description: "Бесплатный инструмент проверки глобальных часовых поясов. Проверяйте время в реальном времени для крупных городов мира, конвертация часовых поясов, информация о летнем времени. Точное отображение текущего времени в Пекине, Токио, Нью-Йорке, Лондоне, Париже и других городах. Поддержка многоязычного интерфейса.",
      keywords: "мировое время,конвертер часовых поясов,глобальное время,часовой пояс,время пекин,время токио,время нью-йорк,время лондон,время париж,проверщик поясов"
    }
  },
  faq: {
    en: {
      title: "FAQ - Frequently Asked Questions | WorldTimeApp",
      description: "Get answers to frequently asked questions about WorldTimeApp timezone checker. Learn how to use our global time tool effectively.",
      keywords: "faq,help,timezone questions,world time help,time zone guide,timezone converter help"
    },
    zh: {
      title: "常见问题 - FAQ | WorldTimeApp",
      description: "获取关于WorldTimeApp时区查询工具的常见问题解答。了解如何有效使用我们的全球时间工具。",
      keywords: "常见问题,帮助,时区问题,世界时间帮助,时区指南,时区转换器帮助"
    },
    ja: {
      title: "FAQ - よくある質問 | WorldTimeApp",
      description: "WorldTimeAppタイムゾーンチェッカーについてのよくある質問の回答を取得。グローバル時間ツールを効果的に使用する方法を学習。",
      keywords: "faq,ヘルプ,タイムゾーン質問,世界時間ヘルプ,タイムゾーンガイド,タイムゾーン変換ヘルプ"
    },
    ko: {
      title: "FAQ - 자주 묻는 질문 | WorldTimeApp",
      description: "WorldTimeApp 시간대 확인기에 대한 자주 묻는 질문의 답변을 얻으세요. 글로벌 시간 도구를 효과적으로 사용하는 방법을 배우세요.",
      keywords: "faq,도움말,시간대 질문,세계 시간 도움말,시간대 가이드,시간대 변환기 도움말"
    },
    fr: {
      title: "FAQ - Questions Fréquemment Posées | WorldTimeApp",
      description: "Obtenez des réponses aux questions fréquemment posées sur le vérificateur de fuseau horaire WorldTimeApp. Apprenez à utiliser efficacement notre outil de temps global.",
      keywords: "faq,aide,questions fuseau horaire,aide heure mondiale,guide fuseau horaire,aide convertisseur fuseau"
    },
    de: {
      title: "FAQ - Häufig Gestellte Fragen | WorldTimeApp",
      description: "Erhalten Sie Antworten auf häufig gestellte Fragen zum WorldTimeApp Zeitzonenprüfer. Lernen Sie, wie Sie unser globales Zeittool effektiv nutzen.",
      keywords: "faq,hilfe,zeitzone fragen,weltzeit hilfe,zeitzone anleitung,zeitzonenkonverter hilfe"
    },
    es: {
      title: "FAQ - Preguntas Frecuentes | WorldTimeApp",
      description: "Obtenga respuestas a preguntas frecuentes sobre el verificador de zona horaria WorldTimeApp. Aprenda a usar nuestra herramienta de tiempo global de manera efectiva.",
      keywords: "faq,ayuda,preguntas zona horaria,ayuda hora mundial,guía zona horaria,ayuda convertidor zona"
    },
    ru: {
      title: "FAQ - Часто Задаваемые Вопросы | WorldTimeApp",
      description: "Получите ответы на часто задаваемые вопросы о проверщике часовых поясов WorldTimeApp. Узнайте, как эффективно использовать наш глобальный инструмент времени.",
      keywords: "faq,помощь,вопросы часовой пояс,помощь мировое время,руководство часовой пояс,помощь конвертер поясов"
    }
  },
  'api-demo': {
    en: {
      title: "API Demo - WorldTimeApp API Documentation | WorldTimeApp",
      description: "Explore WorldTimeApp API demo and documentation. Learn how to integrate our timezone API into your applications.",
      keywords: "api demo,timezone api,world time api,api documentation,developer tools,timezone integration"
    },
    zh: {
      title: "API演示 - WorldTimeApp API文档 | WorldTimeApp",
      description: "探索WorldTimeApp API演示和文档。了解如何将我们的时区API集成到您的应用程序中。",
      keywords: "api演示,时区api,世界时间api,api文档,开发者工具,时区集成"
    },
    ja: {
      title: "APIデモ - WorldTimeApp APIドキュメント | WorldTimeApp",
      description: "WorldTimeApp APIデモとドキュメントを探索。タイムゾーンAPIをアプリケーションに統合する方法を学習。",
      keywords: "apiデモ,タイムゾーンapi,世界時間api,apiドキュメント,開発者ツール,タイムゾーン統合"
    },
    ko: {
      title: "API 데모 - WorldTimeApp API 문서 | WorldTimeApp",
      description: "WorldTimeApp API 데모 및 문서를 탐색하세요. 시간대 API를 애플리케이션에 통합하는 방법을 배우세요.",
      keywords: "api 데모,시간대 api,세계 시간 api,api 문서,개발자 도구,시간대 통합"
    },
    fr: {
      title: "Démo API - Documentation API WorldTimeApp | WorldTimeApp",
      description: "Explorez la démo et la documentation de l'API WorldTimeApp. Apprenez à intégrer notre API de fuseau horaire dans vos applications.",
      keywords: "démo api,api fuseau horaire,api heure mondiale,documentation api,outils développeur,intégration fuseau"
    },
    de: {
      title: "API Demo - WorldTimeApp API Dokumentation | WorldTimeApp",
      description: "Erkunden Sie die WorldTimeApp API Demo und Dokumentation. Lernen Sie, wie Sie unsere Zeitzone API in Ihre Anwendungen integrieren.",
      keywords: "api demo,zeitzone api,weltzeit api,api dokumentation,entwicklertools,zeitzone integration"
    },
    es: {
      title: "Demo API - Documentación API WorldTimeApp | WorldTimeApp",
      description: "Explore la demo y documentación de la API WorldTimeApp. Aprenda a integrar nuestra API de zona horaria en sus aplicaciones.",
      keywords: "demo api,api zona horaria,api hora mundial,documentación api,herramientas desarrollador,integración zona"
    },
    ru: {
      title: "API Демо - Документация API WorldTimeApp | WorldTimeApp",
      description: "Изучите демо и документацию API WorldTimeApp. Узнайте, как интегрировать наш API часовых поясов в ваши приложения.",
      keywords: "api демо,api часовой пояс,api мировое время,документация api,инструменты разработчика,интеграция поясов"
    }
  }
}

// 生成本地化元数据的主函数
export function generateLocalizedMetadata(
  page: string,
  locale: Locale,
  customData?: {
    title?: string
    description?: string
    keywords?: string[]
    cityName?: string
    countryName?: string
  }
): Metadata {
  const baseConfig = pageMetadata[page]?.[locale] || pageMetadata[page]?.en || pageMetadata.home[locale]
  
  // 如果是城市页面，生成动态元数据
  if (customData?.cityName) {
    return generateCityMetadata(locale, customData.cityName, customData.countryName)
  }
  
  // 所有语言都包含语言前缀，包括默认语言
  const canonicalPath = `/${locale}`
  const pagePath = page === 'home' ? '' : `/${page}`
  const fullPath = `${canonicalPath}${pagePath}`

  return {
    title: customData?.title || baseConfig.title,
    description: customData?.description || baseConfig.description,
    keywords: customData?.keywords?.join(',') || baseConfig.keywords,
    authors: [{ name: "WorldTimeApp Team" }],
    creator: "WorldTimeApp",
    publisher: "WorldTimeApp",
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(siteConfig.domain),
    alternates: {
      canonical: `${siteConfig.domain}${fullPath}`,
      languages: generateLanguageAlternates(pagePath)
    },
    openGraph: {
      type: 'website',
      locale: localeToRegion[locale],
      url: `${siteConfig.domain}${fullPath}`,
      title: customData?.title || baseConfig.title,
      description: customData?.description || baseConfig.description,
      siteName: siteConfig.name,
      images: [
        {
          url: '/timecha-screenshot.png',
          width: 1200,
          height: 630,
          alt: `${siteConfig.name} - Global Timezone Checker Interface`,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: customData?.title || baseConfig.title,
      description: customData?.description || baseConfig.description,
      images: ['/timecha-screenshot.png'],
      creator: '@WorldTimeApp',
      site: '@WorldTimeApp',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  }
}

// 生成城市页面元数据
export function generateCityMetadata(locale: Locale, cityName: string, countryName?: string, cityId?: string): Metadata {
  const keywords = seoKeywords[locale]
  const cityKeywords = keywords.cityRelated.map(keyword => `${keyword} ${cityName}`).join(',')
  const allKeywords = [...keywords.primary, ...keywords.secondary, cityName, countryName].filter(Boolean).join(',')
  
  const titles: Record<Locale, string> = {
    en: `Current Time in ${cityName} ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    zh: `${cityName}当前时间 ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    ja: `${cityName}の現在時刻 ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    ko: `${cityName} 현재 시간 ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    fr: `Heure Actuelle à ${cityName} ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    de: `Aktuelle Zeit in ${cityName} ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    es: `Hora Actual en ${cityName} ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`,
    ru: `Текущее Время в ${cityName} ${countryName ? `- ${countryName}` : ''} | WorldTimeApp`
  }
  
  const descriptions: Record<Locale, string> = {
    en: `Check current local time in ${cityName}${countryName ? `, ${countryName}` : ''}. Get accurate timezone information, UTC offset, daylight saving time status, sunrise and sunset times.`,
    zh: `查看${cityName}${countryName ? `，${countryName}` : ''}的当前本地时间。获取准确的时区信息、UTC偏移、夏令时状态、日出日落时间。`,
    ja: `${cityName}${countryName ? `、${countryName}` : ''}の現在の現地時間を確認。正確なタイムゾーン情報、UTCオフセット、夏時間ステータス、日の出日の入り時刻を取得。`,
    ko: `${cityName}${countryName ? `, ${countryName}` : ''}의 현재 현지 시간을 확인하세요. 정확한 시간대 정보, UTC 오프셋, 일광 절약 시간 상태, 일출 일몰 시간을 얻으세요.`,
    fr: `Vérifiez l'heure locale actuelle à ${cityName}${countryName ? `, ${countryName}` : ''}. Obtenez des informations précises sur le fuseau horaire, le décalage UTC, le statut de l'heure d'été, les heures de lever et coucher du soleil.`,
    de: `Überprüfen Sie die aktuelle Ortszeit in ${cityName}${countryName ? `, ${countryName}` : ''}. Erhalten Sie genaue Zeitzoneninfos, UTC-Versatz, Sommerzeitstatus, Sonnenauf- und -untergangszeiten.`,
    es: `Verifique la hora local actual en ${cityName}${countryName ? `, ${countryName}` : ''}. Obtenga información precisa de zona horaria, compensación UTC, estado de horario de verano, horas de amanecer y atardecer.`,
    ru: `Проверьте текущее местное время в ${cityName}${countryName ? `, ${countryName}` : ''}. Получите точную информацию о часовом поясе, смещении UTC, статусе летнего времени, времени восхода и заката.`
  }
  
  // 所有语言都包含语言前缀，包括默认语言
  const canonicalPath = `/${locale}`
  const cityPath = `/city/${cityId || encodeURIComponent(cityName.toLowerCase())}`
  const fullPath = `${canonicalPath}${cityPath}`

  return {
    title: titles[locale],
    description: descriptions[locale],
    keywords: `${allKeywords},${cityKeywords}`,
    authors: [{ name: "WorldTimeApp Team" }],
    creator: "WorldTimeApp",
    publisher: "WorldTimeApp",
    metadataBase: new URL(siteConfig.domain),
    alternates: {
      canonical: `${siteConfig.domain}${fullPath}`,
      languages: generateLanguageAlternates(cityPath)
    },
    openGraph: {
      type: 'website',
      locale: localeToRegion[locale],
      url: `${siteConfig.domain}${fullPath}`,
      title: titles[locale],
      description: descriptions[locale],
      siteName: siteConfig.name,
      images: [
        {
          url: '/timecha-screenshot.png',
          width: 1200,
          height: 630,
          alt: `${cityName} Time - ${siteConfig.name}`,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: titles[locale],
      description: descriptions[locale],
      images: ['/timecha-screenshot.png'],
      creator: '@WorldTimeApp',
      site: '@WorldTimeApp',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  }
}

// 生成语言alternates
export function generateLanguageAlternates(path: string = ''): Record<string, string> {
  const alternates: Record<string, string> = {}
  
  siteConfig.locales.forEach(locale => {
    const localePath = locale === siteConfig.defaultLocale ? '' : `/${locale}`
    alternates[locale] = `${siteConfig.domain}${localePath}${path}`
  })
  
  return alternates
}
