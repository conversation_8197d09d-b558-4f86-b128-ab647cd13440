import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// 翻译资源
const resources = {
  zh: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "首页",
      worldTime: "世界时间",
      searchPlaceholder: "搜索城市或国家...",
      currentLocalTime: "当前本地时间",
      timezone: "时区",
      utcOffset: "UTC偏移",
      coordinates: "坐标",
      dstStatus: "夏令时状态",
      sunrise: "日出",
      sunset: "日落",
      cityInfo: "城市信息",
      population: "人口",
      currency: "货币",
      language: "语言",
      elevation: "海拔",
      area: "面积",
      density: "人口密度",
      nickname: "别名",
      sameAsBeijing: "与北京时间相同",
      sameAsDefault: "与默认时区相同",
      earlierThan: "比北京时间早",
      laterThan: "比北京时间晚",
      earlierThanDefault: "比默认时区早",
      laterThanDefault: "比默认时区晚",
      hours: "小时",
      metropolis: "大都市",
      country: "国家",
      addToHomepage: "添加到主页",
      setAsDefaultTimezone: "设为默认时区",
      officialWebsite: "官方网站",
      cityDescription: "城市介绍",
      quickActions: "快速操作",
      additionalInfo: "更多信息",
      cityNotFound: "未找到城市数据",
      loadingCityData: "正在加载城市数据...",
      backToHome: "返回首页",
      mainTitle: "世界时间查询",
      subtitle: "快速查看全球主要城市的当前时间",
      feature1Title: "实时时间",
      feature1Desc: "准确显示全球各城市的实时时间",
      feature2Title: "时区转换",
      feature2Desc: "轻松进行不同时区之间的时间转换",
      feature3Title: "城市信息",
      feature3Desc: "了解城市的详细信息和时区状态",
      myCities: "我的城市",
      cities: "个城市",
      resetToDefault: "重置为默认",
      cityListSaved: "您的城市列表已自动保存",
      worldMap: "世界地图",
      exploreTimezones: "探索全球时区",
      compareTitle: "城市对比",
      faq: "常见问题",
      beijingTime: "北京时间",
      tokyoTime: "东京时间",
      newYorkTime: "纽约时间",
      // 世界时区图相关
      worldTimezoneMap: "世界时区图",
      clickForDetails: "点击不同的时区区域查看详细信息",
      utcOffsetLabel: "UTC偏移:",
      currentTimeLabel: "当前时间:",
      majorCitiesLabel: "主要城市:",
      timezoneLegend: "时区图例",
      timezoneDisclaimer: "此图为简化的时区示意图，实际时区边界可能因地理和政治因素而有所不同。",
      // 详情页相关
      defaultTimezoneLabel: "默认时区",
      removedFromHomepage: "已从主页移除",
      addedToHomepage: "已添加到主页",
      defaultTimezoneCleared: "已清除默认时区设置",
      setAsDefaultTimezoneMessage: "已设置为默认时区",
      clickToRemoveFromHomepage: "点击从主页移除",
      clickToAddToHomepage: "点击添加到主页",
      clickToClearDefaultTimezone: "点击清除默认时区",
      clickToSetAsDefaultTimezone: "点击设为默认时区",
      addedToHomepageButton: "已添加到主页",
      addToHomepageButton: "添加到主页",
      defaultTimezoneButton: "默认时区",
      setAsDefaultTimezoneButton: "设为默认时区",
      // 时间对比相关
      sameAs: "与",
      sameTime: "时间相同",
      aheadOf: "早",
      behindOf: "晚",
      hoursUnit: "小时",
      referenceTimezone: "参考时区",
      // 主页额外文本
      loadingCityDataMessage: "正在加载城市数据...",
      noCityData: "暂无城市数据",
      trySearchAndAdd: "请尝试搜索并添加城市",
      showing: "正在显示",
      clearFilter: "清除筛选",
      // FAQ页面
      frequentlyAskedQuestions: "常见问题",
      faqDescription: "关于WorldTimeApp时间查询工具的常见问题解答，帮助您更好地使用我们的服务。",
      relatedLinks: "相关链接",
      londonTime: "伦敦时间",
      parisTime: "巴黎时间",
      // 数据库演示页面
      databaseDemo: "数据库演示",
      performanceComparison: "性能对比",
      jsonLoadTime: "JSON加载时间",
      sqliteLoadTime: "SQLite加载时间",
      jsonSearchTime: "JSON搜索时间",
      sqliteSearchTime: "SQLite搜索时间",
      searchCityOrCountry: "搜索城市或国家...",
      jsonFileApproach: "JSON文件方案",
      sqliteApproach: "SQLite方案",
      citiesCount: "个城市",
      simpleAndDirect: "简单直接，无需配置",
      noDataModification: "不支持动态添加数据",
      limitedQuery: "查询功能有限",
      supportsComplexQuery: "支持复杂查询",
      supportsCRUD: "支持动态增删改查",
      supportsIndexOptimization: "支持索引优化",
      addSampleCity: "添加演示城市",
      searchResults: "搜索结果",
      allCities: "所有城市",
      populationLabel: "人口",
      recommendations: "推荐建议",
      recommendSQLite: "推荐使用SQLite，因为：",
      supportMoreCities: "支持更多城市数据（可扩展到数千个）",
      betterQueryPerformance: "查询性能更好（有索引支持）",
      supportUserFeatures: "支持用户收藏、自定义城市等功能",
      supportDynamicCRUD: "支持数据的动态增删改查",
      betterExtensibility: "未来扩展性更强",
      jsonSuitableFor: "JSON方案适合：",
      lessThan100Cities: "城市数量少于100个",
      noNeedDynamicData: "不需要动态添加数据",
      simpleStaticDeployment: "简单的静态部署",
      prototypeDevelopment: "原型开发阶段",
      // SQLite测试页面
      sqliteTestPage: "SQLite 测试页面",
      initializingSQLite: "正在初始化 SQLite 数据库...",
      errorMessage: "错误",
      sqliteInitSuccess: "SQLite 初始化成功!",
      successfullyLoaded: "成功加载了",
      cityData: "城市数据",
      countryLabel: "国家",
      timezoneLabel: "时区",
      returnToHome: "返回主页",
      seoDescription: "支持查询全球主要城市时间：北京时间、东京时间、纽约时间、伦敦时间、巴黎时间、莫斯科时间等。提供准确的时区转换、UTC偏移量显示、夏令时状态查询，是您进行国际商务、旅行规划的理想工具。",
      // 相关城市推荐
      relatedCities: "相关城市",
      sameCountryCities: "同国家城市",
      sameTimezoneCities: "同时区城市",
      popularCities: "热门城市",
      viewCityTime: "查看时间",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 提示：您可以通过搜索添加更多城市到收藏列表",
      searchToAddCities: "搜索城市名称，点击 + 号即可添加",
      customizeYourCities: "个性化您的城市列表"
    }
  },
  en: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "Home",
      worldTime: "World Time",
      searchPlaceholder: "Search cities or countries...",
      currentLocalTime: "Current Local Time",
      timezone: "Timezone",
      utcOffset: "UTC Offset",
      coordinates: "Coordinates",
      dstStatus: "DST Status",
      sunrise: "Sunrise",
      sunset: "Sunset",
      cityInfo: "City Information",
      population: "Population",
      currency: "Currency",
      language: "Language",
      elevation: "Elevation",
      area: "Area",
      density: "Density",
      nickname: "Nickname",
      sameAsBeijing: "Same as Beijing time",
      sameAsDefault: "Same as default timezone",
      earlierThan: "Earlier than Beijing",
      laterThan: "Later than Beijing",
      earlierThanDefault: "Earlier than default timezone",
      laterThanDefault: "Later than default timezone",
      hours: "hours",
      metropolis: "Metropolis",
      country: "Country",
      addToHomepage: "Add to Homepage",
      setAsDefaultTimezone: "Set as Default Timezone",
      officialWebsite: "Official Website",
      cityDescription: "City Description",
      quickActions: "Quick Actions",
      additionalInfo: "Additional Info",
      cityNotFound: "City data not found",
      loadingCityData: "Loading city data...",
      backToHome: "Back to Home",
      mainTitle: "World Time Query",
      subtitle: "Quickly check current time in major cities worldwide",
      feature1Title: "Real-time Clock",
      feature1Desc: "Accurately display real-time in cities worldwide",
      feature2Title: "Timezone Conversion",
      feature2Desc: "Easy time conversion between different timezones",
      feature3Title: "City Information",
      feature3Desc: "Learn detailed information and timezone status of cities",
      myCities: "My Cities",
      cities: "cities",
      resetToDefault: "Reset to Default",
      cityListSaved: "Your city list is automatically saved",
      worldMap: "World Map",
      exploreTimezones: "Explore global timezones",
      compareTitle: "Compare Cities",
      faq: "FAQ",
      beijingTime: "Beijing Time",
      tokyoTime: "Tokyo Time",
      newYorkTime: "New York Time",
      // 世界时区图相关
      worldTimezoneMap: "World Timezone Map",
      clickForDetails: "Click on different timezone regions for details",
      utcOffsetLabel: "UTC Offset:",
      currentTimeLabel: "Current Time:",
      majorCitiesLabel: "Major Cities:",
      timezoneLegend: "Timezone Legend",
      timezoneDisclaimer: "This is a simplified timezone diagram. Actual timezone boundaries may vary due to geographical and political factors.",
      // 详情页相关
      defaultTimezoneLabel: "Default Timezone",
      removedFromHomepage: "removed from homepage",
      addedToHomepage: "added to homepage",
      defaultTimezoneCleared: "Default timezone cleared",
      setAsDefaultTimezoneMessage: "set as default timezone",
      clickToRemoveFromHomepage: "Click to remove from homepage",
      clickToAddToHomepage: "Click to add to homepage",
      clickToClearDefaultTimezone: "Click to clear default timezone",
      clickToSetAsDefaultTimezone: "Click to set as default timezone",
      addedToHomepageButton: "Added to Homepage",
      addToHomepageButton: "Add to Homepage",
      defaultTimezoneButton: "Default Timezone",
      setAsDefaultTimezoneButton: "Set as Default Timezone",
      // 时间对比相关
      sameAs: "Same as",
      sameTime: "",
      aheadOf: "ahead",
      behindOf: "behind",
      hoursUnit: "hours",
      referenceTimezone: "Reference",
      // 主页额外文本
      loadingCityDataMessage: "Loading city data...",
      noCityData: "No city data available",
      trySearchAndAdd: "Try searching and adding cities",
      showing: "Showing",
      clearFilter: "Clear filter",
      // FAQ页面
      frequentlyAskedQuestions: "Frequently Asked Questions",
      faqDescription: "Common questions and answers about WorldTimeApp time query tool to help you better use our services.",
      relatedLinks: "Related Links",
      londonTime: "London Time",
      parisTime: "Paris Time",
      // 数据库演示页面
      databaseDemo: "Database Demo",
      performanceComparison: "Performance Comparison",
      jsonLoadTime: "JSON Load Time",
      sqliteLoadTime: "SQLite Load Time",
      jsonSearchTime: "JSON Search Time",
      sqliteSearchTime: "SQLite Search Time",
      searchCityOrCountry: "Search cities or countries...",
      jsonFileApproach: "JSON File Approach",
      sqliteApproach: "SQLite Approach",
      citiesCount: "cities",
      simpleAndDirect: "Simple and direct, no configuration needed",
      noDataModification: "No support for dynamic data addition",
      limitedQuery: "Limited query functionality",
      supportsComplexQuery: "Supports complex queries",
      supportsCRUD: "Supports dynamic CRUD operations",
      supportsIndexOptimization: "Supports index optimization",
      addSampleCity: "Add Sample City",
      searchResults: "Search Results",
      allCities: "All Cities",
      populationLabel: "Population",
      recommendations: "Recommendations",
      recommendSQLite: "Recommend using SQLite because:",
      supportMoreCities: "Supports more city data (expandable to thousands)",
      betterQueryPerformance: "Better query performance (with index support)",
      supportUserFeatures: "Supports user favorites, custom cities and other features",
      supportDynamicCRUD: "Supports dynamic CRUD operations",
      betterExtensibility: "Better extensibility for the future",
      jsonSuitableFor: "JSON approach is suitable for:",
      lessThan100Cities: "Less than 100 cities",
      noNeedDynamicData: "No need for dynamic data addition",
      simpleStaticDeployment: "Simple static deployment",
      prototypeDevelopment: "Prototype development stage",
      // SQLite测试页面
      sqliteTestPage: "SQLite Test Page",
      initializingSQLite: "Initializing SQLite database...",
      errorMessage: "Error",
      sqliteInitSuccess: "SQLite initialization successful!",
      successfullyLoaded: "Successfully loaded",
      cityData: "city data",
      countryLabel: "Country",
      timezoneLabel: "Timezone",
      returnToHome: "Return to Home",
      seoDescription: "Support time queries for major global cities: Beijing time, Tokyo time, New York time, London time, Paris time, Moscow time, etc. Provides accurate timezone conversion, UTC offset display, DST status queries - your ideal tool for international business and travel planning.",
      // 相关城市推荐
      relatedCities: "Related Cities",
      sameCountryCities: "Cities in the Same Country",
      sameTimezoneCities: "Cities in the Same Timezone",
      popularCities: "Popular Cities",
      viewCityTime: "View Time",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 Tip: You can search and add more cities to your favorites list",
      searchToAddCities: "Search city names and click + to add them",
      customizeYourCities: "Customize your city list"
    }
  },
  ja: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "ホーム",
      worldTime: "世界時間",
      searchPlaceholder: "都市や国を検索...",
      currentLocalTime: "現在の現地時間",
      timezone: "タイムゾーン",
      utcOffset: "UTCオフセット",
      coordinates: "座標",
      dstStatus: "夏時間ステータス",
      sunrise: "日の出",
      sunset: "日の入り",
      cityInfo: "都市情報",
      population: "人口",
      currency: "通貨",
      language: "言語",
      elevation: "標高",
      area: "面積",
      density: "人口密度",
      nickname: "愛称",
      sameAsBeijing: "北京時間と同じ",
      sameAsDefault: "デフォルトタイムゾーンと同じ",
      earlierThan: "北京より早い",
      laterThan: "北京より遅い",
      earlierThanDefault: "デフォルトタイムゾーンより早い",
      laterThanDefault: "デフォルトタイムゾーンより遅い",
      hours: "時間",
      metropolis: "大都市",
      country: "国",
      addToHomepage: "ホームページに追加",
      setAsDefaultTimezone: "デフォルトタイムゾーンに設定",
      officialWebsite: "公式ウェブサイト",
      cityDescription: "都市紹介",
      quickActions: "クイックアクション",
      additionalInfo: "追加情報",
      cityNotFound: "都市データが見つかりません",
      loadingCityData: "都市データを読み込み中...",
      backToHome: "ホームに戻る",
      mainTitle: "世界時間検索",
      subtitle: "世界主要都市の現在時刻を素早く確認",
      feature1Title: "リアルタイム時計",
      feature1Desc: "世界各都市のリアルタイムを正確に表示",
      feature2Title: "タイムゾーン変換",
      feature2Desc: "異なるタイムゾーン間の時間変換が簡単",
      feature3Title: "都市情報",
      feature3Desc: "都市の詳細情報とタイムゾーンステータスを確認",
      myCities: "マイ都市",
      cities: "都市",
      resetToDefault: "デフォルトにリセット",
      cityListSaved: "都市リストは自動的に保存されました",
      worldMap: "世界地図",
      exploreTimezones: "世界のタイムゾーンを探索",
      compareTitle: "都市比較",
      faq: "よくある質問",
      beijingTime: "北京時間",
      tokyoTime: "東京時間",
      newYorkTime: "ニューヨーク時間",
      // 世界時区図相関
      worldTimezoneMap: "世界タイムゾーンマップ",
      clickForDetails: "詳細については、異なるタイムゾーン地域をクリックしてください",
      utcOffsetLabel: "UTCオフセット:",
      currentTimeLabel: "現在時刻:",
      majorCitiesLabel: "主要都市:",
      timezoneLegend: "タイムゾーン凡例",
      timezoneDisclaimer: "これは簡略化されたタイムゾーン図です。実際のタイムゾーン境界は、地理的および政治的要因により異なる場合があります。",
      // 詳細ページ関連
      defaultTimezoneLabel: "デフォルトタイムゾーン",
      removedFromHomepage: "ホームページから削除されました",
      addedToHomepage: "ホームページに追加されました",
      defaultTimezoneCleared: "デフォルトタイムゾーンをクリアしました",
      setAsDefaultTimezoneMessage: "デフォルトタイムゾーンに設定しました",
      clickToRemoveFromHomepage: "ホームページから削除するにはクリック",
      clickToAddToHomepage: "ホームページに追加するにはクリック",
      clickToClearDefaultTimezone: "デフォルトタイムゾーンをクリアするにはクリック",
      clickToSetAsDefaultTimezone: "デフォルトタイムゾーンに設定するにはクリック",
      addedToHomepageButton: "ホームページに追加済み",
      addToHomepageButton: "ホームページに追加",
      defaultTimezoneButton: "デフォルトタイムゾーン",
      setAsDefaultTimezoneButton: "デフォルトタイムゾーンに設定",
      // 時間比較関連
      sameAs: "と",
      sameTime: "同じ時間",
      aheadOf: "より早い",
      behindOf: "より遅い",
      hoursUnit: "時間",
      referenceTimezone: "参照タイムゾーン",
      // ホームページ追加テキスト
      loadingCityDataMessage: "都市データを読み込み中...",
      noCityData: "都市データがありません",
      trySearchAndAdd: "都市を検索して追加してください",
      showing: "表示中",
      clearFilter: "フィルターをクリア",
      // FAQページ
      frequentlyAskedQuestions: "よくある質問",
      faqDescription: "WorldTimeApp時間検索ツールに関するよくある質問と回答で、サービスをより良く利用するのに役立ちます。",
      relatedLinks: "関連リンク",
      londonTime: "ロンドン時間",
      parisTime: "パリ時間",
      // データベースデモページ
      databaseDemo: "データベースデモ",
      performanceComparison: "パフォーマンス比較",
      jsonLoadTime: "JSON読み込み時間",
      sqliteLoadTime: "SQLite読み込み時間",
      jsonSearchTime: "JSON検索時間",
      sqliteSearchTime: "SQLite検索時間",
      searchCityOrCountry: "都市や国を検索...",
      jsonFileApproach: "JSONファイル方式",
      sqliteApproach: "SQLite方式",
      citiesCount: "都市",
      simpleAndDirect: "シンプルで直接的、設定不要",
      noDataModification: "動的データ追加をサポートしない",
      limitedQuery: "限定的なクエリ機能",
      supportsComplexQuery: "複雑なクエリをサポート",
      supportsCRUD: "動的CRUD操作をサポート",
      supportsIndexOptimization: "インデックス最適化をサポート",
      addSampleCity: "サンプル都市を追加",
      searchResults: "検索結果",
      allCities: "すべての都市",
      populationLabel: "人口",
      recommendations: "推奨事項",
      recommendSQLite: "SQLiteの使用を推奨する理由：",
      supportMoreCities: "より多くの都市データをサポート（数千まで拡張可能）",
      betterQueryPerformance: "より良いクエリパフォーマンス（インデックスサポート付き）",
      supportUserFeatures: "ユーザーのお気に入り、カスタム都市などの機能をサポート",
      supportDynamicCRUD: "動的CRUD操作をサポート",
      betterExtensibility: "将来の拡張性が向上",
      jsonSuitableFor: "JSON方式に適している：",
      lessThan100Cities: "100都市未満",
      noNeedDynamicData: "動的データ追加が不要",
      simpleStaticDeployment: "シンプルな静的デプロイメント",
      prototypeDevelopment: "プロトタイプ開発段階",
      // SQLiteテストページ
      sqliteTestPage: "SQLiteテストページ",
      initializingSQLite: "SQLiteデータベースを初期化中...",
      errorMessage: "エラー",
      sqliteInitSuccess: "SQLite初期化成功！",
      successfullyLoaded: "正常に読み込まれました",
      cityData: "都市データ",
      countryLabel: "国",
      timezoneLabel: "タイムゾーン",
      returnToHome: "ホームに戻る",
      seoDescription: "世界主要都市の時間検索をサポート：北京時間、東京時間、ニューヨーク時間、ロンドン時間、パリ時間、モスクワ時間など。正確なタイムゾーン変換、UTCオフセット表示、夏時間ステータス検索を提供 - 国際ビジネスや旅行計画に最適なツールです。",
      // 相关城市推荐
      relatedCities: "関連する都市",
      sameCountryCities: "同じ国の都市",
      sameTimezoneCities: "同じタイムゾーンの都市",
      popularCities: "人気の都市",
      viewCityTime: "時間を見る",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 ヒント：検索してお気に入りリストに都市を追加できます",
      searchToAddCities: "都市名を検索して + をクリックして追加",
      customizeYourCities: "都市リストをカスタマイズ"
    }
  },
  ko: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "홈",
      worldTime: "세계 시간",
      searchPlaceholder: "도시나 국가를 검색하세요...",
      currentLocalTime: "현재 현지 시간",
      timezone: "시간대",
      utcOffset: "UTC 오프셋",
      coordinates: "좌표",
      dstStatus: "일광 절약 시간 상태",
      sunrise: "일출",
      sunset: "일몰",
      cityInfo: "도시 정보",
      population: "인구",
      currency: "통화",
      language: "언어",
      elevation: "고도",
      area: "면적",
      density: "인구 밀도",
      nickname: "별명",
      sameAsBeijing: "베이징 시간과 동일",
      sameAsDefault: "기본 시간대와 동일",
      earlierThan: "베이징보다 빠름",
      laterThan: "베이징보다 늦음",
      hours: "시간",
      metropolis: "대도시",
      country: "국가",
      addToHomepage: "홈페이지에 추가",
      setAsDefaultTimezone: "기본 시간대로 설정",
      officialWebsite: "공식 웹사이트",
      cityDescription: "도시 소개",
      quickActions: "빠른 작업",
      additionalInfo: "추가 정보",
      cityNotFound: "도시 데이터를 찾을 수 없습니다",
      loadingCityData: "도시 데이터 로딩 중...",
      backToHome: "홈으로 돌아가기",
      mainTitle: "세계 시간 검색",
      subtitle: "전 세계 주요 도시의 현재 시간을 빠르게 확인",
      feature1Title: "실시간 시계",
      feature1Desc: "전 세계 도시의 실시간을 정확하게 표시",
      feature2Title: "시간대 변환",
      feature2Desc: "다른 시간대 간의 시간 변환이 쉬움",
      feature3Title: "도시 정보",
      feature3Desc: "도시의 자세한 정보와 시간대 상태 확인",
      myCities: "내 도시",
      cities: "개 도시",
      resetToDefault: "기본값으로 재설정",
      cityListSaved: "도시 목록이 자동으로 저장되었습니다",
      worldMap: "세계 지도",
      exploreTimezones: "전 세계 시간대 탐색",
      compareTitle: "도시 비교",
      faq: "자주 묻는 질문",
      beijingTime: "베이징 시간",
      tokyoTime: "도쿄 시간",
      newYorkTime: "뉴욕 시간",
      // 세계 시간대 지도 관련
      worldTimezoneMap: "세계 시간대 지도",
      clickForDetails: "자세한 내용을 보려면 다른 시간대 지역을 클릭하세요",
      utcOffsetLabel: "UTC 오프셋:",
      currentTimeLabel: "현재 시간:",
      majorCitiesLabel: "주요 도시:",
      timezoneLegend: "시간대 범례",
      timezoneDisclaimer: "이것은 단순화된 시간대 다이어그램입니다. 실제 시간대 경계는 지리적 및 정치적 요인에 따라 다를 수 있습니다.",
      // 상세 페이지 관련
      defaultTimezoneLabel: "기본 시간대",
      removedFromHomepage: "홈페이지에서 제거됨",
      addedToHomepage: "홈페이지에 추가됨",
      defaultTimezoneCleared: "기본 시간대가 지워짐",
      setAsDefaultTimezoneMessage: "기본 시간대로 설정됨",
      clickToRemoveFromHomepage: "홈페이지에서 제거하려면 클릭",
      clickToAddToHomepage: "홈페이지에 추가하려면 클릭",
      clickToClearDefaultTimezone: "기본 시간대를 지우려면 클릭",
      clickToSetAsDefaultTimezone: "기본 시간대로 설정하려면 클릭",
      addedToHomepageButton: "홈페이지에 추가됨",
      addToHomepageButton: "홈페이지에 추가",
      defaultTimezoneButton: "기본 시간대",
      setAsDefaultTimezoneButton: "기본 시간대로 설정",
      // 시간 비교 관련
      sameAs: "와",
      sameTime: "같은 시간",
      aheadOf: "보다 빠름",
      behindOf: "보다 늦음",
      hoursUnit: "시간",
      referenceTimezone: "참조 시간대",
      // 추가 텍스트
      loadingCityDataMessage: "도시 데이터 로딩 중...",
      noCityData: "도시 데이터가 없습니다",
      trySearchAndAdd: "도시를 검색하고 추가해 보세요",
      showing: "표시 중",
      clearFilter: "필터 지우기",
      frequentlyAskedQuestions: "자주 묻는 질문",
      faqDescription: "WorldTimeApp 시간 검색 도구에 대한 일반적인 질문과 답변으로 서비스를 더 잘 이용할 수 있도록 도와드립니다.",
      relatedLinks: "관련 링크",
      londonTime: "런던 시간",
      parisTime: "파리 시간",
      databaseDemo: "데이터베이스 데모",
      performanceComparison: "성능 비교",
      sqliteTestPage: "SQLite 테스트 페이지",
      initializingSQLite: "SQLite 데이터베이스 초기화 중...",
      errorMessage: "오류",
      sqliteInitSuccess: "SQLite 초기화 성공!",
      successfullyLoaded: "성공적으로 로드됨",
      cityData: "도시 데이터",
      countryLabel: "국가",
      timezoneLabel: "시간대",
      returnToHome: "홈으로 돌아가기",
      populationLabel: "인구",
      seoDescription: "전 세계 주요 도시 시간 검색 지원: 베이징 시간, 도쿄 시간, 뉴욕 시간, 런던 시간, 파리 시간, 모스크바 시간 등. 정확한 시간대 변환, UTC 오프셋 표시, 일광 절약 시간 상태 검색 제공 - 국제 비즈니스 및 여행 계획을 위한 이상적인 도구입니다.",
      // 相关城市推荐
      relatedCities: "관련 도시",
      sameCountryCities: "동일한 국가의 도시",
      sameTimezoneCities: "동일한 시간대의 도시",
      popularCities: "인기 있는 도시",
      viewCityTime: "시간 보기",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 팁: 검색을 통해 즐겨찾기 목록에 더 많은 도시를 추가할 수 있습니다",
      searchToAddCities: "도시 이름을 검색하고 + 를 클릭하여 추가하세요",
      customizeYourCities: "도시 목록을 사용자 정의하세요"
    }
  },
  fr: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "Accueil",
      worldTime: "Heure Mondiale",
      searchPlaceholder: "Rechercher des villes ou des pays...",
      currentLocalTime: "Heure Locale Actuelle",
      timezone: "Fuseau Horaire",
      utcOffset: "Décalage UTC",
      coordinates: "Coordonnées",
      dstStatus: "Statut de l'Heure d'Été",
      sunrise: "Lever du Soleil",
      sunset: "Coucher du Soleil",
      cityInfo: "Informations sur la Ville",
      population: "Population",
      currency: "Devise",
      language: "Langue",
      elevation: "Altitude",
      area: "Superficie",
      density: "Densité",
      nickname: "Surnom",
      sameAsBeijing: "Même que l'heure de Pékin",
      sameAsDefault: "Même que le fuseau horaire par défaut",
      earlierThan: "Plus tôt que Pékin",
      laterThan: "Plus tard que Pékin",
      hours: "heures",
      metropolis: "Métropole",
      country: "Pays",
      addToHomepage: "Ajouter à la Page d'Accueil",
      setAsDefaultTimezone: "Définir comme Fuseau Horaire par Défaut",
      officialWebsite: "Site Web Officiel",
      cityDescription: "Description de la Ville",
      quickActions: "Actions Rapides",
      additionalInfo: "Informations Supplémentaires",
      cityNotFound: "Données de ville non trouvées",
      loadingCityData: "Chargement des données de ville...",
      backToHome: "Retour à l'Accueil",
      mainTitle: "Recherche de l'Heure Mondiale",
      subtitle: "Vérifiez rapidement l'heure actuelle dans les grandes villes du monde",
      feature1Title: "Horloge en Temps Réel",
      feature1Desc: "Affiche avec précision l'heure en temps réel dans les villes du monde",
      feature2Title: "Conversion de Fuseau Horaire",
      feature2Desc: "Conversion facile entre différents fuseaux horaires",
      feature3Title: "Informations sur la Ville",
      feature3Desc: "Apprenez les informations détaillées et le statut du fuseau horaire des villes",
      myCities: "Mes Villes",
      cities: "villes",
      resetToDefault: "Réinitialiser par Défaut",
      cityListSaved: "Votre liste de villes est automatiquement sauvegardée",
      worldMap: "Carte du Monde",
      exploreTimezones: "Explorer les fuseaux horaires mondiaux",
      compareTitle: "Comparer les Villes",
      faq: "FAQ",
      beijingTime: "Heure de Pékin",
      tokyoTime: "Heure de Tokyo",
      newYorkTime: "Heure de New York",
      // Carte des fuseaux horaires mondiaux
      worldTimezoneMap: "Carte des Fuseaux Horaires Mondiaux",
      clickForDetails: "Cliquez sur différentes régions de fuseau horaire pour plus de détails",
      utcOffsetLabel: "Décalage UTC:",
      currentTimeLabel: "Heure Actuelle:",
      majorCitiesLabel: "Villes Principales:",
      timezoneLegend: "Légende des Fuseaux Horaires",
      timezoneDisclaimer: "Il s'agit d'un diagramme de fuseau horaire simplifié. Les frontières réelles des fuseaux horaires peuvent varier en raison de facteurs géographiques et politiques.",
      // Page de détails
      defaultTimezoneLabel: "Fuseau Horaire par Défaut",
      removedFromHomepage: "supprimé de la page d'accueil",
      addedToHomepage: "ajouté à la page d'accueil",
      defaultTimezoneCleared: "Fuseau horaire par défaut effacé",
      setAsDefaultTimezoneMessage: "défini comme fuseau horaire par défaut",
      clickToRemoveFromHomepage: "Cliquez pour supprimer de la page d'accueil",
      clickToAddToHomepage: "Cliquez pour ajouter à la page d'accueil",
      clickToClearDefaultTimezone: "Cliquez pour effacer le fuseau horaire par défaut",
      clickToSetAsDefaultTimezone: "Cliquez pour définir comme fuseau horaire par défaut",
      addedToHomepageButton: "Ajouté à la Page d'Accueil",
      addToHomepageButton: "Ajouter à la Page d'Accueil",
      defaultTimezoneButton: "Fuseau Horaire par Défaut",
      setAsDefaultTimezoneButton: "Définir comme Fuseau Horaire par Défaut",
      // Comparaison de temps
      sameAs: "Même que",
      sameTime: "",
      aheadOf: "en avance",
      behindOf: "en retard",
      hoursUnit: "heures",
      referenceTimezone: "Fuseau Horaire de Référence",
      // Textes supplémentaires
      loadingCityDataMessage: "Chargement des données de ville...",
      noCityData: "Aucune donnée de ville disponible",
      trySearchAndAdd: "Essayez de rechercher et d'ajouter des villes",
      showing: "Affichage",
      clearFilter: "Effacer le filtre",
      frequentlyAskedQuestions: "Questions Fréquemment Posées",
      faqDescription: "Questions et réponses courantes sur l'outil de recherche de temps WorldTimeApp pour vous aider à mieux utiliser nos services.",
      relatedLinks: "Liens Connexes",
      londonTime: "Heure de Londres",
      parisTime: "Heure de Paris",
      databaseDemo: "Démo de Base de Données",
      performanceComparison: "Comparaison de Performance",
      sqliteTestPage: "Page de Test SQLite",
      initializingSQLite: "Initialisation de la base de données SQLite...",
      errorMessage: "Erreur",
      sqliteInitSuccess: "Initialisation SQLite réussie!",
      successfullyLoaded: "Chargé avec succès",
      cityData: "données de ville",
      countryLabel: "Pays",
      timezoneLabel: "Fuseau Horaire",
      returnToHome: "Retour à l'Accueil",
      populationLabel: "Population",
      seoDescription: "Recherche de temps pour les principales villes du monde : heure de Pékin, heure de Tokyo, heure de New York, heure de Londres, heure de Paris, heure de Moscou, etc. Fournit une conversion précise des fuseaux horaires, l'affichage du décalage UTC, les requêtes d'état de l'heure d'été - votre outil idéal pour les affaires internationales et la planification de voyages.",
      // 相关城市推荐
      relatedCities: "Villes Relatives",
      sameCountryCities: "Villes du Même Pays",
      sameTimezoneCities: "Villes du Même Fuseau Horaire",
      popularCities: "Villes Populaires",
      viewCityTime: "Voir l'Heure",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 Astuce: Vous pouvez rechercher et ajouter plus de villes à votre liste de favoris",
      searchToAddCities: "Recherchez les noms de villes et cliquez sur + pour les ajouter",
      customizeYourCities: "Personnalisez votre liste de villes"
    }
  },
  de: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "Startseite",
      worldTime: "Weltzeit",
      searchPlaceholder: "Städte oder Länder suchen...",
      currentLocalTime: "Aktuelle Ortszeit",
      timezone: "Zeitzone",
      utcOffset: "UTC-Versatz",
      coordinates: "Koordinaten",
      dstStatus: "Sommerzeit-Status",
      sunrise: "Sonnenaufgang",
      sunset: "Sonnenuntergang",
      cityInfo: "Stadtinformationen",
      population: "Bevölkerung",
      currency: "Währung",
      language: "Sprache",
      elevation: "Höhe",
      area: "Fläche",
      density: "Dichte",
      nickname: "Spitzname",
      sameAsBeijing: "Gleich wie Peking-Zeit",
      sameAsDefault: "Gleich wie Standard-Zeitzone",
      earlierThan: "Früher als Peking",
      laterThan: "Später als Peking",
      hours: "Stunden",
      metropolis: "Metropole",
      country: "Land",
      addToHomepage: "Zur Startseite hinzufügen",
      setAsDefaultTimezone: "Als Standard-Zeitzone festlegen",
      officialWebsite: "Offizielle Website",
      cityDescription: "Stadtbeschreibung",
      quickActions: "Schnelle Aktionen",
      additionalInfo: "Zusätzliche Informationen",
      cityNotFound: "Stadtdaten nicht gefunden",
      loadingCityData: "Stadtdaten werden geladen...",
      backToHome: "Zurück zur Startseite",
      mainTitle: "Weltzeit-Abfrage",
      subtitle: "Überprüfen Sie schnell die aktuelle Zeit in den wichtigsten Städten weltweit",
      feature1Title: "Echtzeit-Uhr",
      feature1Desc: "Zeigt genau die Echtzeit in Städten weltweit an",
      feature2Title: "Zeitzonenumrechnung",
      feature2Desc: "Einfache Zeitumrechnung zwischen verschiedenen Zeitzonen",
      feature3Title: "Stadtinformationen",
      feature3Desc: "Lernen Sie detaillierte Informationen und Zeitzonenstatus von Städten",
      myCities: "Meine Städte",
      cities: "Städte",
      resetToDefault: "Auf Standard zurücksetzen",
      cityListSaved: "Ihre Städteliste wird automatisch gespeichert",
      worldMap: "Weltkarte",
      exploreTimezones: "Erkunden Sie globale Zeitzonen",
      compareTitle: "Städte Vergleichen",
      faq: "FAQ",
      beijingTime: "Peking-Zeit",
      tokyoTime: "Tokio-Zeit",
      newYorkTime: "New York Zeit",
      // Weltzeitzonenkarte
      worldTimezoneMap: "Weltzeitzonenkarte",
      clickForDetails: "Klicken Sie auf verschiedene Zeitzonenregionen für Details",
      utcOffsetLabel: "UTC-Versatz:",
      currentTimeLabel: "Aktuelle Zeit:",
      majorCitiesLabel: "Hauptstädte:",
      timezoneLegend: "Zeitzonen-Legende",
      timezoneDisclaimer: "Dies ist ein vereinfachtes Zeitzonendiagramm. Tatsächliche Zeitzonengrenzen können aufgrund geografischer und politischer Faktoren variieren.",
      // Detailseite
      defaultTimezoneLabel: "Standard-Zeitzone",
      removedFromHomepage: "von der Startseite entfernt",
      addedToHomepage: "zur Startseite hinzugefügt",
      defaultTimezoneCleared: "Standard-Zeitzone gelöscht",
      setAsDefaultTimezoneMessage: "als Standard-Zeitzone festgelegt",
      clickToRemoveFromHomepage: "Klicken Sie, um von der Startseite zu entfernen",
      clickToAddToHomepage: "Klicken Sie, um zur Startseite hinzuzufügen",
      clickToClearDefaultTimezone: "Klicken Sie, um die Standard-Zeitzone zu löschen",
      clickToSetAsDefaultTimezone: "Klicken Sie, um als Standard-Zeitzone festzulegen",
      addedToHomepageButton: "Zur Startseite Hinzugefügt",
      addToHomepageButton: "Zur Startseite Hinzufügen",
      defaultTimezoneButton: "Standard-Zeitzone",
      setAsDefaultTimezoneButton: "Als Standard-Zeitzone Festlegen",
      // Zeitvergleich
      sameAs: "Gleich wie",
      sameTime: "",
      aheadOf: "voraus",
      behindOf: "hinterher",
      hoursUnit: "Stunden",
      referenceTimezone: "Referenz-Zeitzone",
      // Zusätzliche Texte
      loadingCityDataMessage: "Stadtdaten werden geladen...",
      noCityData: "Keine Stadtdaten verfügbar",
      trySearchAndAdd: "Versuchen Sie, Städte zu suchen und hinzuzufügen",
      showing: "Anzeige",
      clearFilter: "Filter löschen",
      frequentlyAskedQuestions: "Häufig Gestellte Fragen",
      faqDescription: "Häufige Fragen und Antworten zum WorldTimeApp-Zeitsuchtools, um Ihnen bei der besseren Nutzung unserer Dienste zu helfen.",
      relatedLinks: "Verwandte Links",
      londonTime: "London-Zeit",
      parisTime: "Paris-Zeit",
      databaseDemo: "Datenbank-Demo",
      performanceComparison: "Leistungsvergleich",
      sqliteTestPage: "SQLite-Testseite",
      initializingSQLite: "SQLite-Datenbank wird initialisiert...",
      errorMessage: "Fehler",
      sqliteInitSuccess: "SQLite-Initialisierung erfolgreich!",
      successfullyLoaded: "Erfolgreich geladen",
      cityData: "Stadtdaten",
      countryLabel: "Land",
      timezoneLabel: "Zeitzone",
      returnToHome: "Zurück zur Startseite",
      populationLabel: "Bevölkerung",
      seoDescription: "Zeitabfrage für wichtige Städte weltweit: Peking-Zeit, Tokio-Zeit, New York-Zeit, London-Zeit, Paris-Zeit, Moskau-Zeit usw. Bietet genaue Zeitzonenumrechnung, UTC-Offset-Anzeige, Sommerzeit-Statusabfragen - Ihr ideales Werkzeug für internationale Geschäfte und Reiseplanung.",
      // 相关城市推荐
      relatedCities: "Verwandte Städte",
      sameCountryCities: "Städte im selben Land",
      sameTimezoneCities: "Städte im selben Zeitzonenbereich",
      popularCities: "Beliebte Städte",
      viewCityTime: "Zeit ansehen",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 Tipp: Sie können suchen und weitere Städte zu Ihrer Favoritenliste hinzufügen",
      searchToAddCities: "Suchen Sie Städtenamen und klicken Sie auf +, um sie hinzuzufügen",
      customizeYourCities: "Passen Sie Ihre Städteliste an"
    }
  },
  es: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "Inicio",
      worldTime: "Hora Mundial",
      searchPlaceholder: "Buscar ciudades o países...",
      currentLocalTime: "Hora Local Actual",
      timezone: "Zona Horaria",
      utcOffset: "Desfase UTC",
      coordinates: "Coordenadas",
      dstStatus: "Estado del Horario de Verano",
      sunrise: "Amanecer",
      sunset: "Atardecer",
      cityInfo: "Información de la Ciudad",
      population: "Población",
      currency: "Moneda",
      language: "Idioma",
      elevation: "Elevación",
      area: "Área",
      density: "Densidad",
      nickname: "Apodo",
      sameAsBeijing: "Igual que la hora de Pekín",
      sameAsDefault: "Igual que la zona horaria predeterminada",
      earlierThan: "Más temprano que Pekín",
      laterThan: "Más tarde que Pekín",
      hours: "horas",
      metropolis: "Metrópoli",
      country: "País",
      addToHomepage: "Agregar a la Página de Inicio",
      setAsDefaultTimezone: "Establecer como Zona Horaria Predeterminada",
      officialWebsite: "Sitio Web Oficial",
      cityDescription: "Descripción de la Ciudad",
      quickActions: "Acciones Rápidas",
      additionalInfo: "Información Adicional",
      cityNotFound: "Datos de ciudad no encontrados",
      loadingCityData: "Cargando datos de ciudad...",
      backToHome: "Volver al Inicio",
      mainTitle: "Consulta de Hora Mundial",
      subtitle: "Verifique rápidamente la hora actual en las principales ciudades del mundo",
      feature1Title: "Reloj en Tiempo Real",
      feature1Desc: "Muestra con precisión el tiempo real en ciudades de todo el mundo",
      feature2Title: "Conversión de Zona Horaria",
      feature2Desc: "Conversión fácil de tiempo entre diferentes zonas horarias",
      feature3Title: "Información de la Ciudad",
      feature3Desc: "Aprenda información detallada y estado de zona horaria de las ciudades",
      myCities: "Mis Ciudades",
      cities: "ciudades",
      resetToDefault: "Restablecer por Defecto",
      cityListSaved: "Su lista de ciudades se guarda automáticamente",
      worldMap: "Mapa Mundial",
      exploreTimezones: "Explorar zonas horarias globales",
      compareTitle: "Comparar Ciudades",
      faq: "FAQ",
      beijingTime: "Hora de Pekín",
      tokyoTime: "Hora de Tokio",
      newYorkTime: "Hora de Nueva York",
      // Mapa de zonas horarias mundiales
      worldTimezoneMap: "Mapa de Zonas Horarias Mundiales",
      clickForDetails: "Haga clic en diferentes regiones de zona horaria para obtener detalles",
      utcOffsetLabel: "Desfase UTC:",
      currentTimeLabel: "Hora Actual:",
      majorCitiesLabel: "Ciudades Principales:",
      timezoneLegend: "Leyenda de Zonas Horarias",
      timezoneDisclaimer: "Este es un diagrama de zona horaria simplificado. Los límites reales de las zonas horarias pueden variar debido a factores geográficos y políticos.",
      // Página de detalles
      defaultTimezoneLabel: "Zona Horaria Predeterminada",
      removedFromHomepage: "eliminado de la página de inicio",
      addedToHomepage: "agregado a la página de inicio",
      defaultTimezoneCleared: "Zona horaria predeterminada eliminada",
      setAsDefaultTimezoneMessage: "establecido como zona horaria predeterminada",
      clickToRemoveFromHomepage: "Haga clic para eliminar de la página de inicio",
      clickToAddToHomepage: "Haga clic para agregar a la página de inicio",
      clickToClearDefaultTimezone: "Haga clic para eliminar la zona horaria predeterminada",
      clickToSetAsDefaultTimezone: "Haga clic para establecer como zona horaria predeterminada",
      addedToHomepageButton: "Agregado a la Página de Inicio",
      addToHomepageButton: "Agregar a la Página de Inicio",
      defaultTimezoneButton: "Zona Horaria Predeterminada",
      setAsDefaultTimezoneButton: "Establecer como Zona Horaria Predeterminada",
      // Comparación de tiempo
      sameAs: "Igual que",
      sameTime: "",
      aheadOf: "adelante",
      behindOf: "atrás",
      hoursUnit: "horas",
      referenceTimezone: "Zona Horaria de Referencia",
      // Textos adicionales
      loadingCityDataMessage: "Cargando datos de ciudad...",
      noCityData: "No hay datos de ciudad disponibles",
      trySearchAndAdd: "Intente buscar y agregar ciudades",
      showing: "Mostrando",
      clearFilter: "Limpiar filtro",
      frequentlyAskedQuestions: "Preguntas Frecuentes",
      faqDescription: "Preguntas y respuestas comunes sobre la herramienta de consulta de tiempo WorldTimeApp para ayudarle a usar mejor nuestros servicios.",
      relatedLinks: "Enlaces Relacionados",
      londonTime: "Hora de Londres",
      parisTime: "Hora de París",
      databaseDemo: "Demo de Base de Datos",
      performanceComparison: "Comparación de Rendimiento",
      sqliteTestPage: "Página de Prueba SQLite",
      initializingSQLite: "Inicializando base de datos SQLite...",
      errorMessage: "Error",
      sqliteInitSuccess: "¡Inicialización SQLite exitosa!",
      successfullyLoaded: "Cargado exitosamente",
      cityData: "datos de ciudad",
      countryLabel: "País",
      timezoneLabel: "Zona Horaria",
      returnToHome: "Volver al Inicio",
      populationLabel: "Población",
      seoDescription: "Consulta de tiempo para las principales ciudades del mundo: hora de Pekín, hora de Tokio, hora de Nueva York, hora de Londres, hora de París, hora de Moscú, etc. Proporciona conversión precisa de zona horaria, visualización de desfase UTC, consultas de estado de horario de verano - su herramienta ideal para negocios internacionales y planificación de viajes.",
      // 相关城市推荐
      relatedCities: "Ciudades Relacionadas",
      sameCountryCities: "Ciudades del mismo país",
      sameTimezoneCities: "Ciudades del mismo huso horario",
      popularCities: "Ciudades Populares",
      viewCityTime: "Ver Hora",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 Consejo: Puede buscar y agregar más ciudades a su lista de favoritos",
      searchToAddCities: "Busque nombres de ciudades y haga clic en + para agregarlas",
      customizeYourCities: "Personalice su lista de ciudades"
    }
  },
  ru: {
    translation: {
      siteTitle: "WorldTimeApp",
      siteDomain: "worldtimeapp.online",
      home: "Главная",
      worldTime: "Мировое Время",
      searchPlaceholder: "Поиск городов или стран...",
      currentLocalTime: "Текущее Местное Время",
      timezone: "Часовой Пояс",
      utcOffset: "Смещение UTC",
      coordinates: "Координаты",
      dstStatus: "Статус Летнего Времени",
      sunrise: "Восход",
      sunset: "Закат",
      cityInfo: "Информация о Городе",
      population: "Население",
      currency: "Валюта",
      language: "Язык",
      elevation: "Высота",
      area: "Площадь",
      density: "Плотность",
      nickname: "Прозвище",
      sameAsBeijing: "Совпадает с пекинским временем",
      sameAsDefault: "Совпадает с часовым поясом по умолчанию",
      earlierThan: "Раньше Пекина",
      laterThan: "Позже Пекина",
      hours: "часов",
      metropolis: "Мегаполис",
      country: "Страна",
      addToHomepage: "Добавить на Главную",
      setAsDefaultTimezone: "Установить как Часовой Пояс по Умолчанию",
      officialWebsite: "Официальный Сайт",
      cityDescription: "Описание Города",
      quickActions: "Быстрые Действия",
      additionalInfo: "Дополнительная Информация",
      cityNotFound: "Данные города не найдены",
      loadingCityData: "Загрузка данных города...",
      backToHome: "Вернуться на Главную",
      mainTitle: "Запрос Мирового Времени",
      subtitle: "Быстро проверьте текущее время в основных городах мира",
      feature1Title: "Часы Реального Времени",
      feature1Desc: "Точно отображает время в реальном времени в городах по всему миру",
      feature2Title: "Преобразование Часового Пояса",
      feature2Desc: "Легкое преобразование времени между различными часовыми поясами",
      feature3Title: "Информация о Городе",
      feature3Desc: "Изучите подробную информацию и статус часового пояса городов",
      myCities: "Мои Города",
      cities: "городов",
      resetToDefault: "Сбросить по Умолчанию",
      cityListSaved: "Ваш список городов автоматически сохранен",
      worldMap: "Карта Мира",
      exploreTimezones: "Исследуйте глобальные часовые пояса",
      compareTitle: "Сравнить Города",
      faq: "FAQ",
      beijingTime: "Пекинское Время",
      tokyoTime: "Токийское Время",
      newYorkTime: "Нью-Йоркское Время",
      // Карта мировых часовых поясов
      worldTimezoneMap: "Карта Мировых Часовых Поясов",
      clickForDetails: "Нажмите на различные регионы часовых поясов для получения подробностей",
      utcOffsetLabel: "Смещение UTC:",
      currentTimeLabel: "Текущее Время:",
      majorCitiesLabel: "Основные Города:",
      timezoneLegend: "Легенда Часовых Поясов",
      timezoneDisclaimer: "Это упрощенная диаграмма часовых поясов. Фактические границы часовых поясов могут отличаться из-за географических и политических факторов.",
      // Страница деталей
      defaultTimezoneLabel: "Часовой Пояс по Умолчанию",
      removedFromHomepage: "удален с главной страницы",
      addedToHomepage: "добавлен на главную страницу",
      defaultTimezoneCleared: "Часовой пояс по умолчанию очищен",
      setAsDefaultTimezoneMessage: "установлен как часовой пояс по умолчанию",
      clickToRemoveFromHomepage: "Нажмите, чтобы удалить с главной страницы",
      clickToAddToHomepage: "Нажмите, чтобы добавить на главную страницу",
      clickToClearDefaultTimezone: "Нажмите, чтобы очистить часовой пояс по умолчанию",
      clickToSetAsDefaultTimezone: "Нажмите, чтобы установить как часовой пояс по умолчанию",
      addedToHomepageButton: "Добавлено на Главную Страницу",
      addToHomepageButton: "Добавить на Главную Страницу",
      defaultTimezoneButton: "Часовой Пояс по Умолчанию",
      setAsDefaultTimezoneButton: "Установить как Часовой Пояс по Умолчанию",
      // Сравнение времени
      sameAs: "Так же как",
      sameTime: "",
      aheadOf: "впереди",
      behindOf: "позади",
      hoursUnit: "часов",
      referenceTimezone: "Референсный Часовой Пояс",
      // Дополнительные тексты
      loadingCityDataMessage: "Загрузка данных города...",
      noCityData: "Данные города недоступны",
      trySearchAndAdd: "Попробуйте найти и добавить города",
      showing: "Показ",
      clearFilter: "Очистить фильтр",
      frequentlyAskedQuestions: "Часто Задаваемые Вопросы",
      faqDescription: "Общие вопросы и ответы о инструменте запроса времени WorldTimeApp, чтобы помочь вам лучше использовать наши услуги.",
      relatedLinks: "Связанные Ссылки",
      londonTime: "Лондонское Время",
      parisTime: "Парижское Время",
      databaseDemo: "Демо База Данных",
      performanceComparison: "Сравнение Производительности",
      sqliteTestPage: "Страница Тестирования SQLite",
      initializingSQLite: "Инициализация базы данных SQLite...",
      errorMessage: "Ошибка",
      sqliteInitSuccess: "Инициализация SQLite успешна!",
      successfullyLoaded: "Успешно загружено",
      cityData: "данные города",
      countryLabel: "Страна",
      timezoneLabel: "Часовой Пояс",
      returnToHome: "Вернуться на Главную",
      populationLabel: "Население",
      seoDescription: "Запрос времени для основных городов мира: пекинское время, токийское время, нью-йоркское время, лондонское время, парижское время, московское время и т.д. Обеспечивает точное преобразование часовых поясов, отображение смещения UTC, запросы статуса летнего времени - ваш идеальный инструмент для международного бизнеса и планирования путешествий.",
      // 相关城市推荐
      relatedCities: "Связанные Города",
      sameCountryCities: "Города в одном государстве",
      sameTimezoneCities: "Города в одном часовом поясе",
      popularCities: "Популярные Города",
      viewCityTime: "Смотреть Время",
      // 首页城市收藏提示
      favoriteCitiesTip: "💡 Совет: Вы можете искать и добавлять больше городов в ваш список избранного",
      searchToAddCities: "Ищите названия городов и нажимайте + для добавления",
      customizeYourCities: "Настройте ваш список городов"
    }
  }
}

// 初始化 i18n
if (typeof window !== 'undefined') {
  // 浏览器环境
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources,
      fallbackLng: 'en',
      debug: false,
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
        lookupLocalStorage: 'i18nextLng'
      },
      interpolation: {
        escapeValue: false
      },
      react: {
        useSuspense: false
      }
    })
} else {
  // 服务器环境 - 使用与客户端相同的配置
  i18n
    .use(initReactI18next)
    .init({
      resources,
      fallbackLng: 'en',
      lng: 'en', // 服务端默认使用英文，客户端会根据URL切换
      interpolation: {
        escapeValue: false
      },
      react: {
        useSuspense: false
      }
    })
}

export default i18n 