#!/usr/bin/env node

/**
 * 最终版本：正确使用 @playwright/mcp 测试百度网站
 * 
 * 先创建标签页，然后进行操作
 */

console.log('🚀 最终版本：使用 @playwright/mcp 测试百度...\n');

async function testBaiduWithMCPFinal() {
  try {
    const playwrightMcp = await import('@playwright/mcp');
    const connection = await playwrightMcp.createConnection();
    
    console.log('✅ MCP 连接创建成功');
    
    // 首先尝试创建新标签页
    console.log('\n📑 创建新标签页...');
    const newTabTool = connection.context.tools.find(tool => 
      tool.schema.name === 'browser_tab_new'
    );
    
    if (newTabTool) {
      try {
        const tabResult = await newTabTool.handle({});
        console.log('✅ 新标签页创建成功:', tabResult);
      } catch (error) {
        console.log('❌ 创建标签页失败:', error.message);
        console.log('💡 尝试直接导航...');
      }
    }
    
    // 等待一下让标签页初始化
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 尝试导航到百度
    console.log('\n🧭 导航到百度首页...');
    const navigateTool = connection.context.tools.find(tool => 
      tool.schema.name === 'browser_navigate'
    );
    
    if (navigateTool) {
      try {
        const navResult = await navigateTool.handle({
          url: 'https://www.baidu.com'
        });
        console.log('✅ 导航成功:', navResult);
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 尝试截图
        console.log('\n📸 尝试截图...');
        const screenshotTool = connection.context.tools.find(tool => 
          tool.schema.name === 'browser_take_screenshot'
        );
        
        if (screenshotTool) {
          try {
            const screenshotResult = await screenshotTool.handle({});
            console.log('✅ 截图成功:', screenshotResult);
          } catch (error) {
            console.log('❌ 截图失败:', error.message);
          }
        }
        
        // 尝试获取页面快照
        console.log('\n📄 获取页面快照...');
        const snapshotTool = connection.context.tools.find(tool => 
          tool.schema.name === 'browser_snapshot'
        );
        
        if (snapshotTool) {
          try {
            const snapshotResult = await snapshotTool.handle({});
            console.log('✅ 快照获取成功');
            console.log('📋 快照类型:', typeof snapshotResult);
            
            if (typeof snapshotResult === 'string') {
              console.log('📄 快照内容预览:', snapshotResult.substring(0, 500) + '...');
            } else {
              console.log('📋 快照对象:', snapshotResult);
            }
          } catch (error) {
            console.log('❌ 获取快照失败:', error.message);
          }
        }
        
        // 尝试在搜索框中输入文字
        console.log('\n🔍 尝试搜索功能...');
        const typeTool = connection.context.tools.find(tool => 
          tool.schema.name === 'browser_type'
        );
        
        const clickTool = connection.context.tools.find(tool => 
          tool.schema.name === 'browser_click'
        );
        
        if (clickTool && typeTool) {
          try {
            // 点击搜索框
            console.log('🖱️  点击搜索框...');
            await clickTool.handle({
              selector: '#kw'
            });
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 输入搜索内容
            console.log('⌨️  输入搜索内容...');
            await typeTool.handle({
              text: 'playwright mcp 自动化测试'
            });
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 点击搜索按钮
            console.log('🔍 点击搜索按钮...');
            await clickTool.handle({
              selector: '#su'
            });
            
            // 等待搜索结果
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 再次截图查看搜索结果
            console.log('📸 搜索结果截图...');
            const finalScreenshot = await screenshotTool.handle({});
            console.log('✅ 搜索结果截图成功:', finalScreenshot);
            
            console.log('🎉 搜索测试完成！');
            
          } catch (error) {
            console.log('❌ 搜索测试失败:', error.message);
          }
        }
        
        // 获取控制台消息
        console.log('\n📋 获取控制台消息...');
        const consoleTool = connection.context.tools.find(tool => 
          tool.schema.name === 'browser_console_messages'
        );
        
        if (consoleTool) {
          try {
            const consoleResult = await consoleTool.handle({});
            console.log('✅ 控制台消息获取成功:', consoleResult);
          } catch (error) {
            console.log('❌ 获取控制台消息失败:', error.message);
          }
        }
        
      } catch (error) {
        console.log('❌ 导航失败:', error.message);
        console.log('💡 可能需要先安装浏览器');
        
        // 尝试安装浏览器
        const installTool = connection.context.tools.find(tool => 
          tool.schema.name === 'browser_install'
        );
        
        if (installTool) {
          console.log('🔧 尝试安装浏览器...');
          try {
            const installResult = await installTool.handle({});
            console.log('✅ 浏览器安装成功:', installResult);
          } catch (installError) {
            console.log('❌ 浏览器安装失败:', installError.message);
          }
        }
      }
    }
    
    console.log('\n🎉 @playwright/mcp 测试完成！');
    console.log('💡 如果遇到问题，请检查浏览器是否正确安装');
    
  } catch (error) {
    console.error('❌ MCP 测试失败:', error.message);
    console.error('📋 错误详情:', error);
  }
}

// 运行测试
if (require.main === module) {
  testBaiduWithMCPFinal().catch(console.error);
}

module.exports = { testBaiduWithMCPFinal };
