#!/usr/bin/env node

/**
 * 验证 Playwright MCP 修复结果的脚本
 * 
 * 这个脚本会检查：
 * 1. 标准 Playwright 是否正确安装
 * 2. 修复的依赖文件是否存在
 * 3. 测试脚本是否可以运行
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 Playwright MCP 修复结果...\n');

// 检查 Playwright 安装
console.log('📦 检查 Playwright 安装状态:');
const playwrightPath = path.join(process.cwd(), 'node_modules', 'playwright');
const playwrightInstalled = fs.existsSync(playwrightPath);
console.log(`- Playwright 已安装: ${playwrightInstalled ? '✅' : '❌'}`);

// 检查修复的文件
console.log('\n🔧 检查修复的依赖文件:');
const zodEnPath = path.join(process.cwd(), 'node_modules', 'zod', 'v3', 'locales', 'en.js');
const zodFixed = fs.existsSync(zodEnPath);
console.log(`- zod v3/locales/en.js: ${zodFixed ? '✅' : '❌'}`);

// 检查测试脚本
console.log('\n📄 检查测试脚本:');
const altScriptPath = path.join(process.cwd(), 'playwright-alternative.js');
const altScriptExists = fs.existsSync(altScriptPath);
console.log(`- playwright-alternative.js: ${altScriptExists ? '✅' : '❌'}`);

// 检查修复脚本
const fixScriptPath = path.join(process.cwd(), 'fix-playwright-mcp.js');
const fixScriptExists = fs.existsSync(fixScriptPath);
console.log(`- fix-playwright-mcp.js: ${fixScriptExists ? '✅' : '❌'}`);

// 总结
console.log('\n📊 修复状态总结:');
const allGood = playwrightInstalled && zodFixed && altScriptExists && fixScriptExists;

if (allGood) {
  console.log('🎉 所有组件都已正确安装和修复！');
  console.log('\n🚀 可以使用以下命令进行测试:');
  console.log('1. 启动开发服务器: npm run dev');
  console.log('2. 运行 Playwright 测试: node playwright-alternative.js');
  console.log('3. 或运行修复脚本: node fix-playwright-mcp.js');
} else {
  console.log('⚠️  部分组件缺失，请检查安装过程');
  if (!playwrightInstalled) {
    console.log('- 请运行: npm install playwright --save-dev --legacy-peer-deps');
  }
  if (!zodFixed) {
    console.log('- 请运行: node fix-playwright-mcp.js');
  }
}

console.log('\n✨ 验证完成！');
