export interface CityDetail {
  id: string
  city: string
  cityEn: string
  country: string
  countryEn: string
  flag: string
  timezone: string
  utcOffset: string
  continent: string
  continentEn: string
  iana: string
  dstStatus: string
  dstStatusEn: string
  latitude: number
  longitude: number
  sunrise: string
  sunset: string
  population?: number
  currency?: string
  language?: string
  areaCode?: string
  elevation?: number
  website?: string
  established?: string
  mayor?: string
  gdp?: number
  area?: number
  density?: number
  nickname?: string
  nicknameEn?: string
  description?: string
  descriptionEn?: string
}

export class SQLiteService {
  private db: any = null
  private SQL: any = null

  async initialize(): Promise<void> {
    try {
      console.log('开始初始化 SQLiteService...')
      
      // 检查是否在浏览器环境中
      if (typeof window === 'undefined') {
        console.log('在服务器环境中，跳过 SQLite 初始化')
        return
      }

      // 动态导入 sql.js 以避免 SSR 问题
      console.log('导入 sql.js...')
      const initSqlJs = (await import('sql.js')).default

      // 初始化 SQL.js，指定 wasm 文件的路径
      console.log('初始化 SQL.js...')
      this.SQL = await initSqlJs({
        locateFile: (file: string) => {
          console.log('定位文件:', file)
          // 在浏览器环境中使用 public 目录的文件
          return `/${file}`
        }
      })

      console.log('创建数据库实例...')
      // 创建数据库
      this.db = new this.SQL.Database()
      
      console.log('创建表结构...')
      // 创建表结构
      await this.createTables()
      
      console.log('插入示例数据...')
      // 插入示例数据
      await this.insertSampleData()
      
      console.log('SQLite 数据库初始化完成')
    } catch (error) {
      console.error('SQLite 初始化失败:', error)
      throw error
    }
  }

  private async createTables(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS cities (
        id TEXT PRIMARY KEY,
        city TEXT NOT NULL,
        cityEn TEXT NOT NULL,
        country TEXT NOT NULL,
        countryEn TEXT NOT NULL,
        flag TEXT NOT NULL,
        timezone TEXT NOT NULL,
        utcOffset TEXT NOT NULL,
        continent TEXT NOT NULL,
        continentEn TEXT NOT NULL,
        iana TEXT NOT NULL,
        dstStatus TEXT NOT NULL,
        dstStatusEn TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        sunrise TEXT NOT NULL,
        sunset TEXT NOT NULL,
        population INTEGER,
        currency TEXT,
        language TEXT,
        areaCode TEXT,
        elevation INTEGER,
        website TEXT,
        established TEXT,
        mayor TEXT,
        gdp REAL,
        area REAL,
        density REAL,
        nickname TEXT,
        nicknameEn TEXT,
        description TEXT,
        descriptionEn TEXT
      );

      CREATE INDEX IF NOT EXISTS idx_city_name ON cities(city, cityEn);
      CREATE INDEX IF NOT EXISTS idx_country ON cities(country, countryEn);
      CREATE INDEX IF NOT EXISTS idx_continent ON cities(continent, continentEn);
      CREATE INDEX IF NOT EXISTS idx_timezone ON cities(timezone);
      CREATE INDEX IF NOT EXISTS idx_population ON cities(population);
    `

    this.db.exec(createTableSQL)
  }

  private async insertSampleData(): Promise<void> {
    // 检查是否已有数据
    const checkResult = this.db.exec("SELECT COUNT(*) as count FROM cities")
    if (checkResult.length > 0 && checkResult[0].values[0][0] > 0) {
      return // 已有数据，不需要重复插入
    }

    const cities = [
      {
        id: 'beijing',
        city: '北京',
        cityEn: 'Beijing',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 39.9042,
        longitude: 116.4074,
        sunrise: '07:30',
        sunset: '17:30',
        population: 21540000,
        currency: 'CNY',
        language: 'Chinese',
        areaCode: '+86',
        elevation: 43,
        website: 'http://www.beijing.gov.cn/',
        established: '1949',
        mayor: '殷勇',
        gdp: 4610000000000,
        area: 16410.54,
        density: 1313,
        nickname: '首都',
        nicknameEn: 'Capital City',
        description: '中华人民共和国首都，全国政治、文化、国际交往、科技创新中心。',
        descriptionEn: 'Capital of the People\'s Republic of China, serving as the nation\'s political, cultural, international exchange, and technological innovation center.'
      },
      {
        id: 'tokyo',
        city: '东京',
        cityEn: 'Tokyo',
        country: '日本',
        countryEn: 'Japan',
        flag: '🇯🇵',
        timezone: 'Asia/Tokyo',
        utcOffset: '+09:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Tokyo',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 35.6762,
        longitude: 139.6503,
        sunrise: '06:45',
        sunset: '16:45',
        population: 13960000,
        currency: 'JPY',
        language: 'Japanese',
        areaCode: '+81',
        elevation: 40,
        website: 'https://www.metro.tokyo.lg.jp/',
        established: '1868',
        mayor: '小池百合子',
        gdp: 1617000000000,
        area: 2194.07,
        density: 6363,
        nickname: '东京都',
        nicknameEn: 'Tokyo Metropolis',
        description: '日本首都，全球重要的经济、金融、文化中心。',
        descriptionEn: 'Capital of Japan and one of the world\'s most important economic, financial, and cultural centers.'
      },
      {
        id: 'new-york',
        city: '纽约',
        cityEn: 'New York',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/New_York',
        utcOffset: '-05:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/New_York',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 40.7128,
        longitude: -74.0060,
        sunrise: '07:15',
        sunset: '16:30',
        population: 8336000,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 10,
        website: 'https://www1.nyc.gov/',
        established: '1624',
        mayor: 'Eric Adams',
        gdp: 1770000000000,
        area: 778.2,
        density: 10715,
        nickname: '大苹果',
        nicknameEn: 'The Big Apple',
        description: '美国最大城市，全球金融、商业、文化中心。',
        descriptionEn: 'The largest city in the United States and a global center for finance, business, and culture.'
      },
      {
        id: 'london',
        city: '伦敦',
        cityEn: 'London',
        country: '英国',
        countryEn: 'United Kingdom',
        flag: '🇬🇧',
        timezone: 'Europe/London',
        utcOffset: '+00:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/London',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 51.5074,
        longitude: -0.1278,
        sunrise: '08:00',
        sunset: '15:45',
        population: 9648000,
        currency: 'GBP',
        language: 'English',
        areaCode: '+44',
        elevation: 35,
        website: 'https://www.london.gov.uk/',
        established: '47 AD',
        mayor: 'Sadiq Khan',
        gdp: 653000000000,
        area: 1572,
        density: 5598,
        nickname: '雾都',
        nicknameEn: 'The City of Fog',
        description: '英国首都，历史悠久的国际金融中心。',
        descriptionEn: 'Capital of the United Kingdom and a historic international financial center.'
      },
      {
        id: 'paris',
        city: '巴黎',
        cityEn: 'Paris',
        country: '法国',
        countryEn: 'France',
        flag: '🇫🇷',
        timezone: 'Europe/Paris',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Paris',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 48.8566,
        longitude: 2.3522,
        sunrise: '08:30',
        sunset: '16:15',
        population: 11020000,
        currency: 'EUR',
        language: 'French',
        areaCode: '+33',
        elevation: 35,
        website: 'https://www.paris.fr/',
        established: '3rd century BC',
        mayor: 'Anne Hidalgo',
        gdp: 779000000000,
        area: 105.4,
        density: 20755,
        nickname: '光之城',
        nicknameEn: 'City of Light',
        description: '法国首都，世界著名的浪漫之都和文化艺术中心。',
        descriptionEn: 'Capital of France, world-renowned romantic city and center of culture and arts.'
      },
      {
        id: 'sydney',
        city: '悉尼',
        cityEn: 'Sydney',
        country: '澳大利亚',
        countryEn: 'Australia',
        flag: '🇦🇺',
        timezone: 'Australia/Sydney',
        utcOffset: '+11:00',
        continent: '大洋洲',
        continentEn: 'Oceania',
        iana: 'Australia/Sydney',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: -33.8688,
        longitude: 151.2093,
        sunrise: '05:45',
        sunset: '19:30',
        population: 5312000,
        currency: 'AUD',
        language: 'English',
        areaCode: '+61',
        elevation: 3,
        website: 'https://www.cityofsydney.nsw.gov.au/',
        established: '1788',
        mayor: 'Clover Moore',
        gdp: 400000000000,
        area: 12368,
        density: 433,
        nickname: '海港城',
        nicknameEn: 'Harbour City',
        description: '澳大利亚最大城市，著名的国际化大都市。',
        descriptionEn: 'Australia\'s largest city and a famous international metropolis.'
      },
      {
        id: 'dubai',
        city: '迪拜',
        cityEn: 'Dubai',
        country: '阿联酋',
        countryEn: 'United Arab Emirates',
        flag: '🇦🇪',
        timezone: 'Asia/Dubai',
        utcOffset: '+04:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Dubai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 25.2048,
        longitude: 55.2708,
        sunrise: '06:30',
        sunset: '18:00',
        population: 3400000,
        currency: 'AED',
        language: 'Arabic',
        areaCode: '+971',
        elevation: 16,
        website: 'https://www.dubai.ae/',
        established: '1833',
        mayor: 'Mohammed bin Rashid Al Maktoum',
        gdp: 105000000000,
        area: 4114,
        density: 619,
        nickname: '沙漠之花',
        nicknameEn: 'Desert Flower',
        description: '阿联酋最大城市，中东地区的商业和金融中心。',
        descriptionEn: 'The largest city in the UAE and a major business and financial center in the Middle East.'
      },
      {
        id: 'singapore',
        city: '新加坡',
        cityEn: 'Singapore',
        country: '新加坡',
        countryEn: 'Singapore',
        flag: '🇸🇬',
        timezone: 'Asia/Singapore',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Singapore',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 1.3521,
        longitude: 103.8198,
        sunrise: '07:00',
        sunset: '19:00',
        population: 5454000,
        currency: 'SGD',
        language: 'English',
        areaCode: '+65',
        elevation: 15,
        website: 'https://www.gov.sg/',
        established: '1819',
        mayor: 'Lawrence Wong',
        gdp: ************,
        area: 719.1,
        density: 7804,
        nickname: '花园城市',
        nicknameEn: 'Garden City',
        description: '东南亚重要的金融、航运和贸易中心。',
        descriptionEn: 'A major financial, shipping, and trading center in Southeast Asia.'
      },
      {
        id: 'mumbai',
        city: '孟买',
        cityEn: 'Mumbai',
        country: '印度',
        countryEn: 'India',
        flag: '🇮🇳',
        timezone: 'Asia/Kolkata',
        utcOffset: '+05:30',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Kolkata',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 19.0760,
        longitude: 72.8777,
        sunrise: '06:45',
        sunset: '18:15',
        population: 20411000,
        currency: 'INR',
        language: 'Hindi',
        areaCode: '+91',
        elevation: 14,
        website: 'https://www.mcgm.gov.in/',
        established: '1507',
        mayor: 'Kishori Pednekar',
        gdp: ************,
        area: 603.4,
        density: 20694,
        nickname: '宝莱坞之都',
        nicknameEn: 'Bollywood Capital',
        description: '印度最大城市，重要的金融和娱乐中心。',
        descriptionEn: 'India\'s largest city and an important financial and entertainment center.'
      },
      {
        id: 'moscow',
        city: '莫斯科',
        cityEn: 'Moscow',
        country: '俄罗斯',
        countryEn: 'Russia',
        flag: '🇷🇺',
        timezone: 'Europe/Moscow',
        utcOffset: '+03:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Moscow',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 55.7558,
        longitude: 37.6173,
        sunrise: '08:45',
        sunset: '15:30',
        population: 12506000,
        currency: 'RUB',
        language: 'Russian',
        areaCode: '+7',
        elevation: 156,
        website: 'https://www.mos.ru/',
        established: '1147',
        mayor: 'Sergey Sobyanin',
        gdp: ************,
        area: 2511,
        density: 4925,
        nickname: '白石之城',
        nicknameEn: 'White Stone City',
        description: '俄罗斯首都，重要的政治、经济、文化中心。',
        descriptionEn: 'Capital of Russia and an important political, economic, and cultural center.'
      },

      // 新增亚洲城市
      {
        id: 'guangzhou',
        city: '广州',
        cityEn: 'Guangzhou',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 23.1291,
        longitude: 113.2644,
        sunrise: '06:45',
        sunset: '18:45',
        population: 15300000,
        currency: 'CNY',
        language: 'Chinese',
        areaCode: '+86',
        elevation: 21,
        website: 'https://www.guangzhou.gov.cn/',
        established: '214 BC',
        mayor: '郭永航',
        gdp: 2873000000000,
        area: 7434.4,
        density: 2059,
        nickname: '花城',
        nicknameEn: 'Flower City',
        description: '中国南方重要的中心城市，国际商贸中心和综合交通枢纽。',
        descriptionEn: 'An important central city in southern China, serving as an international commercial center and comprehensive transportation hub.'
      },
      {
        id: 'shenzhen',
        city: '深圳',
        cityEn: 'Shenzhen',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 22.5431,
        longitude: 114.0579,
        sunrise: '06:50',
        sunset: '18:50',
        population: 12590000,
        currency: 'CNY',
        language: 'Chinese',
        areaCode: '+86',
        elevation: 81,
        website: 'https://www.sz.gov.cn/',
        established: '1979',
        mayor: '覃伟中',
        gdp: 3240000000000,
        area: 1997.47,
        density: 6306,
        nickname: '鹏城',
        nicknameEn: 'Peng City',
        description: '中国改革开放的窗口，全球科技创新中心。',
        descriptionEn: 'China\'s window of reform and opening-up, and a global technology innovation center.'
      },
      {
        id: 'chengdu',
        city: '成都',
        cityEn: 'Chengdu',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 30.5728,
        longitude: 104.0668,
        sunrise: '07:00',
        sunset: '19:00',
        population: 16330000,
        currency: 'CNY',
        language: 'Chinese',
        areaCode: '+86',
        elevation: 505,
        website: 'https://www.chengdu.gov.cn/',
        established: '316 BC',
        mayor: '王凤朝',
        gdp: 1991000000000,
        area: 14335,
        density: 1139,
        nickname: '天府之国',
        nicknameEn: 'Land of Abundance',
        description: '中国西南地区的科技、商贸、金融中心和交通、通信枢纽。',
        descriptionEn: 'A major center for technology, commerce, finance, transportation, and communications in Southwest China.'
      },
      {
        id: 'hangzhou',
        city: '杭州',
        cityEn: 'Hangzhou',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 30.2741,
        longitude: 120.1551,
        sunrise: '06:20',
        sunset: '18:20',
        population: 11940000,
        currency: 'CNY',
        language: 'Chinese',
        areaCode: '+86',
        elevation: 19,
        website: 'https://www.hangzhou.gov.cn/',
        established: '221 BC',
        mayor: '刘忻',
        gdp: 1810000000000,
        area: 16596,
        density: 719,
        nickname: '人间天堂',
        nicknameEn: 'Paradise on Earth',
        description: '历史文化名城，中国电子商务之都。',
        descriptionEn: 'A famous historical and cultural city, known as China\'s e-commerce capital.'
      },
      {
        id: 'xian',
        city: '西安',
        cityEn: 'Xi\'an',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 34.3416,
        longitude: 108.9398,
        sunrise: '07:15',
        sunset: '18:15',
        population: 12950000,
        currency: 'CNY',
        language: 'Chinese',
        areaCode: '+86',
        elevation: 405,
        website: 'https://www.xa.gov.cn/',
        established: '1100 BC',
        mayor: '李明远',
        gdp: 1020000000000,
        area: 10108,
        density: 1281,
        nickname: '古都',
        nicknameEn: 'Ancient Capital',
        description: '中国历史文化名城，古丝绸之路起点。',
        descriptionEn: 'A famous historical and cultural city in China, starting point of the ancient Silk Road.'
      },
      {
        id: 'osaka',
        city: '大阪',
        cityEn: 'Osaka',
        country: '日本',
        countryEn: 'Japan',
        flag: '🇯🇵',
        timezone: 'Asia/Tokyo',
        utcOffset: '+09:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Tokyo',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 34.6937,
        longitude: 135.5023,
        sunrise: '06:50',
        sunset: '16:50',
        population: 2690000,
        currency: 'JPY',
        language: 'Japanese',
        areaCode: '+81',
        elevation: 5,
        website: 'https://www.city.osaka.lg.jp/',
        established: '1889',
        mayor: '松井一郎',
        gdp: 654000000000,
        area: 225.21,
        density: 11946,
        nickname: '天下の台所',
        nicknameEn: 'Nation\'s Kitchen',
        description: '日本第二大都市，关西地区经济、文化中心。',
        descriptionEn: 'Japan\'s second-largest city and the economic and cultural center of the Kansai region.'
      },
      {
        id: 'seoul',
        city: '首尔',
        cityEn: 'Seoul',
        country: '韩国',
        countryEn: 'South Korea',
        flag: '🇰🇷',
        timezone: 'Asia/Seoul',
        utcOffset: '+09:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Seoul',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 37.5665,
        longitude: 126.9780,
        sunrise: '07:30',
        sunset: '17:15',
        population: 9720000,
        currency: 'KRW',
        language: 'Korean',
        areaCode: '+82',
        elevation: 38,
        website: 'https://www.seoul.go.kr/',
        established: '18 BC',
        mayor: '오세훈',
        gdp: ************,
        area: 605.21,
        density: 16052,
        nickname: '汉江奇迹',
        nicknameEn: 'Miracle on the Han River',
        description: '韩国首都，东北亚重要的政治、经济、文化中心。',
        descriptionEn: 'Capital of South Korea and an important political, economic, and cultural center in Northeast Asia.'
      },
      {
        id: 'delhi',
        city: '德里',
        cityEn: 'Delhi',
        country: '印度',
        countryEn: 'India',
        flag: '🇮🇳',
        timezone: 'Asia/Kolkata',
        utcOffset: '+05:30',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Kolkata',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 28.7041,
        longitude: 77.1025,
        sunrise: '06:45',
        sunset: '17:45',
        population: 32900000,
        currency: 'INR',
        language: 'Hindi, English',
        areaCode: '+91',
        elevation: 216,
        website: 'https://delhi.gov.in/',
        established: '6th century BC',
        mayor: 'Arvind Kejriwal',
        gdp: ************,
        area: 1484,
        density: 22169,
        nickname: '印度心脏',
        nicknameEn: 'Heart of India',
        description: '印度首都，国家政治、文化中心。',
        descriptionEn: 'Capital of India and the country\'s political and cultural center.'
      },
      {
        id: 'bangalore',
        city: '班加罗尔',
        cityEn: 'Bangalore',
        country: '印度',
        countryEn: 'India',
        flag: '🇮🇳',
        timezone: 'Asia/Kolkata',
        utcOffset: '+05:30',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Kolkata',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 12.9716,
        longitude: 77.5946,
        sunrise: '06:15',
        sunset: '18:15',
        population: 13600000,
        currency: 'INR',
        language: 'Kannada, English',
        areaCode: '+91',
        elevation: 920,
        website: 'https://www.bbmp.gov.in/',
        established: '1537',
        mayor: 'Gangambike Mallikarjun',
        gdp: 110000000000,
        area: 741,
        density: 18357,
        nickname: '印度硅谷',
        nicknameEn: 'Silicon Valley of India',
        description: '印度IT产业中心，被称为印度硅谷。',
        descriptionEn: 'India\'s IT industry center, known as the Silicon Valley of India.'
      },
      {
        id: 'kuala-lumpur',
        city: '吉隆坡',
        cityEn: 'Kuala Lumpur',
        country: '马来西亚',
        countryEn: 'Malaysia',
        flag: '🇲🇾',
        timezone: 'Asia/Kuala_Lumpur',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Kuala_Lumpur',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 3.1390,
        longitude: 101.6869,
        sunrise: '07:15',
        sunset: '19:15',
        population: 8400000,
        currency: 'MYR',
        language: 'Malay, English',
        areaCode: '+60',
        elevation: 66,
        website: 'https://www.dbkl.gov.my/',
        established: '1857',
        mayor: 'Mahadi Che Ngah',
        gdp: 170000000000,
        area: 243,
        density: 34567,
        nickname: '花园城市',
        nicknameEn: 'Garden City',
        description: '马来西亚首都，东南亚重要的金融和商业中心。',
        descriptionEn: 'Capital of Malaysia and an important financial and commercial center in Southeast Asia.'
      },
      {
        id: 'manila',
        city: '马尼拉',
        cityEn: 'Manila',
        country: '菲律宾',
        countryEn: 'Philippines',
        flag: '🇵🇭',
        timezone: 'Asia/Manila',
        utcOffset: '+08:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Manila',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 14.5995,
        longitude: 120.9842,
        sunrise: '06:00',
        sunset: '18:00',
        population: 13480000,
        currency: 'PHP',
        language: 'Filipino, English',
        areaCode: '+63',
        elevation: 14,
        website: 'https://manila.gov.ph/',
        established: '1571',
        mayor: 'Francisco Domagoso',
        gdp: 250000000000,
        area: 42.88,
        density: 314448,
        nickname: '东方明珠',
        nicknameEn: 'Pearl of the Orient',
        description: '菲律宾首都，东南亚重要的港口和商业中心。',
        descriptionEn: 'Capital of the Philippines and an important port and commercial center in Southeast Asia.'
      },
      {
        id: 'ho-chi-minh',
        city: '胡志明市',
        cityEn: 'Ho Chi Minh City',
        country: '越南',
        countryEn: 'Vietnam',
        flag: '🇻🇳',
        timezone: 'Asia/Ho_Chi_Minh',
        utcOffset: '+07:00',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Ho_Chi_Minh',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 10.8231,
        longitude: 106.6297,
        sunrise: '06:00',
        sunset: '18:00',
        population: 9400000,
        currency: 'VND',
        language: 'Vietnamese',
        areaCode: '+84',
        elevation: 5,
        website: 'https://www.hochiminhcity.gov.vn/',
        established: '1698',
        mayor: 'Phan Van Mai',
        gdp: 171000000000,
        area: 2061,
        density: 4564,
        nickname: '东方巴黎',
        nicknameEn: 'Paris of the East',
        description: '越南最大城市，国家经济中心。',
        descriptionEn: 'Vietnam\'s largest city and the country\'s economic center.'
      },

      // 新增欧洲城市
      {
        id: 'manchester',
        city: '曼彻斯特',
        cityEn: 'Manchester',
        country: '英国',
        countryEn: 'United Kingdom',
        flag: '🇬🇧',
        timezone: 'Europe/London',
        utcOffset: '+00:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/London',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 53.4808,
        longitude: -2.2426,
        sunrise: '08:15',
        sunset: '15:30',
        population: 2720000,
        currency: 'GBP',
        language: 'English',
        areaCode: '+44',
        elevation: 38,
        website: 'https://www.manchester.gov.uk/',
        established: '79 AD',
        mayor: 'Andy Burnham',
        gdp: 88000000000,
        area: 1276,
        density: 2131,
        nickname: '工业革命之城',
        nicknameEn: 'Cradle of Industrial Revolution',
        description: '英国重要的工业城市，足球之城。',
        descriptionEn: 'An important industrial city in the UK, known as the football city.'
      },
      {
        id: 'lyon',
        city: '里昂',
        cityEn: 'Lyon',
        country: '法国',
        countryEn: 'France',
        flag: '🇫🇷',
        timezone: 'Europe/Paris',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Paris',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 45.7640,
        longitude: 4.8357,
        sunrise: '08:00',
        sunset: '16:45',
        population: 2280000,
        currency: 'EUR',
        language: 'French',
        areaCode: '+33',
        elevation: 173,
        website: 'https://www.lyon.fr/',
        established: '43 BC',
        mayor: 'Grégory Doucet',
        gdp: 74000000000,
        area: 47.87,
        density: 10476,
        nickname: '美食之都',
        nicknameEn: 'Gastronomic Capital',
        description: '法国第三大城市，著名的美食和丝绸之都。',
        descriptionEn: 'France\'s third-largest city, famous for gastronomy and silk production.'
      },
      {
        id: 'berlin',
        city: '柏林',
        cityEn: 'Berlin',
        country: '德国',
        countryEn: 'Germany',
        flag: '🇩🇪',
        timezone: 'Europe/Berlin',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Berlin',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 52.5200,
        longitude: 13.4050,
        sunrise: '08:15',
        sunset: '16:30',
        population: 3677000,
        currency: 'EUR',
        language: 'German',
        areaCode: '+49',
        elevation: 34,
        website: 'https://www.berlin.de/',
        established: '1237',
        mayor: 'Kai Wegner',
        gdp: 147000000000,
        area: 891.8,
        density: 4122,
        nickname: '自由之城',
        nicknameEn: 'City of Freedom',
        description: '德国首都，欧洲重要的政治、文化、科技中心。',
        descriptionEn: 'Capital of Germany and an important political, cultural, and technological center in Europe.'
      },
      {
        id: 'munich',
        city: '慕尼黑',
        cityEn: 'Munich',
        country: '德国',
        countryEn: 'Germany',
        flag: '🇩🇪',
        timezone: 'Europe/Berlin',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Berlin',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 48.1351,
        longitude: 11.5820,
        sunrise: '08:00',
        sunset: '16:45',
        population: 1488000,
        currency: 'EUR',
        language: 'German',
        areaCode: '+49',
        elevation: 519,
        website: 'https://www.muenchen.de/',
        established: '1158',
        mayor: 'Dieter Reiter',
        gdp: 109000000000,
        area: 310.4,
        density: 4794,
        nickname: '啤酒之都',
        nicknameEn: 'Beer Capital',
        description: '德国巴伐利亚州首府，著名的啤酒节举办地。',
        descriptionEn: 'Capital of Bavaria, Germany, famous for hosting Oktoberfest.'
      },
      {
        id: 'hamburg',
        city: '汉堡',
        cityEn: 'Hamburg',
        country: '德国',
        countryEn: 'Germany',
        flag: '🇩🇪',
        timezone: 'Europe/Berlin',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Berlin',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 53.5511,
        longitude: 9.9937,
        sunrise: '08:30',
        sunset: '16:00',
        population: 1900000,
        currency: 'EUR',
        language: 'German',
        areaCode: '+49',
        elevation: 6,
        website: 'https://www.hamburg.de/',
        established: '808',
        mayor: 'Peter Tschentscher',
        gdp: 110000000000,
        area: 755,
        density: 2516,
        nickname: '北方威尼斯',
        nicknameEn: 'Venice of the North',
        description: '德国第二大城市，重要的港口和媒体中心。',
        descriptionEn: 'Germany\'s second-largest city and an important port and media center.'
      },
      {
        id: 'rome',
        city: '罗马',
        cityEn: 'Rome',
        country: '意大利',
        countryEn: 'Italy',
        flag: '🇮🇹',
        timezone: 'Europe/Rome',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Rome',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 41.9028,
        longitude: 12.4964,
        sunrise: '07:30',
        sunset: '17:00',
        population: 4340000,
        currency: 'EUR',
        language: 'Italian',
        areaCode: '+39',
        elevation: 21,
        website: 'https://www.comune.roma.it/',
        established: '753 BC',
        mayor: 'Roberto Gualtieri',
        gdp: 147000000000,
        area: 1285,
        density: 3378,
        nickname: '永恒之城',
        nicknameEn: 'The Eternal City',
        description: '意大利首都，古罗马帝国的发源地，世界历史文化名城。',
        descriptionEn: 'Capital of Italy, birthplace of the Roman Empire, and a world-renowned historical and cultural city.'
      },
      {
        id: 'milan',
        city: '米兰',
        cityEn: 'Milan',
        country: '意大利',
        countryEn: 'Italy',
        flag: '🇮🇹',
        timezone: 'Europe/Rome',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Rome',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 45.4642,
        longitude: 9.1900,
        sunrise: '07:45',
        sunset: '17:15',
        population: 3140000,
        currency: 'EUR',
        language: 'Italian',
        areaCode: '+39',
        elevation: 122,
        website: 'https://www.comune.milano.it/',
        established: '400 BC',
        mayor: 'Giuseppe Sala',
        gdp: 158000000000,
        area: 181.8,
        density: 7315,
        nickname: '时尚之都',
        nicknameEn: 'Fashion Capital',
        description: '意大利经济首都，世界时尚和设计之都。',
        descriptionEn: 'Economic capital of Italy and world capital of fashion and design.'
      },
      {
        id: 'madrid',
        city: '马德里',
        cityEn: 'Madrid',
        country: '西班牙',
        countryEn: 'Spain',
        flag: '🇪🇸',
        timezone: 'Europe/Madrid',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Madrid',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 40.4168,
        longitude: -3.7038,
        sunrise: '08:30',
        sunset: '17:45',
        population: 6640000,
        currency: 'EUR',
        language: 'Spanish',
        areaCode: '+34',
        elevation: 650,
        website: 'https://www.madrid.es/',
        established: '9th century',
        mayor: 'José Luis Martínez-Almeida',
        gdp: 230000000000,
        area: 604.3,
        density: 5265,
        nickname: '艺术之都',
        nicknameEn: 'City of Art',
        description: '西班牙首都，欧洲重要的艺术和文化中心。',
        descriptionEn: 'Capital of Spain and an important art and cultural center in Europe.'
      },
      {
        id: 'barcelona',
        city: '巴塞罗那',
        cityEn: 'Barcelona',
        country: '西班牙',
        countryEn: 'Spain',
        flag: '🇪🇸',
        timezone: 'Europe/Madrid',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Madrid',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 41.3851,
        longitude: 2.1734,
        sunrise: '08:00',
        sunset: '17:30',
        population: 5575000,
        currency: 'EUR',
        language: 'Spanish, Catalan',
        areaCode: '+34',
        elevation: 12,
        website: 'https://www.barcelona.cat/',
        established: '15 BC',
        mayor: 'Jaume Collboni',
        gdp: 177000000000,
        area: 101.4,
        density: 15991,
        nickname: '地中海明珠',
        nicknameEn: 'Pearl of the Mediterranean',
        description: '西班牙第二大城市，地中海重要的港口和旅游城市。',
        descriptionEn: 'Spain\'s second-largest city and an important Mediterranean port and tourist destination.'
      },
      {
        id: 'amsterdam',
        city: '阿姆斯特丹',
        cityEn: 'Amsterdam',
        country: '荷兰',
        countryEn: 'Netherlands',
        flag: '🇳🇱',
        timezone: 'Europe/Amsterdam',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Amsterdam',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 52.3676,
        longitude: 4.9041,
        sunrise: '08:30',
        sunset: '16:30',
        population: 2480000,
        currency: 'EUR',
        language: 'Dutch',
        areaCode: '+31',
        elevation: -2,
        website: 'https://www.amsterdam.nl/',
        established: '1275',
        mayor: 'Femke Halsema',
        gdp: 96000000000,
        area: 219.3,
        density: 4908,
        nickname: '运河之城',
        nicknameEn: 'Venice of the North',
        description: '荷兰首都，以其美丽的运河系统和自由文化著称。',
        descriptionEn: 'Capital of the Netherlands, famous for its beautiful canal system and liberal culture.'
      },
      {
        id: 'zurich',
        city: '苏黎世',
        cityEn: 'Zurich',
        country: '瑞士',
        countryEn: 'Switzerland',
        flag: '🇨🇭',
        timezone: 'Europe/Zurich',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Zurich',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 47.3769,
        longitude: 8.5417,
        sunrise: '08:00',
        sunset: '16:30',
        population: 1380000,
        currency: 'CHF',
        language: 'German',
        areaCode: '+41',
        elevation: 408,
        website: 'https://www.stadt-zuerich.ch/',
        established: '15 BC',
        mayor: 'Corine Mauch',
        gdp: 63000000000,
        area: 87.88,
        density: 4700,
        nickname: '金融之都',
        nicknameEn: 'Financial Capital',
        description: '瑞士最大城市，世界重要的金融中心。',
        descriptionEn: 'Switzerland\'s largest city and a major global financial center.'
      },
      {
        id: 'st-petersburg',
        city: '圣彼得堡',
        cityEn: 'St. Petersburg',
        country: '俄罗斯',
        countryEn: 'Russia',
        flag: '🇷🇺',
        timezone: 'Europe/Moscow',
        utcOffset: '+03:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Moscow',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 59.9311,
        longitude: 30.3609,
        sunrise: '09:30',
        sunset: '15:00',
        population: 5384000,
        currency: 'RUB',
        language: 'Russian',
        areaCode: '+7',
        elevation: 3,
        website: 'https://www.gov.spb.ru/',
        established: '1703',
        mayor: 'Alexander Beglov',
        gdp: 86000000000,
        area: 1439,
        density: 3741,
        nickname: '北方威尼斯',
        nicknameEn: 'Venice of the North',
        description: '俄罗斯第二大城市，前帝国首都，文化艺术中心。',
        descriptionEn: 'Russia\'s second-largest city, former imperial capital, and center of culture and arts.'
      },
      {
        id: 'stockholm',
        city: '斯德哥尔摩',
        cityEn: 'Stockholm',
        country: '瑞典',
        countryEn: 'Sweden',
        flag: '🇸🇪',
        timezone: 'Europe/Stockholm',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Stockholm',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 59.3293,
        longitude: 18.0686,
        sunrise: '09:00',
        sunset: '15:30',
        population: 2390000,
        currency: 'SEK',
        language: 'Swedish',
        areaCode: '+46',
        elevation: 28,
        website: 'https://www.stockholm.se/',
        established: '1252',
        mayor: 'Anna König Jerlmyr',
        gdp: 74000000000,
        area: 188,
        density: 5200,
        nickname: '北方威尼斯',
        nicknameEn: 'Venice of the North',
        description: '瑞典首都，诺贝尔奖颁奖地，北欧设计之都。',
        descriptionEn: 'Capital of Sweden, home of the Nobel Prize, and capital of Nordic design.'
      },
      {
        id: 'copenhagen',
        city: '哥本哈根',
        cityEn: 'Copenhagen',
        country: '丹麦',
        countryEn: 'Denmark',
        flag: '🇩🇰',
        timezone: 'Europe/Copenhagen',
        utcOffset: '+01:00',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Copenhagen',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 55.6761,
        longitude: 12.5683,
        sunrise: '08:45',
        sunset: '15:45',
        population: 2057000,
        currency: 'DKK',
        language: 'Danish',
        areaCode: '+45',
        elevation: 24,
        website: 'https://www.kk.dk/',
        established: '1167',
        mayor: 'Sophie Hæstorp Andersen',
        gdp: 61000000000,
        area: 86.4,
        density: 6800,
        nickname: '童话之城',
        nicknameEn: 'City of Fairy Tales',
        description: '丹麦首都，安徒生童话的故乡，北欧设计中心。',
        descriptionEn: 'Capital of Denmark, home of Hans Christian Andersen\'s fairy tales, and Nordic design center.'
      },
      // 北美洲城市
      {
        id: 'los-angeles',
        city: '洛杉矶',
        cityEn: 'Los Angeles',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/Los_Angeles',
        utcOffset: '-08:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Los_Angeles',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 34.0522,
        longitude: -118.2437,
        sunrise: '06:45',
        sunset: '17:00',
        population: 3898747,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 87,
        website: 'https://www.lacity.org',
        established: '1781',
        mayor: 'Karen Bass',
        gdp: 1000000000000,
        area: 1302,
        density: 2996,
        nickname: '天使之城',
        nicknameEn: 'City of Angels',
        description: '世界娱乐之都，美国第二大城市。',
        descriptionEn: 'The entertainment capital of the world and second-largest city in the US.'
      },
      {
        id: 'chicago',
        city: '芝加哥',
        cityEn: 'Chicago',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/Chicago',
        utcOffset: '-06:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Chicago',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 41.8781,
        longitude: -87.6298,
        sunrise: '07:15',
        sunset: '16:30',
        population: 2695598,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 179,
        website: 'https://www.chicago.gov',
        established: '1837',
        mayor: 'Brandon Johnson',
        gdp: 689000000000,
        area: 606.1,
        density: 4447,
        nickname: '风城',
        nicknameEn: 'Windy City',
        description: '美国第三大城市，以建筑和深盘披萨闻名。',
        descriptionEn: 'The third-largest city in the US, known for its architecture and deep-dish pizza.'
      },
      {
        id: 'houston',
        city: '休斯顿',
        cityEn: 'Houston',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/Chicago',
        utcOffset: '-06:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Chicago',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 29.7604,
        longitude: -95.3698,
        sunrise: '07:00',
        sunset: '17:30',
        population: 2320268,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 13,
        website: 'https://www.houstontx.gov',
        established: '1836',
        mayor: 'Sylvester Turner',
        gdp: 478000000000,
        area: 1651.8,
        density: 1405,
        nickname: '太空城',
        nicknameEn: 'Space City',
        description: '美国第四大城市，航天工业中心。',
        descriptionEn: 'The fourth-largest city in the US and center of the space industry.'
      },
      {
        id: 'miami',
        city: '迈阿密',
        cityEn: 'Miami',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/New_York',
        utcOffset: '-05:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/New_York',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 25.7617,
        longitude: -80.1918,
        sunrise: '07:00',
        sunset: '18:00',
        population: 470914,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 2,
        website: 'https://www.miamigov.com',
        established: '1896',
        mayor: 'Francis Suarez',
        gdp: 344000000000,
        area: 143.1,
        density: 3292,
        nickname: '魔法城',
        nicknameEn: 'Magic City',
        description: '重要港口城市，以海滩、装饰艺术建筑和夜生活闻名。',
        descriptionEn: 'A major port city known for its beaches, Art Deco architecture, and nightlife.'
      },
      {
        id: 'san-francisco',
        city: '旧金山',
        cityEn: 'San Francisco',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/Los_Angeles',
        utcOffset: '-08:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Los_Angeles',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 37.7749,
        longitude: -122.4194,
        sunrise: '07:00',
        sunset: '17:15',
        population: 873965,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 16,
        website: 'https://sf.gov',
        established: '1776',
        mayor: 'London Breed',
        gdp: 549000000000,
        area: 121.4,
        density: 7197,
        nickname: '雾城',
        nicknameEn: 'Fog City',
        description: '科技中心，以金门大桥和陡峭山丘闻名。',
        descriptionEn: 'A tech hub famous for the Golden Gate Bridge and steep hills.'
      },
      {
        id: 'las-vegas',
        city: '拉斯维加斯',
        cityEn: 'Las Vegas',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/Los_Angeles',
        utcOffset: '-08:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Los_Angeles',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 36.1699,
        longitude: -115.1398,
        sunrise: '06:30',
        sunset: '17:45',
        population: 651319,
        currency: 'USD',
        language: 'English',
        areaCode: '+1',
        elevation: 610,
        website: 'https://www.lasvegasnevada.gov',
        established: '1905',
        mayor: 'Carolyn Goodman',
        gdp: 57000000000,
        area: 352,
        density: 1851,
        nickname: '罪恶之城',
        nicknameEn: 'Sin City',
        description: '娱乐之都，以赌场、表演和夜生活闻名。',
        descriptionEn: 'The entertainment capital known for casinos, shows, and nightlife.'
      },
      {
        id: 'toronto',
        city: '多伦多',
        cityEn: 'Toronto',
        country: '加拿大',
        countryEn: 'Canada',
        flag: '🇨🇦',
        timezone: 'America/Toronto',
        utcOffset: '-05:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Toronto',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 43.6532,
        longitude: -79.3832,
        sunrise: '07:30',
        sunset: '16:45',
        population: 2794356,
        currency: 'CAD',
        language: 'English',
        areaCode: '+1',
        elevation: 76,
        website: 'https://www.toronto.ca',
        established: '1793',
        mayor: 'Olivia Chow',
        gdp: 395000000000,
        area: 630.2,
        density: 4434,
        nickname: '多元文化之都',
        nicknameEn: 'The Six',
        description: '加拿大最大城市和金融中心。',
        descriptionEn: 'Canada\'s largest city and financial center.'
      },
      {
        id: 'vancouver',
        city: '温哥华',
        cityEn: 'Vancouver',
        country: '加拿大',
        countryEn: 'Canada',
        flag: '🇨🇦',
        timezone: 'America/Vancouver',
        utcOffset: '-08:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Vancouver',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 49.2827,
        longitude: -123.1207,
        sunrise: '08:00',
        sunset: '16:15',
        population: 675218,
        currency: 'CAD',
        language: 'English',
        areaCode: '+1',
        elevation: 0,
        website: 'https://vancouver.ca',
        established: '1886',
        mayor: 'Ken Sim',
        gdp: 135000000000,
        area: 114.97,
        density: 5868,
        nickname: '雨城',
        nicknameEn: 'Rain City',
        description: '沿海城市，以自然美景和温和气候闻名。',
        descriptionEn: 'A coastal city known for its natural beauty and mild climate.'
      },
      {
        id: 'montreal',
        city: '蒙特利尔',
        cityEn: 'Montreal',
        country: '加拿大',
        countryEn: 'Canada',
        flag: '🇨🇦',
        timezone: 'America/Toronto',
        utcOffset: '-05:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Toronto',
        dstStatus: '实行夏令时',
        dstStatusEn: 'DST Observed',
        latitude: 45.5017,
        longitude: -73.5673,
        sunrise: '07:45',
        sunset: '16:30',
        population: 1704694,
        currency: 'CAD',
        language: 'French',
        areaCode: '+1',
        elevation: 36,
        website: 'https://montreal.ca',
        established: '1642',
        mayor: 'Valérie Plante',
        gdp: 197000000000,
        area: 431.5,
        density: 3950,
        nickname: '北美巴黎',
        nicknameEn: 'Paris of North America',
        description: '法语城市，以文化和节庆闻名。',
        descriptionEn: 'A French-speaking city known for its culture and festivals.'
      },
      {
        id: 'mexico-city',
        city: '墨西哥城',
        cityEn: 'Mexico City',
        country: '墨西哥',
        countryEn: 'Mexico',
        flag: '🇲🇽',
        timezone: 'America/Mexico_City',
        utcOffset: '-06:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Mexico_City',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 19.4326,
        longitude: -99.1332,
        sunrise: '07:00',
        sunset: '18:30',
        population: 9209944,
        currency: 'MXN',
        language: 'Spanish',
        areaCode: '+52',
        elevation: 2240,
        website: 'https://www.cdmx.gob.mx',
        established: '1325',
        mayor: 'Martí Batres',
        gdp: 411000000000,
        area: 1485,
        density: 6200,
        nickname: '阿兹特克之城',
        nicknameEn: 'City of Aztecs',
        description: '墨西哥首都和最大城市，建在古阿兹特克遗址上。',
        descriptionEn: 'The capital and largest city of Mexico, built on ancient Aztec ruins.'
      },
      {
        id: 'guadalajara',
        city: '瓜达拉哈拉',
        cityEn: 'Guadalajara',
        country: '墨西哥',
        countryEn: 'Mexico',
        flag: '🇲🇽',
        timezone: 'America/Mexico_City',
        utcOffset: '-06:00',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/Mexico_City',
        dstStatus: '不实行夏令时',
        dstStatusEn: 'No DST',
        latitude: 20.6597,
        longitude: -103.3496,
        sunrise: '07:15',
        sunset: '18:45',
        population: 1385629,
        currency: 'MXN',
        language: 'Spanish',
        areaCode: '+52',
        elevation: 1566,
        website: 'https://guadalajara.gob.mx',
        established: '1542',
        mayor: 'Pablo Lemus',
        gdp: 85000000000,
        area: 151,
        density: 9178,
        nickname: '墨西哥硅谷',
        nicknameEn: 'Silicon Valley of Mexico',
        description: '墨西哥科技中心和文化中心，以墨西哥流浪乐队音乐闻名。',
        descriptionEn: 'Mexico\'s tech hub and cultural center, known for mariachi music.'
      }
    ]

    // 批量插入数据
    const insertSQL = `
      INSERT INTO cities (
        id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
        continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
        sunrise, sunset, population, currency, language, areaCode, elevation,
        website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `

    const stmt = this.db.prepare(insertSQL)
    for (const city of cities) {
      stmt.run([
        city.id, city.city, city.cityEn, city.country, city.countryEn, 
        city.flag, city.timezone, city.utcOffset, city.continent, city.continentEn,
        city.iana, city.dstStatus, city.dstStatusEn, city.latitude, city.longitude,
        city.sunrise, city.sunset, city.population, city.currency, city.language,
        city.areaCode, city.elevation, city.website, city.established, city.mayor,
        city.gdp, city.area, city.density, city.nickname, city.nicknameEn, city.description, city.descriptionEn
      ])
    }
    stmt.free()
  }

  async getAllCities(): Promise<CityDetail[]> {
    if (!this.db) {
      console.error('数据库未初始化')
      return []
    }
    
    try {
      const stmt = this.db.prepare("SELECT * FROM cities ORDER BY city")
      const result = []
      while (stmt.step()) {
        const row = stmt.getAsObject()
        result.push(this.rowToCity(row))
      }
      stmt.free()
      return result
    } catch (error) {
      console.error('获取所有城市失败:', error)
      return []
    }
  }

  async getCityById(id: string): Promise<CityDetail | null> {
    if (!this.db) {
      console.error('数据库未初始化')
      return null
    }
    
    try {
      const stmt = this.db.prepare("SELECT * FROM cities WHERE id = ?")
      stmt.bind([id])
      
      if (stmt.step()) {
        const row = stmt.getAsObject()
        stmt.free()
        return this.rowToCity(row)
      }
      
      stmt.free()
      return null
    } catch (error) {
      console.error('根据ID获取城市失败:', error)
      return null
    }
  }

  async searchCities(query: string): Promise<CityDetail[]> {
    const searchTerm = `%${query}%`
    const stmt = this.db.prepare(`
      SELECT * FROM cities 
      WHERE city LIKE ? OR cityEn LIKE ? OR country LIKE ? OR countryEn LIKE ?
      ORDER BY 
        CASE 
          WHEN city LIKE ? OR cityEn LIKE ? THEN 1
          WHEN country LIKE ? OR countryEn LIKE ? THEN 2
          ELSE 3
        END,
        city
    `)
    
    stmt.bind([searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm])
    
    const result = []
    while (stmt.step()) {
      const row = stmt.getAsObject()
      result.push(this.rowToCity(row))
    }
    stmt.free()
    return result
  }

  async getCitiesByContinent(continent: string): Promise<CityDetail[]> {
    const stmt = this.db.prepare("SELECT * FROM cities WHERE continent = ? OR continentEn = ? ORDER BY city")
    stmt.bind([continent, continent])
    
    const result = []
    while (stmt.step()) {
      const row = stmt.getAsObject()
      result.push(this.rowToCity(row))
    }
    stmt.free()
    return result
  }

  async getCitiesByCountry(country: string): Promise<CityDetail[]> {
    const stmt = this.db.prepare("SELECT * FROM cities WHERE country = ? OR countryEn = ? ORDER BY city")
    stmt.bind([country, country])
    
    const result = []
    while (stmt.step()) {
      const row = stmt.getAsObject()
      result.push(this.rowToCity(row))
    }
    stmt.free()
    return result
  }

  async getPopularCities(limit: number = 10): Promise<CityDetail[]> {
    const stmt = this.db.prepare("SELECT * FROM cities ORDER BY population DESC LIMIT ?")
    stmt.bind([limit])
    
    const result = []
    while (stmt.step()) {
      const row = stmt.getAsObject()
      result.push(this.rowToCity(row))
    }
    stmt.free()
    return result
  }

  async addCity(city: CityDetail): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        INSERT INTO cities (
          id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
          continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
          sunrise, sunset, population, currency, language, areaCode, elevation,
          website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)
      
      stmt.run([
        city.id, city.city, city.cityEn, city.country, city.countryEn, 
        city.flag, city.timezone, city.utcOffset, city.continent, city.continentEn,
        city.iana, city.dstStatus, city.dstStatusEn, city.latitude, city.longitude,
        city.sunrise, city.sunset, city.population, city.currency, city.language,
        city.areaCode, city.elevation, city.website, city.established, city.mayor,
        city.gdp, city.area, city.density, city.nickname, city.nicknameEn, city.description, city.descriptionEn
      ])
      
      stmt.free()
      return true
    } catch (error) {
      console.error('添加城市失败:', error)
      return false
    }
  }

  async updateCity(city: CityDetail): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        UPDATE cities SET 
          city = ?, cityEn = ?, country = ?, countryEn = ?, flag = ?, timezone = ?, 
          utcOffset = ?, continent = ?, continentEn = ?, iana = ?, dstStatus = ?, 
          dstStatusEn = ?, latitude = ?, longitude = ?, sunrise = ?, sunset = ?,
          population = ?, currency = ?, language = ?, areaCode = ?, elevation = ?,
          website = ?, established = ?, mayor = ?, gdp = ?, area = ?, density = ?,
          nickname = ?, nicknameEn = ?, description = ?, descriptionEn = ?
        WHERE id = ?
      `)
      
      stmt.run([
        city.city, city.cityEn, city.country, city.countryEn, city.flag, city.timezone,
        city.utcOffset, city.continent, city.continentEn, city.iana, city.dstStatus,
        city.dstStatusEn, city.latitude, city.longitude, city.sunrise, city.sunset,
        city.population, city.currency, city.language, city.areaCode, city.elevation,
        city.website, city.established, city.mayor, city.gdp, city.area, city.density,
        city.nickname, city.nicknameEn, city.description, city.descriptionEn, city.id
      ])
      
      stmt.free()
      return true
    } catch (error) {
      console.error('更新城市失败:', error)
      return false
    }
  }

  async deleteCity(id: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare("DELETE FROM cities WHERE id = ?")
      stmt.run([id])
      stmt.free()
      return true
    } catch (error) {
      console.error('删除城市失败:', error)
      return false
    }
  }

  private rowToCity(row: any): CityDetail {
    return {
      id: row.id,
      city: row.city,
      cityEn: row.cityEn,
      country: row.country,
      countryEn: row.countryEn,
      flag: row.flag,
      timezone: row.timezone,
      utcOffset: row.utcOffset,
      continent: row.continent,
      continentEn: row.continentEn,
      iana: row.iana,
      dstStatus: row.dstStatus,
      dstStatusEn: row.dstStatusEn,
      latitude: row.latitude,
      longitude: row.longitude,
      sunrise: row.sunrise,
      sunset: row.sunset,
      population: row.population,
      currency: row.currency,
      language: row.language,
      areaCode: row.areaCode,
      elevation: row.elevation,
      website: row.website,
      established: row.established,
      mayor: row.mayor,
      gdp: row.gdp,
      area: row.area,
      density: row.density,
      nickname: row.nickname,
      nicknameEn: row.nicknameEn,
      description: row.description,
      descriptionEn: row.descriptionEn
    }
  }
} 