const Database = require('better-sqlite3');
const path = require('path');

// 导入额外的亚洲城市数据
const additionalAsiaCities = require('./additional-asia-cities.js');

// 数据库文件路径
const dbPath = path.join(__dirname, 'data', 'cities.db');

// 连接数据库
const db = new Database(dbPath);

try {
  // 准备插入语句
  const insertSQL = `
    INSERT OR REPLACE INTO cities (
      id, city, cityEn, country, countryEn, flag, timezone, utcOffset,
      continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
      sunrise, sunset, population, currency, language, areaCode, elevation,
      website, established, mayor, gdp, area, density, nickname, nicknameEn,
      description, descriptionEn
    ) VALUES (
      ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
    )
  `;

  const stmt = db.prepare(insertSQL);

  // 开始事务
  const transaction = db.transaction(() => {
    let successCount = 0;
    let errorCount = 0;

    additionalAsiaCities.forEach((city, index) => {
      try {
        stmt.run(
          city.id, city.city, city.cityEn, city.country, city.countryEn,
          city.flag, city.timezone, city.utcOffset, city.continent, city.continentEn,
          city.iana, city.dstStatus, city.dstStatusEn, city.latitude, city.longitude,
          city.sunrise, city.sunset, city.population, city.currency, city.language,
          city.areaCode, city.elevation, city.website, city.established, city.mayor,
          city.gdp, city.area, city.density, city.nickname, city.nicknameEn,
          city.description, city.descriptionEn
        );
        successCount++;
        console.log(`✅ ${index + 1}. ${city.city} (${city.cityEn}) - 添加成功`);
      } catch (error) {
        errorCount++;
        console.error(`❌ ${index + 1}. ${city.city} (${city.cityEn}) - 添加失败:`, error.message);
      }
    });

    return { successCount, errorCount };
  });

  // 执行事务
  const result = transaction();

  console.log('\n🎉 额外亚洲城市数据添加完成！');
  console.log('='.repeat(50));
  console.log(`✅ 成功添加: ${result.successCount} 个城市`);
  console.log(`❌ 添加失败: ${result.errorCount} 个城市`);
  console.log(`📊 总计处理: ${additionalAsiaCities.length} 个城市`);

  // 按国家统计
  const countryStats = {};
  additionalAsiaCities.forEach(city => {
    if (!countryStats[city.country]) {
      countryStats[city.country] = [];
    }
    countryStats[city.country].push(city.city);
  });

  console.log('\n📍 按国家统计:');
  console.log('-'.repeat(30));
  Object.entries(countryStats).forEach(([country, cities]) => {
    console.log(`${country}: ${cities.length} 个城市`);
    cities.forEach(city => {
      console.log(`  • ${city}`);
    });
  });

  // 验证数据库中的城市数量
  const totalCities = db.prepare('SELECT COUNT(*) as count FROM cities').get();
  console.log(`\n📊 数据库中总城市数量: ${totalCities.count}`);

  // 显示亚洲城市数量
  const asiaCities = db.prepare('SELECT COUNT(*) as count FROM cities WHERE continent = ?').get('亚洲');
  console.log(`🌏 亚洲城市数量: ${asiaCities.count}`);

  // 显示各大洲城市数量
  const continentStats = db.prepare(`
    SELECT continent, COUNT(*) as count 
    FROM cities 
    GROUP BY continent 
    ORDER BY count DESC
  `).all();

  console.log('\n🌍 各大洲城市数量:');
  console.log('-'.repeat(25));
  continentStats.forEach(stat => {
    console.log(`${stat.continent}: ${stat.count} 个城市`);
  });

} catch (error) {
  console.error('❌ 数据库操作失败:', error);
} finally {
  // 关闭数据库连接
  db.close();
  console.log('\n✅ 数据库连接已关闭');
} 