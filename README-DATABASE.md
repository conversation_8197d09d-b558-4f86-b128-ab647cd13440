# 数据库方案对比与使用说明

## 🆚 JSON vs SQLite 对比

### JSON文件方案
**优势：**
- ✅ 简单直接，无需额外配置
- ✅ 首屏加载快（小数据量）
- ✅ 适合静态部署
- ✅ 无依赖库需求

**劣势：**
- ❌ 数据量大时性能差
- ❌ 内存占用高（全量加载）
- ❌ 查询功能弱
- ❌ 无法动态添加数据
- ❌ 不支持复杂查询

### SQLite方案
**优势：**
- ✅ 强大的SQL查询能力
- ✅ 支持索引，查询速度快
- ✅ 支持动态增删改查
- ✅ 内存使用高效
- ✅ 支持复杂查询和关联
- ✅ 可扩展性强

**劣势：**
- ❌ 需要额外依赖库
- ❌ 初始化时间较长
- ❌ 文件大小略大

## 🚀 建议使用SQLite方案

考虑到你的需求：
1. **城市数据库太少** - SQLite可以轻松存储上千个城市
2. **功能扩展性** - 未来添加用户收藏、自定义城市等功能
3. **性能要求** - 更好的查询性能和内存使用

## 📊 数据库规模对比

| 方案 | 当前城市数量 | 推荐最大城市数 | 查询性能 | 扩展性 |
|------|-------------|---------------|----------|---------|
| JSON | 26个城市 | < 100个 | 😐 一般 | 🚫 困难 |
| SQLite | 18个城市 | > 5000个 | 🚀 优秀 | ✅ 容易 |

## 🔧 如何使用SQLite方案

### 1. 切换到SQLite服务
```typescript
// 在需要使用的组件中
import { sqliteService } from '@/lib/sqlite-service'

// 替换原来的 CitiesService
const cities = await sqliteService.getAllCities()
const city = await sqliteService.getCityById('tokyo')
```

### 2. 支持的操作
```typescript
// 查询所有城市
const allCities = await sqliteService.getAllCities()

// 按ID查找城市
const tokyo = await sqliteService.getCityById('tokyo')

// 按名称查找城市
const city = await sqliteService.getCityByName('Tokyo')

// 搜索城市
const results = await sqliteService.searchCities('北京')

// 添加新城市
const newCity = {
  id: 'vancouver',
  city: '温哥华',
  cityEn: 'Vancouver',
  // ... 其他属性
}
await sqliteService.addCity(newCity)

// 更新城市信息
await sqliteService.updateCity('tokyo', { 
  population: 14000000 
})

// 删除城市
await sqliteService.deleteCity('old-city-id')
```

### 3. 未来扩展功能
```typescript
// 用户收藏功能（已预留数据表）
CREATE TABLE user_favorites (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  city_id TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

// 自定义城市功能（已预留数据表）  
CREATE TABLE custom_cities (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  name TEXT NOT NULL,
  timezone TEXT NOT NULL,
  latitude REAL,
  longitude REAL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 性能优化特性

### 1. 索引优化
```sql
-- 已创建的索引
CREATE INDEX idx_cities_city ON cities(city);
CREATE INDEX idx_cities_city_en ON cities(city_en);
CREATE INDEX idx_cities_country ON cities(country);
CREATE INDEX idx_cities_timezone ON cities(timezone);
```

### 2. 查询优化
- 使用预编译语句（PreparedStatement）
- 支持模糊查询和精确查询
- 分页查询支持（LIMIT）

### 3. 内存管理
- 按需加载数据
- 及时释放查询资源
- 支持懒加载

## 🛠️ 迁移步骤

如果你决定切换到SQLite方案：

1. **保持兼容性**
   ```typescript
   // 可以同时保留两种方案
   import { CitiesService } from '@/lib/cities-service'      // JSON方案
   import { sqliteService } from '@/lib/sqlite-service'      // SQLite方案
   ```

2. **渐进式迁移**
   ```typescript
   // 先在一个组件中试用SQLite
   const cities = await sqliteService.getAllCities()
   
   // 确认无问题后再全面迁移
   ```

3. **数据迁移**
   - SQLite方案已包含JSON中的所有数据
   - 额外添加了人口、货币等扩展字段
   - 支持动态添加更多城市

## 📋 推荐的下一步行动

1. **立即可做：** 在一个页面中试用SQLite方案
2. **短期规划：** 逐步迁移所有页面到SQLite
3. **长期规划：** 添加用户收藏、自定义城市等功能
4. **数据扩展：** 添加更多世界城市数据

## 🔍 总结

对于你的时间查询应用，**强烈推荐使用SQLite方案**：

- 📊 **数据量支持：** 从26个城市轻松扩展到数千个城市
- 🚀 **性能优势：** 查询速度快，内存使用效率高
- 🛠️ **功能扩展：** 支持用户收藏、自定义城市等高级功能
- 🔧 **易于维护：** 标准SQL语法，数据管理更简单

虽然初期学习成本稍高，但长期收益巨大！ 