import Database from 'better-sqlite3'
import path from 'path'
import fs from 'fs'

export interface CityDetail {
  id: string
  city: string
  cityEn: string
  country: string
  countryEn: string
  flag: string
  timezone: string
  utcOffset: string
  continent: string
  continentEn: string
  iana: string
  dstStatus: string
  dstStatusEn: string
  latitude: number
  longitude: number
  sunrise: string
  sunset: string
  population?: number
  currency?: string
  language?: string
  areaCode?: string
  elevation?: number
  website?: string
  established?: string
  mayor?: string
  gdp?: number
  area?: number
  density?: number
  nickname?: string
  nicknameEn?: string
  description?: string
  descriptionEn?: string
}

class BackendDatabaseService {
  private db: Database.Database | null = null
  private static instance: BackendDatabaseService | null = null

  private constructor() {}

  static getInstance(): BackendDatabaseService {
    if (!BackendDatabaseService.instance) {
      BackendDatabaseService.instance = new BackendDatabaseService()
    }
    return BackendDatabaseService.instance
  }

  async initialize(): Promise<void> {
    try {
      // 创建数据库文件路径
      const dbPath = path.join(process.cwd(), 'data', 'cities.db')
      
      // 确保数据目录存在
      const dataDir = path.dirname(dbPath)
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true })
      }

      // 初始化数据库
      this.db = new Database(dbPath)
      
      // 创建表结构
      await this.createTables()
      
      // 检查是否需要初始化数据
      const count = this.db.prepare('SELECT COUNT(*) as count FROM cities').get() as { count: number }
      if (count.count === 0) {
        await this.insertInitialData()
      }
      
      console.log('后端数据库初始化完成')
    } catch (error) {
      console.error('后端数据库初始化失败:', error)
      throw error
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS cities (
        id TEXT PRIMARY KEY,
        city TEXT NOT NULL,
        cityEn TEXT NOT NULL,
        country TEXT NOT NULL,
        countryEn TEXT NOT NULL,
        flag TEXT NOT NULL,
        timezone TEXT NOT NULL,
        utcOffset TEXT NOT NULL,
        continent TEXT NOT NULL,
        continentEn TEXT NOT NULL,
        iana TEXT NOT NULL,
        dstStatus TEXT NOT NULL,
        dstStatusEn TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        sunrise TEXT NOT NULL,
        sunset TEXT NOT NULL,
        population INTEGER,
        currency TEXT,
        language TEXT,
        areaCode TEXT,
        elevation INTEGER,
        website TEXT,
        established TEXT,
        mayor TEXT,
        gdp REAL,
        area REAL,
        density REAL,
        nickname TEXT,
        nicknameEn TEXT,
        description TEXT,
        descriptionEn TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_city_name ON cities(city, cityEn);
      CREATE INDEX IF NOT EXISTS idx_country ON cities(country, countryEn);
      CREATE INDEX IF NOT EXISTS idx_continent ON cities(continent, continentEn);
      CREATE INDEX IF NOT EXISTS idx_timezone ON cities(timezone);
      CREATE INDEX IF NOT EXISTS idx_population ON cities(population);
    `

    this.db.exec(createTableSQL)
  }

  private async insertInitialData(): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    const cities = [
      // 中国城市
      {
        id: 'beijing',
        city: '北京',
        cityEn: 'Beijing',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: 'UTC+8',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '当前时区不实行夏时制',
        dstStatusEn: 'Daylight saving time is not observed in this time zone',
        latitude: 39.9042,
        longitude: 116.4074,
        sunrise: '06:30',
        sunset: '18:30',
        population: 21540000,
        currency: 'CNY',
        language: 'zh-CN',
        areaCode: '+86-10',
        elevation: 43,
        website: 'http://www.beijing.gov.cn/',
        established: '1949',
        mayor: '殷勇',
        gdp: 4610000000000,
        area: 16410.54,
        density: 1313,
        nickname: '首都',
        nicknameEn: 'Capital City',
        description: '中华人民共和国首都，全国政治、文化、国际交往、科技创新中心。',
        descriptionEn: 'Capital of the People\'s Republic of China, serving as the nation\'s political, cultural, international exchange, and technological innovation center.'
      },
      {
        id: 'shanghai',
        city: '上海',
        cityEn: 'Shanghai',
        country: '中国',
        countryEn: 'China',
        flag: '🇨🇳',
        timezone: 'Asia/Shanghai',
        utcOffset: 'UTC+8',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Shanghai',
        dstStatus: '当前时区不实行夏时制',
        dstStatusEn: 'Daylight saving time is not observed in this time zone',
        latitude: 31.2304,
        longitude: 121.4737,
        sunrise: '06:15',
        sunset: '18:15',
        population: 24870000,
        currency: 'CNY',
        language: 'zh-CN',
        areaCode: '+86-21'
      },
      // 日本城市
      {
        id: 'tokyo',
        city: '东京',
        cityEn: 'Tokyo',
        country: '日本',
        countryEn: 'Japan',
        flag: '🇯🇵',
        timezone: 'Asia/Tokyo',
        utcOffset: 'UTC+9',
        continent: '亚洲',
        continentEn: 'Asia',
        iana: 'Asia/Tokyo',
        dstStatus: '当前时区不实行夏时制',
        dstStatusEn: 'Daylight saving time is not observed in this time zone',
        latitude: 35.6762,
        longitude: 139.6503,
        sunrise: '04:31',
        sunset: '19:00',
        population: 13960000,
        currency: 'JPY',
        language: 'ja-JP',
        areaCode: '+81',
        elevation: 40,
        website: 'https://www.metro.tokyo.lg.jp/',
        established: '1603',
        mayor: 'Yuriko Koike',
        gdp: 1617000000000,
        area: 2194,
        density: 6363,
        nickname: '东京都',
        nicknameEn: 'Tokyo Metropolis',
        description: '日本首都，全球重要的金融、商业和文化中心。',
        descriptionEn: 'Capital of Japan and a major global financial, commercial and cultural center.'
      },
      // 美国城市
      {
        id: 'new-york',
        city: '纽约',
        cityEn: 'New York',
        country: '美国',
        countryEn: 'United States',
        flag: '🇺🇸',
        timezone: 'America/New_York',
        utcOffset: 'UTC-5',
        continent: '北美洲',
        continentEn: 'North America',
        iana: 'America/New_York',
        dstStatus: '当前时区实行夏时制',
        dstStatusEn: 'Daylight saving time is observed in this time zone',
        latitude: 40.7128,
        longitude: -74.0060,
        sunrise: '07:15',
        sunset: '17:30',
        population: 8380000,
        currency: 'USD',
        language: 'en-US',
        areaCode: '+1'
      },
      // 英国城市
      {
        id: 'london',
        city: '伦敦',
        cityEn: 'London',
        country: '英国',
        countryEn: 'United Kingdom',
        flag: '🇬🇧',
        timezone: 'Europe/London',
        utcOffset: 'UTC+0',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/London',
        dstStatus: '当前时区实行夏时制',
        dstStatusEn: 'Daylight saving time is observed in this time zone',
        latitude: 51.5074,
        longitude: -0.1278,
        sunrise: '08:00',
        sunset: '16:00',
        population: 9000000,
        currency: 'GBP',
        language: 'en-GB',
        areaCode: '+44',
        elevation: 35,
        website: 'https://www.london.gov.uk/',
        established: '47 AD',
        mayor: 'Sadiq Khan',
        gdp: 653000000000,
        area: 1572,
        density: 5598,
        nickname: '雾都',
        nicknameEn: 'The City of Fog',
        description: '英国首都，历史悠久的国际金融中心。',
        descriptionEn: 'Capital of the United Kingdom and a historic international financial center.'
      },
      // 法国城市
      {
        id: 'paris',
        city: '巴黎',
        cityEn: 'Paris',
        country: '法国',
        countryEn: 'France',
        flag: '🇫🇷',
        timezone: 'Europe/Paris',
        utcOffset: 'UTC+1',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Paris',
        dstStatus: '当前时区实行夏时制',
        dstStatusEn: 'Daylight saving time is observed in this time zone',
        latitude: 48.8566,
        longitude: 2.3522,
        sunrise: '08:30',
        sunset: '17:30',
        population: 2165000,
        currency: 'EUR',
        language: 'fr-FR',
        areaCode: '+33',
        elevation: 35,
        website: 'https://www.paris.fr/',
        established: '3rd century BC',
        mayor: 'Anne Hidalgo',
        gdp: 779000000000,
        area: 105.4,
        density: 20755,
        nickname: '光之城',
        nicknameEn: 'City of Light',
        description: '法国首都，世界著名的浪漫之都和文化艺术中心。',
        descriptionEn: 'Capital of France, world-renowned romantic city and center of culture and arts.'
      },
      // 俄罗斯城市
      {
        id: 'moscow',
        city: '莫斯科',
        cityEn: 'Moscow',
        country: '俄罗斯',
        countryEn: 'Russia',
        flag: '🇷🇺',
        timezone: 'Europe/Moscow',
        utcOffset: 'UTC+3',
        continent: '欧洲',
        continentEn: 'Europe',
        iana: 'Europe/Moscow',
        dstStatus: '当前时区不实行夏时制',
        dstStatusEn: 'Daylight saving time is not observed in this time zone',
        latitude: 55.7558,
        longitude: 37.6173,
        sunrise: '08:45',
        sunset: '16:15',
        population: 12500000,
        currency: 'RUB',
        language: 'ru-RU',
        areaCode: '+7',
        elevation: 156,
        website: 'https://www.mos.ru/',
        established: '1147',
        mayor: 'Sergey Sobyanin',
        gdp: 415000000000,
        area: 2511,
        density: 4925,
        nickname: '白石之城',
        nicknameEn: 'White Stone City',
        description: '俄罗斯首都，重要的政治、经济、文化中心。',
        descriptionEn: 'Capital of Russia and an important political, economic, and cultural center.'
      },
      // 澳大利亚城市
      {
        id: 'sydney',
        city: '悉尼',
        cityEn: 'Sydney',
        country: '澳洲',
        countryEn: 'Australia',
        flag: '🇦🇺',
        timezone: 'Australia/Sydney',
        utcOffset: 'UTC+11',
        continent: '大洋洲',
        continentEn: 'Oceania',
        iana: 'Australia/Sydney',
        dstStatus: '当前时区实行夏时制',
        dstStatusEn: 'Daylight saving time is observed in this time zone',
        latitude: -33.8688,
        longitude: 151.2093,
        sunrise: '05:30',
        sunset: '19:30',
        population: 5230000,
        currency: 'AUD',
        language: 'en-AU',
        areaCode: '+61'
      }
    ]

    const insertStmt = this.db.prepare(`
      INSERT INTO cities (
        id, city, cityEn, country, countryEn, flag, timezone, utcOffset,
        continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
        sunrise, sunset, population, currency, language, areaCode, elevation, website,
        established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
      ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )
    `)

    const transaction = this.db.transaction(() => {
      for (const city of cities) {
        insertStmt.run(
          city.id, city.city, city.cityEn, city.country, city.countryEn,
          city.flag, city.timezone, city.utcOffset, city.continent, city.continentEn,
          city.iana, city.dstStatus, city.dstStatusEn, city.latitude, city.longitude,
          city.sunrise, city.sunset, city.population, city.currency, city.language, city.areaCode,
          city.elevation, city.website, city.established, city.mayor, city.gdp, city.area,
          city.density, city.nickname, city.nicknameEn, city.description, city.descriptionEn
        )
      }
    })

    transaction()
    console.log('初始城市数据插入完成')
  }

  async getAllCities(): Promise<CityDetail[]> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('SELECT * FROM cities ORDER BY city')
    const rows = stmt.all()
    
    return rows.map(row => this.rowToCity(row))
  }

  async getCityById(id: string): Promise<CityDetail | null> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('SELECT * FROM cities WHERE id = ?')
    const row = stmt.get(id)
    
    return row ? this.rowToCity(row) : null
  }

  async getCityByName(name: string): Promise<CityDetail | null> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('SELECT * FROM cities WHERE city = ? OR cityEn = ? LIMIT 1')
    const row = stmt.get(name, name)
    
    return row ? this.rowToCity(row) : null
  }

  async searchCities(query: string): Promise<CityDetail[]> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const searchTerm = `%${query}%`
    const stmt = this.db.prepare(`
      SELECT * FROM cities 
      WHERE city LIKE ? OR cityEn LIKE ? OR country LIKE ? OR countryEn LIKE ?
      ORDER BY 
        CASE 
          WHEN city LIKE ? OR cityEn LIKE ? THEN 1
          WHEN country LIKE ? OR countryEn LIKE ? THEN 2
          ELSE 3
        END,
        city
    `)
    
    const rows = stmt.all(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm)
    
    return rows.map(row => this.rowToCity(row))
  }

  async getCitiesByCountry(country: string): Promise<CityDetail[]> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('SELECT * FROM cities WHERE country = ? OR countryEn = ? ORDER BY city')
    const rows = stmt.all(country, country)
    
    return rows.map(row => this.rowToCity(row))
  }

  async getCitiesByContinent(continent: string): Promise<CityDetail[]> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('SELECT * FROM cities WHERE continent = ? OR continentEn = ? ORDER BY city')
    const rows = stmt.all(continent, continent)
    
    return rows.map(row => this.rowToCity(row))
  }

  async getPopularCities(limit: number = 10): Promise<CityDetail[]> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('SELECT * FROM cities WHERE population IS NOT NULL ORDER BY population DESC LIMIT ?')
    const rows = stmt.all(limit)
    
    return rows.map(row => this.rowToCity(row))
  }

  async addCity(city: CityDetail): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare(`
      INSERT INTO cities (
        id, city, cityEn, country, countryEn, flag, timezone, utcOffset,
        continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
        sunrise, sunset, population, currency, language, areaCode, elevation,
        website, established, mayor, gdp, area, density, nickname, nicknameEn,
        description, descriptionEn
      ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )
    `)
    
    stmt.run(
      city.id, city.city, city.cityEn, city.country, city.countryEn,
      city.flag, city.timezone, city.utcOffset, city.continent, city.continentEn,
      city.iana, city.dstStatus, city.dstStatusEn, city.latitude, city.longitude,
      city.sunrise, city.sunset, city.population, city.currency, city.language,
      city.areaCode, city.elevation, city.website, city.established, city.mayor,
      city.gdp, city.area, city.density, city.nickname, city.nicknameEn,
      city.description, city.descriptionEn
    )
  }

  async updateCity(id: string, updates: Partial<CityDetail>): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const fields = Object.keys(updates).filter(key => key !== 'id')
    if (fields.length === 0) return
    
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => (updates as any)[field])
    
    const stmt = this.db.prepare(`UPDATE cities SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)
    stmt.run(...values, id)
  }

  async deleteCity(id: string): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')
    
    const stmt = this.db.prepare('DELETE FROM cities WHERE id = ?')
    stmt.run(id)
  }

  private rowToCity(row: any): CityDetail {
    return {
      id: row.id,
      city: row.city,
      cityEn: row.cityEn,
      country: row.country,
      countryEn: row.countryEn,
      flag: row.flag,
      timezone: row.timezone,
      utcOffset: row.utcOffset,
      continent: row.continent,
      continentEn: row.continentEn,
      iana: row.iana,
      dstStatus: row.dstStatus,
      dstStatusEn: row.dstStatusEn,
      latitude: row.latitude,
      longitude: row.longitude,
      sunrise: row.sunrise,
      sunset: row.sunset,
      population: row.population,
      currency: row.currency,
      language: row.language,
      areaCode: row.areaCode,
      elevation: row.elevation,
      website: row.website,
      established: row.established,
      mayor: row.mayor,
      gdp: row.gdp,
      area: row.area,
      density: row.density,
      nickname: row.nickname,
      nicknameEn: row.nicknameEn,
      description: row.description,
      descriptionEn: row.descriptionEn
    }
  }

  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }
}

export const backendDatabase = BackendDatabaseService.getInstance() 