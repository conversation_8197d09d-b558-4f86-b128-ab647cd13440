// 亚洲城市扩展脚本
const fs = require('fs');

// 亚洲主要城市数据 - 第一批：中国城市
const asiaCitiesPart1 = [
  {
    id: 'guangzhou',
    city: '广州',
    cityEn: 'Guangzhou',
    country: '中国',
    countryEn: 'China',
    flag: '🇨🇳',
    timezone: 'Asia/Shanghai',
    utcOffset: '+08:00',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Shanghai',
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: 23.1291,
    longitude: 113.2644,
    sunrise: '06:45',
    sunset: '18:45',
    population: 15300000,
    currency: 'CNY',
    language: 'Chinese',
    areaCode: '+86',
    elevation: 21,
    nickname: '花城',
    nicknameEn: 'Flower City',
    description: '中国南方重要的中心城市，国际商贸中心。',
    descriptionEn: 'An important central city in southern China and international commercial center.'
  },
  {
    id: 'shenzhen',
    city: '深圳',
    cityEn: 'Shenzhen',
    country: '中国',
    countryEn: 'China',
    flag: '🇨🇳',
    timezone: 'Asia/Shanghai',
    utcOffset: '+08:00',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Shanghai',
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: 22.5431,
    longitude: 114.0579,
    sunrise: '06:50',
    sunset: '18:50',
    population: 12590000,
    currency: 'CNY',
    language: 'Chinese',
    areaCode: '+86',
    elevation: 81,
    nickname: '鹏城',
    nicknameEn: 'Peng City',
    description: '中国改革开放的窗口，全球科技创新中心。',
    descriptionEn: 'China\'s window of reform and opening-up, and a global technology innovation center.'
  },
  {
    id: 'chengdu',
    city: '成都',
    cityEn: 'Chengdu',
    country: '中国',
    countryEn: 'China',
    flag: '🇨🇳',
    timezone: 'Asia/Shanghai',
    utcOffset: '+08:00',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Shanghai',
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: 30.5728,
    longitude: 104.0668,
    sunrise: '07:00',
    sunset: '19:00',
    population: 16330000,
    currency: 'CNY',
    language: 'Chinese',
    areaCode: '+86',
    elevation: 505,
    nickname: '天府之国',
    nicknameEn: 'Land of Abundance',
    description: '中国西南地区的科技、商贸、金融中心。',
    descriptionEn: 'A major center for technology, commerce, and finance in Southwest China.'
  },
  {
    id: 'hangzhou',
    city: '杭州',
    cityEn: 'Hangzhou',
    country: '中国',
    countryEn: 'China',
    flag: '🇨🇳',
    timezone: 'Asia/Shanghai',
    utcOffset: '+08:00',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Shanghai',
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: 30.2741,
    longitude: 120.1551,
    sunrise: '06:20',
    sunset: '18:20',
    population: 11940000,
    currency: 'CNY',
    language: 'Chinese',
    areaCode: '+86',
    elevation: 19,
    nickname: '人间天堂',
    nicknameEn: 'Paradise on Earth',
    description: '历史文化名城，中国电子商务之都。',
    descriptionEn: 'A famous historical and cultural city, known as China\'s e-commerce capital.'
  },
  {
    id: 'xian',
    city: '西安',
    cityEn: 'Xi\'an',
    country: '中国',
    countryEn: 'China',
    flag: '🇨🇳',
    timezone: 'Asia/Shanghai',
    utcOffset: '+08:00',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Shanghai',
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: 34.3416,
    longitude: 108.9398,
    sunrise: '07:15',
    sunset: '18:15',
    population: 12950000,
    currency: 'CNY',
    language: 'Chinese',
    areaCode: '+86',
    elevation: 405,
    nickname: '古都',
    nicknameEn: 'Ancient Capital',
    description: '中国历史文化名城，古丝绸之路起点。',
    descriptionEn: 'A famous historical and cultural city in China, starting point of the ancient Silk Road.'
  }
];

console.log(`准备添加 ${asiaCitiesPart1.length} 个中国城市到数据库...`);
module.exports = { asiaCitiesPart1 }; 