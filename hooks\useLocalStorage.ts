import { useState, useEffect } from 'react'

export function useLocalStorage<T>(key: string, initialValue: T) {
  // 状态来存储我们的值
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue
    }
    
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  // 返回一个包装过的版本的useState setter函数，它会持久化新值到localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // 允许值为一个函数，这样我们就有了和useState相同的API
      const valueToStore = value instanceof Function ? value(storedValue) : value
      
      // 保存状态
      setStoredValue(valueToStore)
      
      // 保存到localStorage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }

  return [storedValue, setValue] as const
}

// 专门用于城市数据的Hook
export function useCityStorage() {
  const [cities, setCities] = useLocalStorage<any[]>('timecha_user_cities_v2', [])
  const [lastUpdated, setLastUpdated] = useLocalStorage<string>('timecha_last_updated', '')

  const saveCities = (newCities: any[]) => {
    setCities(newCities)
    setLastUpdated(new Date().toISOString())
  }

  const addCity = (city: any) => {
    const exists = cities.some(c => c.id === city.id)
    if (!exists) {
      const newCities = [...cities, city]
      saveCities(newCities)
      return newCities
    }
    return cities
  }

  const removeCity = (cityId: string) => {
    const newCities = cities.filter(c => c.id !== cityId)
    saveCities(newCities)
    return newCities
  }

  const clearCities = () => {
    setCities([])
    setLastUpdated('')
  }

  const hasCities = cities.length > 0

  return {
    cities,
    saveCities,
    addCity,
    removeCity,
    clearCities,
    hasCities,
    lastUpdated: lastUpdated ? new Date(lastUpdated) : null
  }
} 