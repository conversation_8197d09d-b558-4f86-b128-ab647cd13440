// TypeScript 导入方式对比演示

// ==========================================
// 1. 普通导入 (值 + 类型)
// ==========================================

// 正确的导入方式
import React, { useState } from "react"
import type { Metadata } from "next"
import type { FC, ReactNode } from "react"

// ==========================================
// 使用场景对比
// ==========================================

// 类型注解 - 只需要类型
const metadata: Metadata = {
  title: "我的网站",
  description: "描述"
}

// 函数组件 - 只需要类型
const MyComponent: FC = () => {
  return React.createElement('div', null, 'Hello')
}

// 实际使用 - 需要值
const [count, setCount] = useState(0)  // useState 是函数，需要导入值
const element = React.createElement('div')  // React 是对象，需要导入值

// ==========================================
// 编译后的 JavaScript 差异
// ==========================================

/*
普通导入编译后：
import React from "react";
import { useState } from "react";

类型导入编译后：
// import type 语句完全消失！
// 因为类型只在编译时需要，运行时不需要
*/ 