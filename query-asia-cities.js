const Database = require('better-sqlite3');
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, 'data', 'cities.db');

// 连接数据库
const db = new Database(dbPath);

try {
  console.log('🌏 亚洲城市数据库查询');
  console.log('='.repeat(50));

  // 查询所有亚洲城市
  const asiaCities = db.prepare(`
    SELECT * FROM cities 
    WHERE continent = '亚洲' 
    ORDER BY country, city
  `).all();

  console.log(`📊 总计: ${asiaCities.length} 个亚洲城市`);
  console.log();

  // 按国家分组显示
  const countryGroups = {};
  asiaCities.forEach(city => {
    if (!countryGroups[city.country]) {
      countryGroups[city.country] = [];
    }
    countryGroups[city.country].push(city);
  });

  Object.entries(countryGroups).forEach(([country, cities]) => {
    console.log(`🏳️ ${country} (${cities.length} 个城市):`);
    cities.forEach((city, index) => {
      console.log(`  ${index + 1}. ${city.city} (${city.cityEn})`);
      console.log(`     🕐 时区: ${city.timezone} (${city.utcOffset})`);
      console.log(`     👥 人口: ${city.population ? city.population.toLocaleString() : 'N/A'}`);
      console.log(`     💰 货币: ${city.currency || 'N/A'}`);
      console.log(`     🏛️ 别名: ${city.nickname || 'N/A'} (${city.nicknameEn || 'N/A'})`);
      console.log(`     📍 坐标: ${city.latitude}, ${city.longitude}`);
      console.log();
    });
  });

  // 统计信息
  console.log('\n📈 统计信息:');
  console.log('-'.repeat(30));
  
  // 各国城市数量
  const countryStats = db.prepare(`
    SELECT country, COUNT(*) as count 
    FROM cities 
    WHERE continent = '亚洲'
    GROUP BY country 
    ORDER BY count DESC
  `).all();

  console.log('\n🌍 各国城市数量:');
  countryStats.forEach(stat => {
    console.log(`  ${stat.country}: ${stat.count} 个城市`);
  });

  // 时区统计
  const timezoneStats = db.prepare(`
    SELECT timezone, COUNT(*) as count 
    FROM cities 
    WHERE continent = '亚洲'
    GROUP BY timezone 
    ORDER BY count DESC
  `).all();

  console.log('\n🕐 时区分布:');
  timezoneStats.forEach(stat => {
    console.log(`  ${stat.timezone}: ${stat.count} 个城市`);
  });

  // 人口最多的城市
  const topCitiesByPopulation = db.prepare(`
    SELECT city, cityEn, country, population 
    FROM cities 
    WHERE continent = '亚洲' AND population IS NOT NULL
    ORDER BY population DESC 
    LIMIT 10
  `).all();

  console.log('\n👥 人口最多的10个城市:');
  topCitiesByPopulation.forEach((city, index) => {
    console.log(`  ${index + 1}. ${city.city} (${city.cityEn}) - ${city.country}: ${city.population.toLocaleString()}`);
  });

  // 各大洲城市数量对比
  const continentStats = db.prepare(`
    SELECT continent, COUNT(*) as count 
    FROM cities 
    GROUP BY continent 
    ORDER BY count DESC
  `).all();

  console.log('\n🌍 各大洲城市数量:');
  continentStats.forEach(stat => {
    console.log(`  ${stat.continent}: ${stat.count} 个城市`);
  });

} catch (error) {
  console.error('❌ 数据库查询失败:', error);
} finally {
  // 关闭数据库连接
  db.close();
  console.log('\n✅ 数据库连接已关闭');
} 