"use client"

import React, { useState, useEffect } from "react"
import { Search, Clock, MapPin, Globe, Plus, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { useI18n } from "@/hooks/useI18n"
import { LanguageSwitchI18n } from "@/components/LanguageSwitchI18n"
import { CitiesService, CityDetail } from "@/lib/cities-service"
import { LocalStorageService } from "@/lib/local-storage-service"
import { WorldTimezoneMap } from "@/components/WorldTimezoneMap"
import { CityDataLoading } from "@/components/LoadingStates"
import type { Locale } from "./layout"

// 注意：由于使用了"use client"，静态参数生成在layout.tsx中处理

// 元数据在layout.tsx中处理

export default function HomePage({ params }: { params: Promise<{ locale: Locale }> }) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<CityDetail[]>([])
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [displayedCities, setDisplayedCities] = useState<CityDetail[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { language, t, changeLanguage } = useI18n()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { locale } = React.use(params)

  // 确保i18n语言与URL locale同步
  React.useEffect(() => {
    if (language !== locale) {
      changeLanguage(locale)
    }
  }, [locale, language, changeLanguage])

  useEffect(() => {
    // 初始化默认显示的城市
    const initializeCities = async () => {
      try {
        setIsLoading(true)
        
        // 检查是否有搜索参数
        const searchQuery = searchParams.get('search')
        const filterType = searchParams.get('filter')
        const filterValue = searchParams.get('value')

        if (searchQuery) {
          // 处理搜索查询
          setSearchQuery(searchQuery)
          const searchResults = await CitiesService.searchCities(searchQuery)
          setDisplayedCities(searchResults.slice(0, 12)) // 限制显示数量
        } else if (filterType && filterValue) {
          // 根据筛选类型加载城市
          let filteredCities: CityDetail[] = []
          
          if (filterType === 'continent') {
            const allCities = await CitiesService.getAllCities()
            filteredCities = allCities.filter(city => 
              city.continent === filterValue || city.continentEn === filterValue
            )
          } else if (filterType === 'country') {
            const allCities = await CitiesService.getAllCities()
            filteredCities = allCities.filter(city => 
              city.country === filterValue || city.countryEn === filterValue
            )
          }
          
          setDisplayedCities(filteredCities.slice(0, 12)) // 限制显示数量
        } else {
          // 优先加载用户保存的城市列表
          const userCities = LocalStorageService.getUserCities()
          
          if (userCities && userCities.length > 0) {
            console.log('Loading user saved cities:', userCities.length, 'cities')
            setDisplayedCities(userCities)
          } else {
            // 如果没有用户保存的城市，加载默认城市
            console.log('加载默认城市')
            const defaultCities = await CitiesService.getDefaultCities()
            setDisplayedCities(defaultCities)
            
            // 保存默认城市到localStorage，方便后续使用
            if (defaultCities.length > 0) {
              LocalStorageService.saveDefaultCities(defaultCities)
              LocalStorageService.saveUserCities(defaultCities)
            }
          }
        }
      } catch (error) {
        console.error("加载城市失败:", error)
        
        // 如果加载失败，尝试从localStorage获取备用数据
        const fallbackCities = LocalStorageService.getDefaultCities()
        if (fallbackCities && fallbackCities.length > 0) {
          console.log('使用localStorage备用数据')
          setDisplayedCities(fallbackCities)
        } else {
          setDisplayedCities([])
        }
      } finally {
        setIsLoading(false)
      }
    }
    
    initializeCities()
  }, [searchParams])

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const getTimeForTimezone = (timezone: string) => {
    try {
      const now = new Date()
      const formatter = new Intl.DateTimeFormat(locale === "zh" ? "zh-CN" : "en-US", {
        timeZone: timezone,
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        weekday: "long",
      })

      const parts = formatter.formatToParts(now)
      const timeData = parts.reduce((acc, part) => {
        acc[part.type] = part.value
        return acc
      }, {} as any)

      const weekdayMap: { [key: string]: string } = {
        Monday: locale === "zh" ? "星期一" : "Monday",
        Tuesday: locale === "zh" ? "星期二" : "Tuesday",
        Wednesday: locale === "zh" ? "星期三" : "Wednesday",
        Thursday: locale === "zh" ? "星期四" : "Thursday",
        Friday: locale === "zh" ? "星期五" : "Friday",
        Saturday: locale === "zh" ? "星期六" : "Saturday",
        Sunday: locale === "zh" ? "星期日" : "Sunday",
      }

      return {
        hours: timeData.hour,
        minutes: timeData.minute,
        seconds: timeData.second,
        date:
          locale === "zh"
            ? `${timeData.year}/${timeData.month}/${timeData.day}`
            : `${timeData.month}/${timeData.day}/${timeData.year}`,
        weekday: weekdayMap[timeData.weekday] || timeData.weekday,
      }
    } catch (error) {
      console.error("时区计算错误:", error)
      return {
        hours: "00",
        minutes: "00",
        seconds: "00",
        date: locale === "zh" ? "2025/07/06" : "07/06/2025",
        weekday: locale === "zh" ? "星期日" : "Sunday",
      }
    }
  }

  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.trim() === "") { 
      setSearchResults([])
      setShowSearchResults(false)
      return
    }

    try {
      const filtered = await CitiesService.searchCities(query)
      setSearchResults(filtered.slice(0, 8))
      setShowSearchResults(true)
    } catch (error) {
      console.error("搜索城市失败:", error)
      setSearchResults([])
      setShowSearchResults(false)
    }
  }

  const addCity = (city: CityDetail) => {
    const isAlreadyAdded = displayedCities.some((c) => c.id === city.id)
    if (!isAlreadyAdded) {
      const updatedCities = [...displayedCities, city]
      setDisplayedCities(updatedCities)
      
      // 保存到localStorage
      LocalStorageService.saveUserCities(updatedCities)
      console.log('城市已添加并保存到localStorage:', city.cityEn || city.city)
    }
    setSearchQuery("")
    setShowSearchResults(false)
  }

  const removeCity = (index: number) => {
    if (displayedCities.length > 1) {
      const cityToRemove = displayedCities[index]
      const updatedCities = displayedCities.filter((_, i) => i !== index)
      setDisplayedCities(updatedCities)
      
      // 保存到localStorage
      LocalStorageService.saveUserCities(updatedCities)
      console.log('城市已移除并更新localStorage:', cityToRemove.cityEn || cityToRemove.city)
    }
  }

  const handleCityClick = (city: CityDetail) => {
    router.push(`/${locale}/city/${city.id}`)
  }

  const resetToDefaultCities = async () => {
    try {
      setIsLoading(true)
      const defaultCities = await CitiesService.getDefaultCities()
      setDisplayedCities(defaultCities)
      
      // 清除用户自定义的城市列表，重新保存默认城市
      LocalStorageService.saveUserCities(defaultCities)
      console.log('已重置为默认城市列表')
    } catch (error) {
      console.error('重置默认城市失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return <CityDataLoading locale={locale} />
  }



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-800">
                  {t('siteTitle')}
                </h1>
              </div>
            </div>

            <div className="flex-1 max-w-md mx-8 relative">
              <div className="relative">
                <Input
                  placeholder={t('searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-4 pr-10 rounded-full border-gray-300"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>

              {showSearchResults && searchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                  {searchResults.map((city, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                    >
                      <span className="text-sm">{city.flag}</span>
                      <div
                        className="flex-1 cursor-pointer"
                        onClick={() => handleCityClick(city)}
                      >
                        <div className="text-sm font-medium text-gray-800">
                          {locale === "zh" ? city.city : city.cityEn}
                        </div>
                        <div className="text-xs text-gray-500">{locale === "zh" ? city.country : city.countryEn}</div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          addCity(city)
                        }}
                        className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                      >
                        <Plus className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex items-center gap-4">
              <Link href={`/${locale}/compare`} className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                <Globe className="w-4 h-4" />
                {t('compareTitle')}
              </Link>
              <LanguageSwitchI18n />
              <Link href={`/${locale}/faq`} className="text-gray-600 hover:text-gray-800 text-sm">
                {t('faq')}
              </Link>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden">
            {/* Top Row: Logo and Action Buttons */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <h1 className="text-lg font-semibold text-gray-800">
                  WTA
                </h1>
              </div>

              <div className="flex items-center gap-2">
                <Link
                  href={`/${locale}/compare`}
                  className="flex items-center justify-center bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors"
                  title={t('compareTitle')}
                >
                  <Globe className="w-4 h-4" />
                </Link>
                <LanguageSwitchI18n />
                <Link
                  href={`/${locale}/faq`}
                  className="text-gray-600 hover:text-gray-800 text-sm px-2 py-1"
                >
                  FAQ
                </Link>
              </div>
            </div>

            {/* Bottom Row: Search Bar */}
            <div className="relative">
              <div className="relative">
                <Input
                  placeholder={t('searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-4 pr-10 rounded-full border-gray-300 w-full"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>

              {showSearchResults && searchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                  {searchResults.map((city, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                    >
                      <span className="text-sm">{city.flag}</span>
                      <div
                        className="flex-1 cursor-pointer"
                        onClick={() => handleCityClick(city)}
                      >
                        <div className="text-sm font-medium text-gray-800">
                          {locale === "zh" ? city.city : city.cityEn}
                        </div>
                        <div className="text-xs text-gray-500">{locale === "zh" ? city.country : city.countryEn}</div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          addCity(city)
                        }}
                        className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                      >
                        <Plus className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Title Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            {t('mainTitle')}
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        {/* Cities Grid */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-800">
              {t('myCities')} ({displayedCities.length} {t('cities')})
            </h3>
            <button
              onClick={resetToDefaultCities}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              {t('resetToDefault')}
            </button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
            {displayedCities.map((city, index) => {
              const time = getTimeForTimezone(city.timezone)
              return (
                <div
                  key={city.id}
                  className="bg-white rounded-lg shadow-md p-3 sm:p-4 md:p-6 hover:shadow-lg transition-shadow cursor-pointer relative group"
                  onClick={() => handleCityClick(city)}
                >
                  {displayedCities.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removeCity(index)
                      }}
                      className="absolute top-2 right-2 p-1 rounded-full bg-gray-100 hover:bg-red-100 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-4 h-4 text-gray-400 hover:text-red-500" />
                    </button>
                  )}
                  
                  <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                    <span className="text-lg sm:text-xl md:text-2xl">{city.flag}</span>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold text-gray-800 text-sm sm:text-base truncate">
                        {locale === "zh" ? city.city : city.cityEn}
                      </h4>
                      <p className="text-xs sm:text-sm text-gray-600 truncate">
                        {locale === "zh" ? city.country : city.countryEn}
                      </p>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg sm:text-xl md:text-2xl font-mono font-bold text-gray-800 mb-1">
                      {time.hours}:{time.minutes}:{time.seconds}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600">
                      {time.date}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-500">
                      {time.weekday}
                    </div>
                  </div>

                  <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-100">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span className="truncate">{city.timezone}</span>
                      <span>{city.utcOffset}</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {t('feature1Title')}
            </h3>
            <p className="text-gray-600">
              {t('feature1Desc')}
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Globe className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {t('feature2Title')}
            </h3>
            <p className="text-gray-600">
              {t('feature2Desc')}
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {t('feature3Title')}
            </h3>
            <p className="text-gray-600">
              {t('feature3Desc')}
            </p>
          </div>
        </div>

        {/* World Map Section */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-6 text-center">
            {t('worldTimezoneMap')}
          </h3>
          <WorldTimezoneMap />
        </div>
      </main>
    </div>
  )
} 