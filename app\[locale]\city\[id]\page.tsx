import React from "react"
import Link from "next/link"
import { CitiesService, CityDetail } from "@/lib/cities-service"
import type { Locale } from "../../layout"
import { generateLocalizedMetadata, generateCityMetadata } from "@/lib/metadata"
import { CityPageClient } from "@/components/CityPageClient"

// 生成静态参数
export async function generateStaticParams() {
  try {
    const cities = await CitiesService.getAllCities()
    const locales = ['en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'ru']
    
    const params = []
    for (const locale of locales) {
      for (const city of cities.slice(0, 50)) { // 限制前50个城市以避免构建时间过长
        params.push({
          locale,
          id: city.id
        })
      }
    }
    
    return params
  } catch (error) {
    console.error('生成静态参数失败:', error)
    // 返回默认参数
    const locales = ['en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'ru']
    const defaultCities = ['beijing', 'tokyo', 'new-york', 'london', 'paris']
    
    const params = []
    for (const locale of locales) {
      for (const cityId of defaultCities) {
        params.push({
          locale,
          id: cityId
        })
      }
    }
    
    return params
  }
}

// 生成元数据
export async function generateMetadata({ params }: { params: Promise<{ locale: Locale; id: string }> }) {
  try {
    const { locale, id } = await params

    // 尝试从数据库获取城市信息
    const city = await CitiesService.getCityById(id)

    if (city) {
      // 先尝试使用硬编码映射表获取多语言名称
      const cityNameMap: Record<string, Record<Locale, string>> = {
        'beijing': { en: 'Beijing', zh: '北京', ja: '北京', ko: '베이징', fr: 'Pékin', de: 'Peking', es: 'Pekín', ru: 'Пекин' },
        'tokyo': { en: 'Tokyo', zh: '东京', ja: '東京', ko: '도쿄', fr: 'Tokyo', de: 'Tokio', es: 'Tokio', ru: 'Токио' },
        'london': { en: 'London', zh: '伦敦', ja: 'ロンドン', ko: '런던', fr: 'Londres', de: 'London', es: 'Londres', ru: 'Лондон' },
        'new-york': { en: 'New York', zh: '纽约', ja: 'ニューヨーク', ko: '뉴욕', fr: 'New York', de: 'New York', es: 'Nueva York', ru: 'Нью-Йорк' },
        'paris': { en: 'Paris', zh: '巴黎', ja: 'パリ', ko: '파리', fr: 'Paris', de: 'Paris', es: 'París', ru: 'Париж' },
        'shanghai': { en: 'Shanghai', zh: '上海', ja: '上海', ko: '상하이', fr: 'Shanghai', de: 'Shanghai', es: 'Shanghái', ru: 'Шанхай' }
      }

      const countryNameMap: Record<string, Record<Locale, string>> = {
        'beijing': { en: 'China', zh: '中国', ja: '中国', ko: '중국', fr: 'Chine', de: 'China', es: 'China', ru: 'Китай' },
        'tokyo': { en: 'Japan', zh: '日本', ja: '日本', ko: '일본', fr: 'Japon', de: 'Japan', es: 'Japón', ru: 'Япония' },
        'london': { en: 'United Kingdom', zh: '英国', ja: 'イギリス', ko: '영국', fr: 'Royaume-Uni', de: 'Vereinigtes Königreich', es: 'Reino Unido', ru: 'Великобритания' },
        'new-york': { en: 'United States', zh: '美国', ja: 'アメリカ', ko: '미국', fr: 'États-Unis', de: 'Vereinigte Staaten', es: 'Estados Unidos', ru: 'США' },
        'paris': { en: 'France', zh: '法国', ja: 'フランス', ko: '프랑스', fr: 'France', de: 'Frankreich', es: 'Francia', ru: 'Франция' },
        'shanghai': { en: 'China', zh: '中国', ja: '中国', ko: '중국', fr: 'Chine', de: 'China', es: 'China', ru: 'Китай' }
      }

      // 优先使用映射表，如果没有则使用数据库数据
      const cityName = cityNameMap[id]?.[locale] || (locale === 'zh' ? city.city : city.cityEn)
      const countryName = countryNameMap[id]?.[locale] || (locale === 'zh' ? city.country : city.countryEn)

      return generateCityMetadata(locale, cityName, countryName, id)
    }

    // 如果数据库中没有找到城市，使用备用映射表
    const cityNameMap: Record<string, Record<Locale, string>> = {
      'beijing': { en: 'Beijing', zh: '北京', ja: '北京', ko: '베이징', fr: 'Pékin', de: 'Peking', es: 'Pekín', ru: 'Пекин' },
      'tokyo': { en: 'Tokyo', zh: '东京', ja: '東京', ko: '도쿄', fr: 'Tokyo', de: 'Tokio', es: 'Tokio', ru: 'Токио' },
      'london': { en: 'London', zh: '伦敦', ja: 'ロンドン', ko: '런던', fr: 'Londres', de: 'London', es: 'Londres', ru: 'Лондон' },
      'new-york': { en: 'New York', zh: '纽约', ja: 'ニューヨーク', ko: '뉴욕', fr: 'New York', de: 'New York', es: 'Nueva York', ru: 'Нью-Йорк' },
      'paris': { en: 'Paris', zh: '巴黎', ja: 'パリ', ko: '파리', fr: 'Paris', de: 'Paris', es: 'París', ru: 'Париж' }
    }

    const countryNameMap: Record<string, Record<Locale, string>> = {
      'beijing': { en: 'China', zh: '中国', ja: '中国', ko: '중국', fr: 'Chine', de: 'China', es: 'China', ru: 'Китай' },
      'tokyo': { en: 'Japan', zh: '日本', ja: '日本', ko: '일본', fr: 'Japon', de: 'Japan', es: 'Japón', ru: 'Япония' },
      'london': { en: 'United Kingdom', zh: '英国', ja: 'イギリス', ko: '영국', fr: 'Royaume-Uni', de: 'Vereinigtes Königreich', es: 'Reino Unido', ru: 'Великобритания' },
      'new-york': { en: 'United States', zh: '美国', ja: 'アメリカ', ko: '미국', fr: 'États-Unis', de: 'Vereinigte Staaten', es: 'Estados Unidos', ru: 'США' },
      'paris': { en: 'France', zh: '法国', ja: 'フランス', ko: '프랑스', fr: 'France', de: 'Frankreich', es: 'Francia', ru: 'Франция' }
    }

    const cityName = cityNameMap[id]?.[locale] || cityNameMap[id]?.en || id
    const countryName = countryNameMap[id]?.[locale] || countryNameMap[id]?.en || ''

    return generateCityMetadata(locale, cityName, countryName, id)
  } catch (error) {
    console.error('生成城市元数据失败:', error)
    const { locale } = await params
    return generateLocalizedMetadata('home', locale)
  }
}

export default async function CityDetailPage({ params }: { params: Promise<{ locale: Locale; id: string }> }) {
  try {
    const { locale, id } = await params
    
    // 获取城市数据
    const city = await CitiesService.getCityById(id)
    
    if (!city) {
      // 如果城市不存在，尝试获取默认城市
      const defaultCity = await CitiesService.getCityById('tokyo')
      if (defaultCity) {
        return <CityPageClient initialCity={defaultCity} locale={locale} />
      }
      
      // 如果连默认城市都不存在，显示错误页面
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600 mb-4">城市数据未找到</p>
            <Link 
              href={`/${locale}`}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              返回首页
            </Link>
          </div>
        </div>
      )
    }

    return <CityPageClient initialCity={city} locale={locale} />
  } catch (error) {
    console.error('城市页面加载失败:', error)
    
    // 错误处理：显示错误页面
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">页面加载失败</p>
          <Link 
            href={`/en`}
            className="text-blue-600 hover:text-blue-800 underline"
          >
            返回首页
          </Link>
        </div>
      </div>
    )
  }
}
