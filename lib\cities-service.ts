import { ApiCitiesService } from './api-cities-service'

export interface CityDetail {
  id: string
  city: string
  cityEn: string
  country: string
  countryEn: string
  flag: string
  timezone: string
  utcOffset: string
  continent: string
  continentEn: string
  iana: string
  dstStatus: string
  dstStatusEn: string
  latitude: number
  longitude: number
  sunrise: string
  sunset: string
  population?: number
  currency?: string
  language?: string
  areaCode?: string
  elevation?: number
  website?: string
  established?: string
  mayor?: string
  gdp?: number
  area?: number
  density?: number
  nickname?: string
  nicknameEn?: string
  description?: string
  descriptionEn?: string
}

// 静态数据作为备用
const STATIC_CITIES: CityDetail[] = [
  {
    id: 'beijing',
    city: '北京',
    cityEn: 'Beijing',
    country: '中国',
    countryEn: 'China',
    flag: '🇨🇳',
    timezone: 'Asia/Shanghai',
    utcOffset: 'UTC+8',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Shanghai',
    dstStatus: '当前时区不实行夏时制',
    dstStatusEn: 'Daylight saving time is not observed in this time zone',
    latitude: 39.9042,
    longitude: 116.4074,
    sunrise: '06:30',
    sunset: '18:30',
    population: 21540000,
    currency: 'CNY',
    language: 'zh-CN',
    areaCode: '+86-10'
  },
  {
    id: 'tokyo',
    city: '东京',
    cityEn: 'Tokyo',
    country: '日本',
    countryEn: 'Japan',
    flag: '🇯🇵',
    timezone: 'Asia/Tokyo',
    utcOffset: 'UTC+9',
    continent: '亚洲',
    continentEn: 'Asia',
    iana: 'Asia/Tokyo',
    dstStatus: '当前时区不实行夏时制',
    dstStatusEn: 'Daylight saving time is not observed in this time zone',
    latitude: 35.6762,
    longitude: 139.6503,
    sunrise: '04:31',
    sunset: '19:00',
    population: 13960000,
    currency: 'JPY',
    language: 'ja-JP',
    areaCode: '+81'
  },
  {
    id: 'new-york',
    city: '纽约',
    cityEn: 'New York',
    country: '美国',
    countryEn: 'United States',
    flag: '🇺🇸',
    timezone: 'America/New_York',
    utcOffset: 'UTC-5',
    continent: '北美洲',
    continentEn: 'North America',
    iana: 'America/New_York',
    dstStatus: '当前时区实行夏时制',
    dstStatusEn: 'Daylight saving time is observed in this time zone',
    latitude: 40.7128,
    longitude: -74.0060,
    sunrise: '07:15',
    sunset: '17:30',
    population: 8380000,
    currency: 'USD',
    language: 'en-US',
    areaCode: '+1'
  },
  {
    id: 'london',
    city: '伦敦',
    cityEn: 'London',
    country: '英国',
    countryEn: 'United Kingdom',
    flag: '🇬🇧',
    timezone: 'Europe/London',
    utcOffset: 'UTC+0',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/London',
    dstStatus: '当前时区实行夏时制',
    dstStatusEn: 'Daylight saving time is observed in this time zone',
    latitude: 51.5074,
    longitude: -0.1278,
    sunrise: '08:00',
    sunset: '16:00',
    population: 9000000,
    currency: 'GBP',
    language: 'en-GB',
    areaCode: '+44'
  },
  {
    id: 'paris',
    city: '巴黎',
    cityEn: 'Paris',
    country: '法国',
    countryEn: 'France',
    flag: '🇫🇷',
    timezone: 'Europe/Paris',
    utcOffset: 'UTC+1',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Paris',
    dstStatus: '当前时区实行夏时制',
    dstStatusEn: 'Daylight saving time is observed in this time zone',
    latitude: 48.8566,
    longitude: 2.3522,
    sunrise: '08:30',
    sunset: '17:30',
    population: 2165000,
    currency: 'EUR',
    language: 'fr-FR',
    areaCode: '+33'
  },
  {
    id: 'moscow',
    city: '莫斯科',
    cityEn: 'Moscow',
    country: '俄罗斯',
    countryEn: 'Russia',
    flag: '🇷🇺',
    timezone: 'Europe/Moscow',
    utcOffset: 'UTC+3',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Moscow',
    dstStatus: '当前时区不实行夏时制',
    dstStatusEn: 'Daylight saving time is not observed in this time zone',
    latitude: 55.7558,
    longitude: 37.6173,
    sunrise: '08:45',
    sunset: '16:15',
    population: 12500000,
    currency: 'RUB',
    language: 'ru-RU',
    areaCode: '+7'
  }
]

export class CitiesService {
  static async getAllCities(): Promise<CityDetail[]> {
    try {
      return await ApiCitiesService.getAllCities()
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      return STATIC_CITIES
    }
  }

  static async getCityById(id: string): Promise<CityDetail | null> {
    try {
      return await ApiCitiesService.getCityById(id)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      return STATIC_CITIES.find(city => city.id === id) || null
    }
  }

  static async getCityByName(name: string): Promise<CityDetail | null> {
    try {
      return await ApiCitiesService.getCityByName(name)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      const normalizedName = name.toLowerCase().replace(/\s+/g, '-')
      const exactMatch = STATIC_CITIES.find(city => 
        city.id === normalizedName ||
        city.city.toLowerCase() === name.toLowerCase() ||
        city.cityEn.toLowerCase() === name.toLowerCase()
      )
      return exactMatch || null
    }
  }

  static async searchCities(query: string): Promise<CityDetail[]> {
    try {
      return await ApiCitiesService.searchCities(query)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      const lowerQuery = query.toLowerCase()
      return STATIC_CITIES.filter(city =>
        city.city.toLowerCase().includes(lowerQuery) ||
        city.cityEn.toLowerCase().includes(lowerQuery) ||
        city.country.toLowerCase().includes(lowerQuery) ||
        city.countryEn.toLowerCase().includes(lowerQuery)
      )
    }
  }

  static async getDefaultCities(): Promise<CityDetail[]> {
    try {
      return await ApiCitiesService.getDefaultCities()
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      return STATIC_CITIES.slice(0, 6)
    }
  }

  static async getCitiesByCountry(country: string): Promise<CityDetail[]> {
    try {
      return await ApiCitiesService.getCitiesByCountry(country)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      return STATIC_CITIES.filter(city => 
        city.country === country || city.countryEn === country
      )
    }
  }

  static async getCitiesByContinent(continent: string): Promise<CityDetail[]> {
    try {
      return await ApiCitiesService.getCitiesByContinent(continent)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      return STATIC_CITIES.filter(city => 
        city.continent === continent || city.continentEn === continent
      )
    }
  }

  static async getPopularCities(limit: number = 10): Promise<CityDetail[]> {
    try {
      return await ApiCitiesService.getPopularCities(limit)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      return STATIC_CITIES
        .filter(city => city.population)
        .sort((a, b) => (b.population || 0) - (a.population || 0))
        .slice(0, limit)
    }
  }

  static async getRelatedCities(currentCity: CityDetail, limit: number = 6): Promise<{
    sameCountryCities: CityDetail[],
    sameTimezoneCities: CityDetail[],
    popularCities: CityDetail[]
  }> {
    try {
      return await ApiCitiesService.getRelatedCities(currentCity, limit)
    } catch (error) {
      console.warn('API 调用失败，使用静态数据:', error)
      
      // 同国家城市（排除当前城市）
      const sameCountryCities = STATIC_CITIES
        .filter(city => 
          city.id !== currentCity.id && 
          (city.country === currentCity.country || city.countryEn === currentCity.countryEn)
        )
        .slice(0, limit)
      
      // 同时区城市（排除当前城市和同国家城市）
      const sameTimezoneCities = STATIC_CITIES
        .filter(city => 
          city.id !== currentCity.id && 
          city.timezone === currentCity.timezone &&
          !sameCountryCities.some(sameCountryCity => sameCountryCity.id === city.id)
        )
        .slice(0, limit)
      
      // 热门城市（排除当前城市）
      const popularCities = STATIC_CITIES
        .filter(city => 
          city.id !== currentCity.id && 
          city.population &&
          !sameCountryCities.some(sameCountryCity => sameCountryCity.id === city.id) &&
          !sameTimezoneCities.some(sameTimezoneCity => sameTimezoneCity.id === city.id)
        )
        .sort((a, b) => (b.population || 0) - (a.population || 0))
        .slice(0, limit)
      
      return {
        sameCountryCities,
        sameTimezoneCities,
        popularCities
      }
    }
  }

  // 新增方法：添加城市
  static async addCity(city: CityDetail): Promise<void> {
    try {
      await ApiCitiesService.addCity(city)
    } catch (error) {
      console.error('添加城市失败:', error)
      throw error
    }
  }

  // 新增方法：更新城市
  static async updateCity(id: string, updates: Partial<CityDetail>): Promise<CityDetail> {
    try {
      return await ApiCitiesService.updateCity(id, updates)
    } catch (error) {
      console.error('更新城市失败:', error)
      throw error
    }
  }

  // 新增方法：删除城市
  static async deleteCity(id: string): Promise<void> {
    try {
      await ApiCitiesService.deleteCity(id)
    } catch (error) {
      console.error('删除城市失败:', error)
      throw error
    }
  }
} 