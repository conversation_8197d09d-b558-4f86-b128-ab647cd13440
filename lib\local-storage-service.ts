import { CityDetail } from './cities-service'

export interface UserCityPreferences {
  selectedCities: CityDetail[]
  lastUpdated: string
}

export class LocalStorageService {
  private static readonly CITIES_KEY = 'timecha_user_cities'
  private static readonly DEFAULT_CITIES_KEY = 'timecha_default_cities'
  private static readonly DEFAULT_TIMEZONE_KEY = 'timecha_default_timezone'

  /**
   * 获取用户保存的城市列表
   */
  static getUserCities(): CityDetail[] | null {
    if (typeof window === 'undefined') return null
    
    try {
      const stored = localStorage.getItem(this.CITIES_KEY)
      if (!stored) return null
      
      const preferences: UserCityPreferences = JSON.parse(stored)
      return preferences.selectedCities
    } catch (error) {
      console.error('读取用户城市数据失败:', error)
      return null
    }
  }

  /**
   * 保存用户的城市列表
   */
  static saveUserCities(cities: CityDetail[]): void {
    if (typeof window === 'undefined') return
    
    try {
      const preferences: UserCityPreferences = {
        selectedCities: cities,
        lastUpdated: new Date().toISOString()
      }
      
      localStorage.setItem(this.CITIES_KEY, JSON.stringify(preferences))
    } catch (error) {
      console.error('保存用户城市数据失败:', error)
    }
  }

  /**
   * 添加城市到用户列表
   */
  static addUserCity(city: CityDetail): CityDetail[] {
    const currentCities = this.getUserCities() || []
    
    // 检查是否已存在
    const exists = currentCities.some(c => c.id === city.id)
    if (exists) {
      return currentCities
    }
    
    const updatedCities = [...currentCities, city]
    this.saveUserCities(updatedCities)
    return updatedCities
  }

  /**
   * 从用户列表移除城市
   */
  static removeUserCity(cityId: string): CityDetail[] {
    const currentCities = this.getUserCities() || []
    const updatedCities = currentCities.filter(c => c.id !== cityId)
    this.saveUserCities(updatedCities)
    return updatedCities
  }

  /**
   * 更新城市在列表中的位置
   */
  static reorderUserCities(cities: CityDetail[]): void {
    this.saveUserCities(cities)
  }

  /**
   * 清除用户的城市列表
   */
  static clearUserCities(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(this.CITIES_KEY)
    } catch (error) {
      console.error('清除用户城市数据失败:', error)
    }
  }

  /**
   * 保存默认城市列表（用于首次加载时的回退）
   */
  static saveDefaultCities(cities: CityDetail[]): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(this.DEFAULT_CITIES_KEY, JSON.stringify(cities))
    } catch (error) {
      console.error('保存默认城市数据失败:', error)
    }
  }

  /**
   * 获取默认城市列表
   */
  static getDefaultCities(): CityDetail[] | null {
    if (typeof window === 'undefined') return null
    
    try {
      const stored = localStorage.getItem(this.DEFAULT_CITIES_KEY)
      if (!stored) return null
      
      return JSON.parse(stored)
    } catch (error) {
      console.error('读取默认城市数据失败:', error)
      return null
    }
  }

  /**
   * 检查是否有用户自定义的城市列表
   */
  static hasUserCities(): boolean {
    if (typeof window === 'undefined') return false
    
    try {
      const stored = localStorage.getItem(this.CITIES_KEY)
      return stored !== null
    } catch (error) {
      return false
    }
  }

  /**
   * 获取用户偏好的更新时间
   */
  static getLastUpdated(): Date | null {
    if (typeof window === 'undefined') return null
    
    try {
      const stored = localStorage.getItem(this.CITIES_KEY)
      if (!stored) return null
      
      const preferences: UserCityPreferences = JSON.parse(stored)
      return new Date(preferences.lastUpdated)
    } catch (error) {
      return null
    }
  }

  /**
   * 设置默认时区
   */
  static setDefaultTimezone(timezone: string): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(this.DEFAULT_TIMEZONE_KEY, timezone)
    } catch (error) {
      console.error('保存默认时区失败:', error)
    }
  }

  /**
   * 获取默认时区
   */
  static getDefaultTimezone(): string | null {
    if (typeof window === 'undefined') return null
    
    try {
      return localStorage.getItem(this.DEFAULT_TIMEZONE_KEY)
    } catch (error) {
      console.error('读取默认时区失败:', error)
      return null
    }
  }

  /**
   * 清除默认时区
   */
  static clearDefaultTimezone(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(this.DEFAULT_TIMEZONE_KEY)
    } catch (error) {
      console.error('清除默认时区失败:', error)
    }
  }
} 