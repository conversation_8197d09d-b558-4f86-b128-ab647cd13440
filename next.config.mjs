/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: false, // 启用图片优化
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1年缓存
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // 图片域名配置
    domains: ['worldtimeapp.online'],
    // 图片加载器配置
    loader: 'default',
  },
  // 启用压缩
  compress: true,
  // 启用 PWA 支持
  experimental: {
    webpackBuildWorker: false,
  },
  // 生成静态页面
  trailingSlash: true,
  // 优化输出 - 暂时改回standalone，稍后处理静态导出
  output: 'standalone',
  webpack: (config, { isServer }) => {
    // 处理 better-sqlite3 的服务器端兼容性
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      }
    }

    return config
  },
}

export default nextConfig
