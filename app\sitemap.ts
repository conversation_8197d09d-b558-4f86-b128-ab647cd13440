import { MetadataRoute } from 'next'
import { backendDatabase } from '@/lib/backend-database'
import { siteConfig } from '@/lib/metadata'

// 支持的语言列表
const locales = siteConfig.locales
const defaultLocale = siteConfig.defaultLocale

// XML实体转义函数
function escapeXml(str: string): string {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;')
}

// 生成语言alternates
function generateLanguageAlternates(path: string = ''): Record<string, string> {
  const alternates: Record<string, string> = {}

  locales.forEach(locale => {
    const localePath = locale === defaultLocale ? '' : `/${locale}`
    alternates[locale] = `${siteConfig.domain}${localePath}${path}`
  })

  return alternates
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = siteConfig.domain

  try {
    // 初始化并获取 SQLite 数据库中的城市数据
    await backendDatabase.initialize()
    const cities = await backendDatabase.getAllCities()

    const routes: MetadataRoute.Sitemap = []

    // 为每种语言生成基础页面
    locales.forEach(locale => {
      const localePath = locale === defaultLocale ? '' : `/${locale}`

      // 主页
      routes.push({
        url: `${baseUrl}${localePath}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: locale === defaultLocale ? 1.0 : 0.9,
        alternates: {
          languages: generateLanguageAlternates('')
        }
      })

      // FAQ页面
      routes.push({
        url: `${baseUrl}${localePath}/faq`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
        alternates: {
          languages: generateLanguageAlternates('/faq')
        }
      })

      // API演示页面
      routes.push({
        url: `${baseUrl}${localePath}/api-demo`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.5,
        alternates: {
          languages: generateLanguageAlternates('/api-demo')
        }
      })
    })

    // 城市页面 - 为每种语言和每个城市生成
    cities.slice(0, 100).forEach(city => { // 限制城市数量以避免sitemap过大
      locales.forEach(locale => {
        const localePath = locale === defaultLocale ? '' : `/${locale}`

        routes.push({
          url: `${baseUrl}${localePath}/city/${city.id}`,
          lastModified: new Date(),
          changeFrequency: 'daily' as const,
          priority: 0.8,
          alternates: {
            languages: generateLanguageAlternates(`/city/${city.id}`)
          }
        })
      })
    })

    // 按大洲分组的页面 - 为每种语言生成
    const continents = [...new Set(cities.map(city => city.continentEn))]
    continents.forEach(continent => {
      locales.forEach(locale => {
        const localePath = locale === defaultLocale ? '' : `/${locale}`

        routes.push({
          url: `${baseUrl}${localePath}?filter=continent&value=${encodeURIComponent(continent)}`,
          lastModified: new Date(),
          changeFrequency: 'weekly' as const,
          priority: 0.6,
          alternates: {
            languages: generateLanguageAlternates(`?filter=continent&value=${encodeURIComponent(continent)}`)
          }
        })
      })
    })

    // 按国家分组的页面 - 为每种语言生成
    const countries = [...new Set(cities.map(city => city.countryEn))]
    countries.forEach(country => {
      locales.forEach(locale => {
        const localePath = locale === defaultLocale ? '' : `/${locale}`

        routes.push({
          url: `${baseUrl}${localePath}?filter=country&value=${encodeURIComponent(country)}`,
          lastModified: new Date(),
          changeFrequency: 'weekly' as const,
          priority: 0.6,
          alternates: {
            languages: generateLanguageAlternates(`?filter=country&value=${encodeURIComponent(country)}`)
          }
        })
      })
    })

    console.log(`Sitemap 生成完成: 总共 ${routes.length} 个页面`)

    return routes
  } catch (error) {
    console.error('生成sitemap失败:', error)

    // 如果数据库访问失败，返回基础页面和一些常见城市页面
    const fallbackRoutes: MetadataRoute.Sitemap = []
    const defaultCities = ['beijing', 'tokyo', 'new-york', 'london', 'paris']

    // 为每种语言生成fallback路由
    locales.forEach(locale => {
      const localePath = locale === defaultLocale ? '' : `/${locale}`

      // 基础页面
      fallbackRoutes.push(
        {
          url: `${baseUrl}${localePath}`,
          lastModified: new Date(),
          changeFrequency: 'daily' as const,
          priority: locale === defaultLocale ? 1.0 : 0.9,
          alternates: {
            languages: generateLanguageAlternates('')
          }
        },
        {
          url: `${baseUrl}${localePath}/faq`,
          lastModified: new Date(),
          changeFrequency: 'weekly' as const,
          priority: 0.7,
          alternates: {
            languages: generateLanguageAlternates('/faq')
          }
        },
        {
          url: `${baseUrl}${localePath}/api-demo`,
          lastModified: new Date(),
          changeFrequency: 'weekly' as const,
          priority: 0.5,
          alternates: {
            languages: generateLanguageAlternates('/api-demo')
          }
        }
      )

      // 默认城市页面
      defaultCities.forEach(cityId => {
        fallbackRoutes.push({
          url: `${baseUrl}${localePath}/city/${cityId}`,
          lastModified: new Date(),
          changeFrequency: 'daily' as const,
          priority: 0.8,
          alternates: {
            languages: generateLanguageAlternates(`/city/${cityId}`)
          }
        })
      })
    })

    console.log(`Sitemap fallback 生成完成: 总共 ${fallbackRoutes.length} 个页面`)
    return fallbackRoutes
  }
}