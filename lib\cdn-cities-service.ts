// 方案四：CDN + 压缩数据 - 适用于全球分发
import { CityDetail } from './cities-service'

export class CDNCitiesService {
  private static cdnBaseUrl = process.env.NEXT_PUBLIC_CDN_BASE_URL || 'https://cdn.worldtimeapp.com'
  private static cache = new Map<string, any>()

  // 加载压缩的城市数据
  static async loadCompressedCities(): Promise<CityDetail[]> {
    const cacheKey = 'all-cities'
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      // 从CDN加载压缩数据
      const response = await fetch(`${this.cdnBaseUrl}/cities.json.gz`, {
        headers: {
          'Accept-Encoding': 'gzip'
        }
      })

      if (!response.ok) {
        throw new Error('CDN数据加载失败')
      }

      const cities = await response.json()
      
      // 缓存数据
      this.cache.set(cacheKey, cities)
      
      return cities
    } catch (error) {
      console.error('CDN服务错误:', error)
      throw error
    }
  }

  // 按地区加载数据
  static async loadCitiesByRegion(region: string): Promise<CityDetail[]> {
    const cacheKey = `region-${region}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch(`${this.cdnBaseUrl}/regions/${region}.json`)
      
      if (!response.ok) {
        throw new Error(`地区 ${region} 数据加载失败`)
      }

      const cities = await response.json()
      this.cache.set(cacheKey, cities)
      
      return cities
    } catch (error) {
      console.error(`地区 ${region} 服务错误:`, error)
      throw error
    }
  }

  // 搜索建议（预计算的搜索索引）
  static async getSearchSuggestions(query: string): Promise<string[]> {
    try {
      const response = await fetch(`${this.cdnBaseUrl}/search-index.json`)
      const searchIndex = await response.json()
      
      const queryLower = query.toLowerCase()
      return searchIndex.filter((term: string) => 
        term.toLowerCase().includes(queryLower)
      ).slice(0, 10)
    } catch (error) {
      console.error('搜索建议服务错误:', error)
      return []
    }
  }

  // 获取城市详情（单独的文件）
  static async getCityDetail(cityId: string): Promise<CityDetail | null> {
    const cacheKey = `city-${cityId}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch(`${this.cdnBaseUrl}/cities/${cityId}.json`)
      
      if (response.status === 404) {
        return null
      }

      if (!response.ok) {
        throw new Error(`城市 ${cityId} 详情加载失败`)
      }

      const city = await response.json()
      this.cache.set(cacheKey, city)
      
      return city
    } catch (error) {
      console.error(`城市 ${cityId} 详情服务错误:`, error)
      return null
    }
  }
}

// 数据压缩和优化工具
export class DataOptimizer {
  // 压缩城市数据（移除不必要的字段）
  static compressCityData(cities: CityDetail[]): any[] {
    return cities.map(city => ({
      i: city.id,
      c: city.city,
      ce: city.cityEn,
      co: city.country,
      coe: city.countryEn,
      f: city.flag,
      tz: city.timezone,
      uo: city.utcOffset,
      lat: city.latitude,
      lng: city.longitude,
      p: city.population,
      // 其他字段按需压缩
    }))
  }

  // 解压城市数据
  static decompressCityData(compressedCities: any[]): CityDetail[] {
    return compressedCities.map(city => ({
      id: city.i,
      city: city.c,
      cityEn: city.ce,
      country: city.co,
      countryEn: city.coe,
      flag: city.f,
      timezone: city.tz,
      utcOffset: city.uo,
      continent: this.getContinent(city.tz),
      continentEn: this.getContinentEn(city.tz),
      iana: city.tz,
      dstStatus: this.getDstStatus(city.tz),
      dstStatusEn: this.getDstStatusEn(city.tz),
      latitude: city.lat,
      longitude: city.lng,
      sunrise: this.calculateSunrise(city.lat, city.lng),
      sunset: this.calculateSunset(city.lat, city.lng),
      population: city.p,
      currency: this.getCurrency(city.coe),
      language: this.getLanguage(city.coe),
      areaCode: this.getAreaCode(city.coe)
    }))
  }

  // 辅助方法
  private static getContinent(timezone: string): string {
    const continentMap: { [key: string]: string } = {
      'Asia': '亚洲',
      'Europe': '欧洲',
      'America': '美洲',
      'Africa': '非洲',
      'Australia': '大洋洲',
      'Antarctica': '南极洲'
    }
    
    const continent = timezone.split('/')[0]
    return continentMap[continent] || '未知'
  }

  private static getContinentEn(timezone: string): string {
    const continent = timezone.split('/')[0]
    return continent === 'America' ? 'North America' : continent
  }

  private static getDstStatus(timezone: string): string {
    // 简化的夏令时判断
    const dstTimezones = ['America/New_York', 'Europe/London', 'Europe/Paris']
    return dstTimezones.includes(timezone) ? '当前时区实行夏时制' : '当前时区不实行夏时制'
  }

  private static getDstStatusEn(timezone: string): string {
    const dstTimezones = ['America/New_York', 'Europe/London', 'Europe/Paris']
    return dstTimezones.includes(timezone) 
      ? 'Daylight saving time is observed in this time zone'
      : 'Daylight saving time is not observed in this time zone'
  }

  private static calculateSunrise(lat: number, lng: number): string {
    // 简化的日出计算（实际应用中需要更精确的算法）
    return '06:30'
  }

  private static calculateSunset(lat: number, lng: number): string {
    // 简化的日落计算
    return '18:30'
  }

  private static getCurrency(country: string): string {
    const currencyMap: { [key: string]: string } = {
      'China': 'CNY',
      'Japan': 'JPY',
      'United States': 'USD',
      'United Kingdom': 'GBP',
      'France': 'EUR',
      'Russia': 'RUB'
    }
    return currencyMap[country] || 'USD'
  }

  private static getLanguage(country: string): string {
    const languageMap: { [key: string]: string } = {
      'China': 'zh-CN',
      'Japan': 'ja-JP',
      'United States': 'en-US',
      'United Kingdom': 'en-GB',
      'France': 'fr-FR',
      'Russia': 'ru-RU'
    }
    return languageMap[country] || 'en-US'
  }

  private static getAreaCode(country: string): string {
    const areaCodeMap: { [key: string]: string } = {
      'China': '+86',
      'Japan': '+81',
      'United States': '+1',
      'United Kingdom': '+44',
      'France': '+33',
      'Russia': '+7'
    }
    return areaCodeMap[country] || '+1'
  }
}
