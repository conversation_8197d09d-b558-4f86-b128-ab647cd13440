import { NextRequest, NextResponse } from 'next/server'
import { backendDatabase } from '@/lib/backend-database'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    await backendDatabase.initialize()
    
    const { name } = await params
    const decodedName = decodeURIComponent(name)
    
    const city = await backendDatabase.getCityByName(decodedName)
    
    if (!city) {
      return NextResponse.json({
        success: false,
        error: '城市未找到'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      data: city
    })
  } catch (error) {
    console.error('按名称获取城市失败:', error)
    return NextResponse.json({
      success: false,
      error: '按名称获取城市失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 