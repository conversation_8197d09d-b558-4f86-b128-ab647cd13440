import React from 'react'
import type { Locale } from '@/app/[locale]/layout'
import type { CityDetail } from '@/lib/cities-service'

interface StructuredDataProps {
  type: 'WebApplication' | 'Organization' | 'Place' | 'BreadcrumbList'
  locale: Locale
  data?: any
  city?: CityDetail
  breadcrumbs?: Array<{
    name: string
    url: string
  }>
}

// 网站应用结构化数据
function generateWebApplicationSchema(locale: Locale) {
  const appNames: Record<Locale, string> = {
    en: 'WorldTimeApp - Global Timezone Checker',
    zh: 'WorldTimeApp - 全球时区查询工具',
    ja: 'WorldTimeApp - グローバルタイムゾーンチェッカー',
    ko: 'WorldTimeApp - 글로벌 시간대 확인기',
    fr: 'WorldTimeApp - Vérificateur de Fuseau Horaire Global',
    de: 'WorldTimeApp - Globaler Zeitzonenprüfer',
    es: 'WorldTimeApp - Verificador de Zona Horaria Global',
    ru: 'WorldTimeApp - Глобальный Проверщик Часовых Поясов'
  }

  const descriptions: Record<Locale, string> = {
    en: 'Free global timezone checker tool. Check real-time for major cities worldwide, timezone conversion, daylight saving time information.',
    zh: '免费的全球时区查询工具。实时查看世界各大城市时间，时区转换，夏令时信息。',
    ja: '無料のグローバルタイムゾーンチェッカーツール。世界の主要都市のリアルタイム時間確認、タイムゾーン変換、夏時間情報。',
    ko: '무료 글로벌 시간대 확인 도구. 전 세계 주요 도시의 실시간 시간 확인, 시간대 변환, 일광 절약 시간 정보.',
    fr: 'Outil gratuit de vérification des fuseaux horaires mondiaux. Vérifiez l\'heure en temps réel des grandes villes du monde.',
    de: 'Kostenloses globales Zeitzonenprüfungstool. Überprüfen Sie die Echtzeit für große Städte weltweit.',
    es: 'Herramienta gratuita de verificación de zonas horarias globales. Verifique el tiempo real de las principales ciudades del mundo.',
    ru: 'Бесплатный инструмент проверки глобальных часовых поясов. Проверяйте время в реальном времени для крупных городов мира.'
  }

  return {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: appNames[locale],
    description: descriptions[locale],
    url: 'https://worldtimeapp.online',
    applicationCategory: 'UtilitiesApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD'
    },
    author: {
      '@type': 'Organization',
      name: 'WorldTimeApp Team'
    },
    inLanguage: locale,
    isAccessibleForFree: true,
    browserRequirements: 'Requires JavaScript. Requires HTML5.',
    softwareVersion: '1.0',
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0]
  }
}

// 组织结构化数据
function generateOrganizationSchema(locale: Locale) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'WorldTimeApp',
    url: 'https://worldtimeapp.online',
    logo: 'https://worldtimeapp.online/timecha-screenshot.png',
    description: locale === 'zh' 
      ? '提供全球时区查询和时间转换服务的在线工具'
      : 'Online tool providing global timezone checking and time conversion services',
    sameAs: [
      'https://worldtimeapp.online'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'ru']
    }
  }
}

// 城市地点结构化数据
function generatePlaceSchema(city: CityDetail, locale: Locale) {
  const cityName = locale === 'zh' ? city.city : city.cityEn
  const countryName = locale === 'zh' ? city.country : city.countryEn
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Place',
    name: cityName,
    address: {
      '@type': 'PostalAddress',
      addressCountry: countryName,
      addressLocality: cityName
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: city.latitude,
      longitude: city.longitude
    },
    timeZone: city.timezone,
    description: locale === 'zh' 
      ? `${cityName}的当前时间、时区信息和相关数据`
      : `Current time, timezone information and related data for ${cityName}`,
    url: `https://worldtimeapp.online/${locale === 'en' ? '' : locale + '/'}city/${city.id}`,
    additionalProperty: [
      {
        '@type': 'PropertyValue',
        name: 'UTC Offset',
        value: city.utcOffset
      },
      {
        '@type': 'PropertyValue',
        name: 'Timezone',
        value: city.timezone
      }
    ]
  }
}

// 面包屑导航结构化数据
function generateBreadcrumbSchema(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  }
}

export function StructuredData({ type, locale, data, city, breadcrumbs }: StructuredDataProps) {
  let schema: any

  switch (type) {
    case 'WebApplication':
      schema = generateWebApplicationSchema(locale)
      break
    case 'Organization':
      schema = generateOrganizationSchema(locale)
      break
    case 'Place':
      if (!city) {
        console.warn('City data is required for Place schema')
        return null
      }
      schema = generatePlaceSchema(city, locale)
      break
    case 'BreadcrumbList':
      if (!breadcrumbs || breadcrumbs.length === 0) {
        console.warn('Breadcrumbs data is required for BreadcrumbList schema')
        return null
      }
      schema = generateBreadcrumbSchema(breadcrumbs)
      break
    default:
      console.warn(`Unknown schema type: ${type}`)
      return null
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema, null, 2)
      }}
    />
  )
}

// 便捷组件
export function WebApplicationStructuredData({ locale }: { locale: Locale }) {
  return <StructuredData type="WebApplication" locale={locale} />
}

export function OrganizationStructuredData({ locale }: { locale: Locale }) {
  return <StructuredData type="Organization" locale={locale} />
}

export function CityStructuredData({ city, locale }: { city: CityDetail; locale: Locale }) {
  return <StructuredData type="Place" locale={locale} city={city} />
}

export function BreadcrumbStructuredData({ 
  breadcrumbs, 
  locale 
}: { 
  breadcrumbs: Array<{ name: string; url: string }>
  locale: Locale 
}) {
  return <StructuredData type="BreadcrumbList" locale={locale} breadcrumbs={breadcrumbs} />
}

// 组合结构化数据组件 - 用于主页
export function HomePageStructuredData({ locale }: { locale: Locale }) {
  const webAppSchema = generateWebApplicationSchema(locale)
  const orgSchema = generateOrganizationSchema(locale)

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(webAppSchema, null, 2)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(orgSchema, null, 2)
        }}
      />
    </>
  )
}

// 组合结构化数据组件 - 用于城市页面
export function CityPageStructuredData({ 
  city, 
  locale, 
  breadcrumbs 
}: { 
  city: CityDetail
  locale: Locale
  breadcrumbs?: Array<{ name: string; url: string }>
}) {
  return (
    <>
      <OrganizationStructuredData locale={locale} />
      <CityStructuredData city={city} locale={locale} />
      {breadcrumbs && breadcrumbs.length > 0 && (
        <BreadcrumbStructuredData breadcrumbs={breadcrumbs} locale={locale} />
      )}
    </>
  )
}
