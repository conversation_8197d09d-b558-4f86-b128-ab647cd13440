// 欧洲主要城市数据
const europeCities = [
  // 英国城市
  {
    id: 'london',
    city: '伦敦',
    cityEn: 'London',
    country: '英国',
    countryEn: 'United Kingdom',
    flag: '🇬🇧',
    timezone: 'Europe/London',
    utcOffset: '+00:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/London',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 51.5074,
    longitude: -0.1278,
    sunrise: '08:00',
    sunset: '15:45',
    population: 9648000,
    currency: 'GBP',
    language: 'English',
    areaCode: '+44',
    elevation: 35,
    website: 'https://www.london.gov.uk/',
    established: '47 AD',
    mayor: '<PERSON><PERSON>',
    gdp: 653000000000,
    area: 1572,
    density: 5598,
    nickname: '雾都',
    nicknameEn: 'The City of Fog',
    description: '英国首都，历史悠久的国际金融中心。',
    descriptionEn: 'Capital of the United Kingdom and a historic international financial center.'
  },
  {
    id: 'manchester',
    city: '曼彻斯特',
    cityEn: 'Manchester',
    country: '英国',
    countryEn: 'United Kingdom',
    flag: '🇬🇧',
    timezone: 'Europe/London',
    utcOffset: '+00:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/London',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 53.4808,
    longitude: -2.2426,
    sunrise: '08:15',
    sunset: '15:30',
    population: 2720000,
    currency: 'GBP',
    language: 'English',
    areaCode: '+44',
    elevation: 38,
    website: 'https://www.manchester.gov.uk/',
    established: '79 AD',
    mayor: 'Andy Burnham',
    gdp: 88000000000,
    area: 1276,
    density: 2131,
    nickname: '工业革命之城',
    nicknameEn: 'Cradle of Industrial Revolution',
    description: '英国重要的工业城市，足球之城。',
    descriptionEn: 'An important industrial city in the UK, known as the football city.'
  },

  // 法国城市
  {
    id: 'paris',
    city: '巴黎',
    cityEn: 'Paris',
    country: '法国',
    countryEn: 'France',
    flag: '🇫🇷',
    timezone: 'Europe/Paris',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Paris',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 48.8566,
    longitude: 2.3522,
    sunrise: '08:30',
    sunset: '16:15',
    population: 11020000,
    currency: 'EUR',
    language: 'French',
    areaCode: '+33',
    elevation: 35,
    website: 'https://www.paris.fr/',
    established: '3rd century BC',
    mayor: 'Anne Hidalgo',
    gdp: 779000000000,
    area: 105.4,
    density: 20755,
    nickname: '光之城',
    nicknameEn: 'City of Light',
    description: '法国首都，世界著名的浪漫之都和文化艺术中心。',
    descriptionEn: 'Capital of France, world-renowned romantic city and center of culture and arts.'
  },
  {
    id: 'lyon',
    city: '里昂',
    cityEn: 'Lyon',
    country: '法国',
    countryEn: 'France',
    flag: '🇫🇷',
    timezone: 'Europe/Paris',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Paris',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 45.7640,
    longitude: 4.8357,
    sunrise: '08:00',
    sunset: '16:45',
    population: 2280000,
    currency: 'EUR',
    language: 'French',
    areaCode: '+33',
    elevation: 173,
    website: 'https://www.lyon.fr/',
    established: '43 BC',
    mayor: 'Grégory Doucet',
    gdp: 74000000000,
    area: 47.87,
    density: 10476,
    nickname: '美食之都',
    nicknameEn: 'Gastronomic Capital',
    description: '法国第三大城市，著名的美食和丝绸之都。',
    descriptionEn: 'France\'s third-largest city, famous for gastronomy and silk production.'
  },

  // 德国城市
  {
    id: 'berlin',
    city: '柏林',
    cityEn: 'Berlin',
    country: '德国',
    countryEn: 'Germany',
    flag: '🇩🇪',
    timezone: 'Europe/Berlin',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Berlin',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 52.5200,
    longitude: 13.4050,
    sunrise: '08:15',
    sunset: '16:30',
    population: 3677000,
    currency: 'EUR',
    language: 'German',
    areaCode: '+49',
    elevation: 34,
    website: 'https://www.berlin.de/',
    established: '1237',
    mayor: 'Kai Wegner',
    gdp: 147000000000,
    area: 891.8,
    density: 4122,
    nickname: '自由之城',
    nicknameEn: 'City of Freedom',
    description: '德国首都，欧洲重要的政治、文化、科技中心。',
    descriptionEn: 'Capital of Germany and an important political, cultural, and technological center in Europe.'
  },
  {
    id: 'munich',
    city: '慕尼黑',
    cityEn: 'Munich',
    country: '德国',
    countryEn: 'Germany',
    flag: '🇩🇪',
    timezone: 'Europe/Berlin',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Berlin',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 48.1351,
    longitude: 11.5820,
    sunrise: '08:00',
    sunset: '16:45',
    population: 1488000,
    currency: 'EUR',
    language: 'German',
    areaCode: '+49',
    elevation: 519,
    website: 'https://www.muenchen.de/',
    established: '1158',
    mayor: 'Dieter Reiter',
    gdp: 109000000000,
    area: 310.4,
    density: 4794,
    nickname: '啤酒之都',
    nicknameEn: 'Beer Capital',
    description: '德国巴伐利亚州首府，著名的啤酒节举办地。',
    descriptionEn: 'Capital of Bavaria, Germany, famous for hosting Oktoberfest.'
  },
  {
    id: 'hamburg',
    city: '汉堡',
    cityEn: 'Hamburg',
    country: '德国',
    countryEn: 'Germany',
    flag: '🇩🇪',
    timezone: 'Europe/Berlin',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Berlin',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 53.5511,
    longitude: 9.9937,
    sunrise: '08:30',
    sunset: '16:00',
    population: 1900000,
    currency: 'EUR',
    language: 'German',
    areaCode: '+49',
    elevation: 6,
    website: 'https://www.hamburg.de/',
    established: '808',
    mayor: 'Peter Tschentscher',
    gdp: 110000000000,
    area: 755,
    density: 2516,
    nickname: '北方威尼斯',
    nicknameEn: 'Venice of the North',
    description: '德国第二大城市，重要的港口和媒体中心。',
    descriptionEn: 'Germany\'s second-largest city and an important port and media center.'
  },

  // 意大利城市
  {
    id: 'rome',
    city: '罗马',
    cityEn: 'Rome',
    country: '意大利',
    countryEn: 'Italy',
    flag: '🇮🇹',
    timezone: 'Europe/Rome',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Rome',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 41.9028,
    longitude: 12.4964,
    sunrise: '07:30',
    sunset: '17:00',
    population: 4340000,
    currency: 'EUR',
    language: 'Italian',
    areaCode: '+39',
    elevation: 21,
    website: 'https://www.comune.roma.it/',
    established: '753 BC',
    mayor: 'Roberto Gualtieri',
    gdp: 147000000000,
    area: 1285,
    density: 3378,
    nickname: '永恒之城',
    nicknameEn: 'The Eternal City',
    description: '意大利首都，古罗马帝国的发源地，世界历史文化名城。',
    descriptionEn: 'Capital of Italy, birthplace of the Roman Empire, and a world-renowned historical and cultural city.'
  },
  {
    id: 'milan',
    city: '米兰',
    cityEn: 'Milan',
    country: '意大利',
    countryEn: 'Italy',
    flag: '🇮🇹',
    timezone: 'Europe/Rome',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Rome',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 45.4642,
    longitude: 9.1900,
    sunrise: '07:45',
    sunset: '17:15',
    population: 3140000,
    currency: 'EUR',
    language: 'Italian',
    areaCode: '+39',
    elevation: 122,
    website: 'https://www.comune.milano.it/',
    established: '400 BC',
    mayor: 'Giuseppe Sala',
    gdp: 158000000000,
    area: 181.8,
    density: 7315,
    nickname: '时尚之都',
    nicknameEn: 'Fashion Capital',
    description: '意大利经济首都，世界时尚和设计之都。',
    descriptionEn: 'Economic capital of Italy and world capital of fashion and design.'
  },

  // 西班牙城市
  {
    id: 'madrid',
    city: '马德里',
    cityEn: 'Madrid',
    country: '西班牙',
    countryEn: 'Spain',
    flag: '🇪🇸',
    timezone: 'Europe/Madrid',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Madrid',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 40.4168,
    longitude: -3.7038,
    sunrise: '08:30',
    sunset: '17:45',
    population: 6640000,
    currency: 'EUR',
    language: 'Spanish',
    areaCode: '+34',
    elevation: 650,
    website: 'https://www.madrid.es/',
    established: '9th century',
    mayor: 'José Luis Martínez-Almeida',
    gdp: 230000000000,
    area: 604.3,
    density: 5265,
    nickname: '艺术之都',
    nicknameEn: 'City of Art',
    description: '西班牙首都，欧洲重要的艺术和文化中心。',
    descriptionEn: 'Capital of Spain and an important art and cultural center in Europe.'
  },
  {
    id: 'barcelona',
    city: '巴塞罗那',
    cityEn: 'Barcelona',
    country: '西班牙',
    countryEn: 'Spain',
    flag: '🇪🇸',
    timezone: 'Europe/Madrid',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Madrid',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 41.3851,
    longitude: 2.1734,
    sunrise: '08:00',
    sunset: '17:30',
    population: 5575000,
    currency: 'EUR',
    language: 'Spanish, Catalan',
    areaCode: '+34',
    elevation: 12,
    website: 'https://www.barcelona.cat/',
    established: '15 BC',
    mayor: 'Jaume Collboni',
    gdp: 177000000000,
    area: 101.4,
    density: 15991,
    nickname: '地中海明珠',
    nicknameEn: 'Pearl of the Mediterranean',
    description: '西班牙第二大城市，地中海重要的港口和旅游城市。',
    descriptionEn: 'Spain\'s second-largest city and an important Mediterranean port and tourist destination.'
  },

  // 荷兰城市
  {
    id: 'amsterdam',
    city: '阿姆斯特丹',
    cityEn: 'Amsterdam',
    country: '荷兰',
    countryEn: 'Netherlands',
    flag: '🇳🇱',
    timezone: 'Europe/Amsterdam',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Amsterdam',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 52.3676,
    longitude: 4.9041,
    sunrise: '08:30',
    sunset: '16:30',
    population: 2480000,
    currency: 'EUR',
    language: 'Dutch',
    areaCode: '+31',
    elevation: -2,
    website: 'https://www.amsterdam.nl/',
    established: '1275',
    mayor: 'Femke Halsema',
    gdp: 96000000000,
    area: 219.3,
    density: 4908,
    nickname: '运河之城',
    nicknameEn: 'Venice of the North',
    description: '荷兰首都，以其美丽的运河系统和自由文化著称。',
    descriptionEn: 'Capital of the Netherlands, famous for its beautiful canal system and liberal culture.'
  },

  // 瑞士城市
  {
    id: 'zurich',
    city: '苏黎世',
    cityEn: 'Zurich',
    country: '瑞士',
    countryEn: 'Switzerland',
    flag: '🇨🇭',
    timezone: 'Europe/Zurich',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Zurich',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 47.3769,
    longitude: 8.5417,
    sunrise: '08:00',
    sunset: '16:30',
    population: 1380000,
    currency: 'CHF',
    language: 'German',
    areaCode: '+41',
    elevation: 408,
    website: 'https://www.stadt-zuerich.ch/',
    established: '15 BC',
    mayor: 'Corine Mauch',
    gdp: 63000000000,
    area: 87.88,
    density: 4700,
    nickname: '金融之都',
    nicknameEn: 'Financial Capital',
    description: '瑞士最大城市，世界重要的金融中心。',
    descriptionEn: 'Switzerland\'s largest city and a major global financial center.'
  },

  // 俄罗斯城市 (已有莫斯科)
  {
    id: 'st-petersburg',
    city: '圣彼得堡',
    cityEn: 'St. Petersburg',
    country: '俄罗斯',
    countryEn: 'Russia',
    flag: '🇷🇺',
    timezone: 'Europe/Moscow',
    utcOffset: '+03:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Moscow',
    dstStatus: '不实行夏令时',
    dstStatusEn: 'No DST',
    latitude: 59.9311,
    longitude: 30.3609,
    sunrise: '09:30',
    sunset: '15:00',
    population: 5384000,
    currency: 'RUB',
    language: 'Russian',
    areaCode: '+7',
    elevation: 3,
    website: 'https://www.gov.spb.ru/',
    established: '1703',
    mayor: 'Alexander Beglov',
    gdp: 86000000000,
    area: 1439,
    density: 3741,
    nickname: '北方威尼斯',
    nicknameEn: 'Venice of the North',
    description: '俄罗斯第二大城市，前帝国首都，文化艺术中心。',
    descriptionEn: 'Russia\'s second-largest city, former imperial capital, and center of culture and arts.'
  },

  // 北欧城市
  {
    id: 'stockholm',
    city: '斯德哥尔摩',
    cityEn: 'Stockholm',
    country: '瑞典',
    countryEn: 'Sweden',
    flag: '🇸🇪',
    timezone: 'Europe/Stockholm',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Stockholm',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 59.3293,
    longitude: 18.0686,
    sunrise: '09:00',
    sunset: '15:30',
    population: 2390000,
    currency: 'SEK',
    language: 'Swedish',
    areaCode: '+46',
    elevation: 28,
    website: 'https://www.stockholm.se/',
    established: '1252',
    mayor: 'Anna König Jerlmyr',
    gdp: 74000000000,
    area: 188,
    density: 5200,
    nickname: '北方威尼斯',
    nicknameEn: 'Venice of the North',
    description: '瑞典首都，诺贝尔奖颁奖地，北欧设计之都。',
    descriptionEn: 'Capital of Sweden, home of the Nobel Prize, and capital of Nordic design.'
  },
  {
    id: 'copenhagen',
    city: '哥本哈根',
    cityEn: 'Copenhagen',
    country: '丹麦',
    countryEn: 'Denmark',
    flag: '🇩🇰',
    timezone: 'Europe/Copenhagen',
    utcOffset: '+01:00',
    continent: '欧洲',
    continentEn: 'Europe',
    iana: 'Europe/Copenhagen',
    dstStatus: '实行夏令时',
    dstStatusEn: 'DST Observed',
    latitude: 55.6761,
    longitude: 12.5683,
    sunrise: '08:45',
    sunset: '15:45',
    population: 2057000,
    currency: 'DKK',
    language: 'Danish',
    areaCode: '+45',
    elevation: 24,
    website: 'https://www.kk.dk/',
    established: '1167',
    mayor: 'Sophie Hæstorp Andersen',
    gdp: 61000000000,
    area: 86.4,
    density: 6800,
    nickname: '童话之城',
    nicknameEn: 'City of Fairy Tales',
    description: '丹麦首都，安徒生童话的故乡，北欧设计中心。',
    descriptionEn: 'Capital of Denmark, home of Hans Christian Andersen\'s fairy tales, and Nordic design center.'
  }
];

console.log(`准备添加 ${europeCities.length} 个欧洲城市`);
europeCities.forEach((city, index) => {
  console.log(`${index + 1}. ${city.city} (${city.cityEn}) - ${city.country}`);
});

module.exports = europeCities; 