"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Search, MapPin, Clock, Filter, X, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { LanguageSwitchI18n } from '@/components/LanguageSwitchI18n'
import { CityDataLoading, SearchLoading } from '@/components/LoadingStates'
import { CitiesService, CityDetail } from '@/lib/cities-service'
import type { Locale } from '../layout'

interface SearchPageProps {
  params: Promise<{ locale: Locale }>
}

export default function SearchPage({ params }: SearchPageProps) {
  const { locale } = React.use(params)
  const searchParams = useSearchParams()
  const router = useRouter()
  
  const [searchQuery, setSearchQuery] = useState('')
  const [cities, setCities] = useState<CityDetail[]>([])
  const [filteredCities, setFilteredCities] = useState<CityDetail[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSearching, setIsSearching] = useState(false)
  const [selectedTimezone, setSelectedTimezone] = useState<string>('')
  const [selectedContinent, setSelectedContinent] = useState<string>('')

  // 多语言文本
  const texts = {
    en: {
      title: "Search Cities",
      subtitle: "Find world time for any city",
      placeholder: "Search for a city...",
      noResults: "No cities found",
      noResultsDesc: "Try searching with different keywords",
      filters: "Filters",
      timezone: "Timezone",
      continent: "Continent",
      clearFilters: "Clear Filters",
      results: "results",
      popularSearches: "Popular Searches",
      recentSearches: "Recent Searches",
      suggestions: "Suggestions"
    },
    zh: {
      title: "搜索城市",
      subtitle: "查找世界各地城市时间",
      placeholder: "搜索城市...",
      noResults: "未找到城市",
      noResultsDesc: "请尝试使用不同的关键词搜索",
      filters: "筛选",
      timezone: "时区",
      continent: "大洲",
      clearFilters: "清除筛选",
      results: "个结果",
      popularSearches: "热门搜索",
      recentSearches: "最近搜索",
      suggestions: "建议"
    },
    ja: {
      title: "都市検索",
      subtitle: "世界各都市の時間を検索",
      placeholder: "都市を検索...",
      noResults: "都市が見つかりません",
      noResultsDesc: "異なるキーワードで検索してみてください",
      filters: "フィルター",
      timezone: "タイムゾーン",
      continent: "大陸",
      clearFilters: "フィルターをクリア",
      results: "件の結果",
      popularSearches: "人気の検索",
      recentSearches: "最近の検索",
      suggestions: "提案"
    },
    ko: {
      title: "도시 검색",
      subtitle: "전 세계 도시 시간 찾기",
      placeholder: "도시 검색...",
      noResults: "도시를 찾을 수 없습니다",
      noResultsDesc: "다른 키워드로 검색해 보세요",
      filters: "필터",
      timezone: "시간대",
      continent: "대륙",
      clearFilters: "필터 지우기",
      results: "개 결과",
      popularSearches: "인기 검색",
      recentSearches: "최근 검색",
      suggestions: "제안"
    },
    fr: {
      title: "Rechercher des villes",
      subtitle: "Trouvez l'heure mondiale pour n'importe quelle ville",
      placeholder: "Rechercher une ville...",
      noResults: "Aucune ville trouvée",
      noResultsDesc: "Essayez de rechercher avec des mots-clés différents",
      filters: "Filtres",
      timezone: "Fuseau horaire",
      continent: "Continent",
      clearFilters: "Effacer les filtres",
      results: "résultats",
      popularSearches: "Recherches populaires",
      recentSearches: "Recherches récentes",
      suggestions: "Suggestions"
    },
    de: {
      title: "Städte suchen",
      subtitle: "Weltzeit für jede Stadt finden",
      placeholder: "Nach einer Stadt suchen...",
      noResults: "Keine Städte gefunden",
      noResultsDesc: "Versuchen Sie es mit anderen Suchbegriffen",
      filters: "Filter",
      timezone: "Zeitzone",
      continent: "Kontinent",
      clearFilters: "Filter löschen",
      results: "Ergebnisse",
      popularSearches: "Beliebte Suchen",
      recentSearches: "Letzte Suchen",
      suggestions: "Vorschläge"
    },
    es: {
      title: "Buscar ciudades",
      subtitle: "Encuentra la hora mundial de cualquier ciudad",
      placeholder: "Buscar una ciudad...",
      noResults: "No se encontraron ciudades",
      noResultsDesc: "Intenta buscar con palabras clave diferentes",
      filters: "Filtros",
      timezone: "Zona horaria",
      continent: "Continente",
      clearFilters: "Limpiar filtros",
      results: "resultados",
      popularSearches: "Búsquedas populares",
      recentSearches: "Búsquedas recientes",
      suggestions: "Sugerencias"
    },
    ru: {
      title: "Поиск городов",
      subtitle: "Найдите мировое время для любого города",
      placeholder: "Поиск города...",
      noResults: "Города не найдены",
      noResultsDesc: "Попробуйте поиск с другими ключевыми словами",
      filters: "Фильтры",
      timezone: "Часовой пояс",
      continent: "Континент",
      clearFilters: "Очистить фильтры",
      results: "результатов",
      popularSearches: "Популярные поиски",
      recentSearches: "Недавние поиски",
      suggestions: "Предложения"
    }
  }

  const t = texts[locale] || texts.en

  // 热门搜索词
  const popularSearches = {
    en: ['New York', 'London', 'Tokyo', 'Paris', 'Sydney', 'Dubai'],
    zh: ['北京', '上海', '东京', '纽约', '伦敦', '巴黎'],
    ja: ['東京', 'ニューヨーク', 'ロンドン', 'パリ', 'シドニー', 'ドバイ'],
    ko: ['서울', '도쿄', '뉴욕', '런던', '파리', '시드니'],
    fr: ['Paris', 'New York', 'Londres', 'Tokyo', 'Sydney', 'Dubaï'],
    de: ['Berlin', 'New York', 'London', 'Tokyo', 'Sydney', 'Dubai'],
    es: ['Madrid', 'Nueva York', 'Londres', 'Tokio', 'Sídney', 'Dubái'],
    ru: ['Москва', 'Нью-Йорк', 'Лондон', 'Токио', 'Сидней', 'Дубай']
  }

  // 初始化搜索查询
  useEffect(() => {
    const query = searchParams.get('q') || ''
    setSearchQuery(query)
  }, [searchParams])

  // 加载城市数据
  useEffect(() => {
    const loadCities = async () => {
      setIsLoading(true)
      try {
        const allCities = await CitiesService.getAllCities()
        setCities(allCities)
      } catch (error) {
        console.error('Failed to load cities:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadCities()
  }, [])

  // 搜索和筛选逻辑
  const searchResults = useMemo(() => {
    if (!searchQuery && !selectedTimezone && !selectedContinent) {
      return cities.slice(0, 20) // 显示前20个城市
    }

    let filtered = cities

    // 文本搜索
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(city => {
        const cityName = locale === 'zh' ? city.city : city.cityEn
        const countryName = locale === 'zh' ? city.country : city.countryEn
        return (
          cityName?.toLowerCase().includes(query) ||
          countryName?.toLowerCase().includes(query) ||
          city.timezone.toLowerCase().includes(query)
        )
      })
    }

    // 时区筛选
    if (selectedTimezone) {
      filtered = filtered.filter(city => city.timezone === selectedTimezone)
    }

    // 大洲筛选
    if (selectedContinent) {
      filtered = filtered.filter(city => city.continent === selectedContinent)
    }

    return filtered
  }, [cities, searchQuery, selectedTimezone, selectedContinent])

  // 获取唯一的时区和大洲
  const uniqueTimezones = useMemo(() =>
    [...new Set(cities.map(city => city.timezone))].sort()
  , [cities])

  const uniqueContinents = useMemo(() =>
    [...new Set(cities.map(city => city.continent))].sort()
  , [cities])

  // 大洲名称多语言映射
  const continentTranslations = {
    '亚洲': {
      en: 'Asia',
      zh: '亚洲',
      ja: 'アジア',
      ko: '아시아',
      fr: 'Asie',
      de: 'Asien',
      es: 'Asia',
      ru: 'Азия'
    },
    '欧洲': {
      en: 'Europe',
      zh: '欧洲',
      ja: 'ヨーロッパ',
      ko: '유럽',
      fr: 'Europe',
      de: 'Europa',
      es: 'Europa',
      ru: 'Европа'
    },
    '北美洲': {
      en: 'North America',
      zh: '北美洲',
      ja: '北アメリカ',
      ko: '북미',
      fr: 'Amérique du Nord',
      de: 'Nordamerika',
      es: 'América del Norte',
      ru: 'Северная Америка'
    },
    '大洋洲': {
      en: 'Oceania',
      zh: '大洋洲',
      ja: 'オセアニア',
      ko: '오세아니아',
      fr: 'Océanie',
      de: 'Ozeanien',
      es: 'Oceanía',
      ru: 'Океания'
    }
  }

  // 处理搜索
  const handleSearch = (query: string) => {
    setIsSearching(true)
    setSearchQuery(query)
    
    // 更新URL
    const params = new URLSearchParams()
    if (query) params.set('q', query)
    router.push(`/${locale}/search?${params.toString()}`)
    
    setTimeout(() => setIsSearching(false), 300)
  }

  // 清除筛选
  const clearFilters = () => {
    setSelectedTimezone('')
    setSelectedContinent('')
    setSearchQuery('')
    router.push(`/${locale}/search`)
  }

  // 格式化当前时间
  const formatCurrentTime = (timezone: string) => {
    try {
      return new Intl.DateTimeFormat(locale, {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }).format(new Date())
    } catch {
      return '--:--'
    }
  }

  if (isLoading) {
    return <CityDataLoading locale={locale} />
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <a href={`/${locale}/`} className="text-xl font-semibold text-gray-800 hover:text-blue-600 transition-colors">
                  WorldTimeApp
                </a>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <a href={`/${locale}/compare/`} className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                <Globe className="w-4 h-4" />
                {locale === 'zh' ? '城市对比' : 'Compare Cities'}
              </a>
              <LanguageSwitchI18n />
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <a href={`/${locale}/`} className="text-lg font-semibold text-gray-800 hover:text-blue-600 transition-colors">
                WTA
              </a>
            </div>

            <div className="flex items-center gap-2">
              <a
                href={`/${locale}/compare/`}
                className="flex items-center justify-center bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors"
                title={locale === 'zh' ? '城市对比' : 'Compare Cities'}
              >
                <Globe className="w-4 h-4" />
              </a>
              <LanguageSwitchI18n />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Title */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {t.title}
          </h1>
          <p className="text-xl text-gray-600">
            {t.subtitle}
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={t.placeholder}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-4 py-3 text-lg"
            />
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <SearchLoading />
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  {t.filters}
                </h3>
                {(selectedTimezone || selectedContinent) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-blue-600"
                  >
                    <X className="w-4 h-4 mr-1" />
                    {t.clearFilters}
                  </Button>
                )}
              </div>

              {/* Timezone Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t.timezone}
                </label>
                <select
                  value={selectedTimezone}
                  onChange={(e) => setSelectedTimezone(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">All Timezones</option>
                  {uniqueTimezones.map(tz => (
                    <option key={tz} value={tz}>{tz}</option>
                  ))}
                </select>
              </div>

              {/* Continent Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t.continent}
                </label>
                <select
                  value={selectedContinent}
                  onChange={(e) => setSelectedContinent(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">{locale === 'zh' ? '所有大洲' : 'All Continents'}</option>
                  {uniqueContinents.map(continent => (
                    <option key={continent} value={continent}>
                      {continentTranslations[continent as keyof typeof continentTranslations]?.[locale as keyof typeof continentTranslations['亚洲']] || continent}
                    </option>
                  ))}
                </select>
              </div>

              {/* Popular Searches */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  {t.popularSearches}
                </h4>
                <div className="space-y-2">
                  {popularSearches[locale]?.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(search)}
                      className="block w-full text-left text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded"
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Search Results */}
          <div className="lg:col-span-3">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-800">
                {searchResults.length} {t.results}
              </h2>
            </div>

            {/* Results Grid */}
            {searchResults.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                {searchResults.map((city) => (
                  <div
                    key={city.id}
                    className="bg-white rounded-lg shadow-md p-3 sm:p-4 md:p-6 hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => router.push(`/${locale}/city/${city.id}`)}
                  >
                    <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <MapPin className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-800 text-sm sm:text-base truncate">
                          {locale === 'zh' ? city.city : city.cityEn}
                        </h3>
                        <p className="text-xs sm:text-sm text-gray-500 truncate">
                          {locale === 'zh' ? city.country : city.countryEn}
                        </p>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800 mb-1">
                        {formatCurrentTime(city.timezone)}
                      </div>
                      <div className="text-xs sm:text-sm text-gray-500 mb-1">
                        {new Date().toLocaleDateString(locale, {
                          timeZone: city.timezone,
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                      <div className="text-xs text-gray-400 truncate">
                        {city.timezone}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  {t.noResults}
                </h3>
                <p className="text-gray-500">
                  {t.noResultsDesc}
                </p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
