#!/usr/bin/env node

/**
 * 调试 @playwright/mcp 的工具结构
 */

console.log('🔍 调试 @playwright/mcp 工具结构...\n');

async function debugMCPTools() {
  try {
    const playwrightMcp = await import('@playwright/mcp');
    const connection = await playwrightMcp.createConnection();
    
    console.log('✅ MCP 连接创建成功');
    console.log('📋 连接对象键:', Object.keys(connection));
    console.log('📋 服务器对象键:', Object.keys(connection.server));
    console.log('📋 上下文对象键:', Object.keys(connection.context));
    
    console.log('\n🛠️  工具数组分析:');
    console.log('工具数量:', connection.context.tools.length);
    console.log('工具数组类型:', Array.isArray(connection.context.tools));
    
    // 检查前几个工具的完整结构
    console.log('\n🔍 前3个工具的完整结构:');
    for (let i = 0; i < Math.min(3, connection.context.tools.length); i++) {
      const tool = connection.context.tools[i];
      console.log(`\n工具 ${i + 1}:`);
      console.log('  类型:', typeof tool);
      console.log('  键:', Object.keys(tool));
      console.log('  完整对象:', JSON.stringify(tool, null, 2));
    }
    
    // 尝试通过服务器获取工具列表
    console.log('\n📋 尝试通过服务器获取工具列表...');
    try {
      const toolsListResult = await connection.server.request({
        method: 'tools/list',
        params: {}
      });
      
      console.log('✅ 工具列表获取成功:');
      console.log(JSON.stringify(toolsListResult, null, 2));
      
    } catch (error) {
      console.log('❌ 获取工具列表失败:', error.message);
    }
    
    // 检查服务器的请求处理器
    console.log('\n🔧 服务器请求处理器:');
    console.log('处理器数量:', connection.server._requestHandlers.size);
    for (const [method, handler] of connection.server._requestHandlers) {
      console.log(`  - ${method}: ${typeof handler}`);
    }
    
    // 尝试调用 ping
    console.log('\n🏓 测试 ping...');
    try {
      const pingResult = await connection.server.request({
        method: 'ping',
        params: {}
      });
      console.log('✅ Ping 成功:', pingResult);
    } catch (error) {
      console.log('❌ Ping 失败:', error.message);
    }
    
    // 检查上下文配置
    console.log('\n⚙️  上下文配置:');
    console.log('浏览器配置:', connection.context.config.browser);
    console.log('输出目录:', connection.context.config.outputDir);
    
    // 尝试初始化
    console.log('\n🚀 尝试初始化...');
    try {
      const initResult = await connection.server.request({
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: {
            name: 'test-client',
            version: '1.0.0'
          }
        }
      });
      console.log('✅ 初始化成功:', initResult);
      
      // 初始化后再次获取工具列表
      console.log('\n📋 初始化后获取工具列表...');
      const toolsAfterInit = await connection.server.request({
        method: 'tools/list',
        params: {}
      });
      console.log('✅ 初始化后工具列表:', JSON.stringify(toolsAfterInit, null, 2));
      
    } catch (error) {
      console.log('❌ 初始化失败:', error.message);
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error('📋 错误详情:', error);
  }
}

// 运行调试
if (require.main === module) {
  debugMCPTools().catch(console.error);
}

module.exports = { debugMCPTools };
