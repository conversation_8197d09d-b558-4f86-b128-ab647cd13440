export interface CityDetail {
  id: string
  city: string
  cityEn: string
  country: string
  countryEn: string
  flag: string
  timezone: string
  utcOffset: string
  continent: string
  continentEn: string
  iana: string
  dstStatus: string
  dstStatusEn: string
  latitude: number
  longitude: number
  sunrise: string
  sunset: string
  population?: number
  currency?: string
  language?: string
  areaCode?: string
  elevation?: number
  website?: string
  established?: string
  mayor?: string
  gdp?: number
  area?: number
  density?: number
  nickname?: string
  nicknameEn?: string
  description?: string
  descriptionEn?: string
}

interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  details?: string
  count?: number
  message?: string
}

export class ApiCitiesService {
  private static get baseUrl() {
    // 在服务器端渲染时使用完整URL
    if (typeof window === 'undefined') {
      return `http://localhost:${process.env.PORT || 3001}/api/cities`
    }
    // 在客户端使用相对URL
    return '/api/cities'
  }

  static async getAllCities(): Promise<CityDetail[]> {
    try {
      const response = await fetch(this.baseUrl)
      const result: ApiResponse<CityDetail[]> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取城市数据失败')
      }
      
      return result.data
    } catch (error) {
      console.error('获取所有城市失败:', error)
      throw error
    }
  }

  static async getCityById(id: string): Promise<CityDetail | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`)
      
      if (response.status === 404) {
        return null
      }
      
      const result: ApiResponse<CityDetail> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取城市数据失败')
      }
      
      return result.data
    } catch (error) {
      console.error('按ID获取城市失败:', error)
      throw error
    }
  }

  static async getCityByName(name: string): Promise<CityDetail | null> {
    try {
      const encodedName = encodeURIComponent(name)
      const response = await fetch(`${this.baseUrl}/name/${encodedName}`)
      
      if (response.status === 404) {
        return null
      }
      
      const result: ApiResponse<CityDetail> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取城市数据失败')
      }
      
      return result.data
    } catch (error) {
      console.error('按名称获取城市失败:', error)
      throw error
    }
  }

  static async searchCities(query: string): Promise<CityDetail[]> {
    try {
      const searchParams = new URLSearchParams({ search: query })
      const response = await fetch(`${this.baseUrl}?${searchParams}`)
      const result: ApiResponse<CityDetail[]> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '搜索城市失败')
      }
      
      return result.data
    } catch (error) {
      console.error('搜索城市失败:', error)
      throw error
    }
  }

  static async getCitiesByCountry(country: string): Promise<CityDetail[]> {
    try {
      const searchParams = new URLSearchParams({ country })
      const response = await fetch(`${this.baseUrl}?${searchParams}`)
      const result: ApiResponse<CityDetail[]> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取国家城市失败')
      }
      
      return result.data
    } catch (error) {
      console.error('获取国家城市失败:', error)
      throw error
    }
  }

  static async getCitiesByContinent(continent: string): Promise<CityDetail[]> {
    try {
      const searchParams = new URLSearchParams({ continent })
      const response = await fetch(`${this.baseUrl}?${searchParams}`)
      const result: ApiResponse<CityDetail[]> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取大洲城市失败')
      }
      
      return result.data
    } catch (error) {
      console.error('获取大洲城市失败:', error)
      throw error
    }
  }

  static async getPopularCities(limit: number = 10): Promise<CityDetail[]> {
    try {
      const searchParams = new URLSearchParams({ popular: limit.toString() })
      const response = await fetch(`${this.baseUrl}?${searchParams}`)
      const result: ApiResponse<CityDetail[]> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取热门城市失败')
      }
      
      return result.data
    } catch (error) {
      console.error('获取热门城市失败:', error)
      throw error
    }
  }

  static async addCity(city: CityDetail): Promise<void> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(city),
      })
      
      const result: ApiResponse<void> = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '添加城市失败')
      }
    } catch (error) {
      console.error('添加城市失败:', error)
      throw error
    }
  }

  static async updateCity(id: string, updates: Partial<CityDetail>): Promise<CityDetail> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      const result: ApiResponse<CityDetail> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '更新城市失败')
      }
      
      return result.data
    } catch (error) {
      console.error('更新城市失败:', error)
      throw error
    }
  }

  static async deleteCity(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
      })
      
      const result: ApiResponse<void> = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '删除城市失败')
      }
    } catch (error) {
      console.error('删除城市失败:', error)
      throw error
    }
  }

  // 辅助方法，用于兼容现有代码
  static async getDefaultCities(): Promise<CityDetail[]> {
    try {
      const allCities = await this.getAllCities()
      const defaultCityIds = ['beijing', 'tokyo', 'new-york', 'london', 'paris', 'moscow']
      
      const defaultCities = defaultCityIds
        .map(id => allCities.find(city => city.id === id))
        .filter(city => city !== undefined) as CityDetail[]
      
      return defaultCities
    } catch (error) {
      console.error('获取默认城市失败:', error)
      throw error
    }
  }

  // 获取相关城市
  static async getRelatedCities(currentCity: CityDetail, limit: number = 6): Promise<{
    sameCountryCities: CityDetail[],
    sameTimezoneCities: CityDetail[],
    popularCities: CityDetail[]
  }> {
    try {
      const [sameCountryCities, allCities, popularCities] = await Promise.all([
        this.getCitiesByCountry(currentCity.country),
        this.getAllCities(),
        this.getPopularCities(20)
      ])
      
      // 同国家城市（排除当前城市）
      const filteredSameCountryCities = sameCountryCities
        .filter(city => city.id !== currentCity.id)
        .slice(0, limit)
      
      // 同时区城市（排除当前城市和同国家城市）
      const sameTimezoneCities = allCities
        .filter(city => 
          city.id !== currentCity.id && 
          city.timezone === currentCity.timezone &&
          !filteredSameCountryCities.some(sameCountryCity => sameCountryCity.id === city.id)
        )
        .slice(0, limit)
      
      // 热门城市（排除当前城市、同国家城市和同时区城市）
      const filteredPopularCities = popularCities
        .filter(city => 
          city.id !== currentCity.id &&
          !filteredSameCountryCities.some(sameCountryCity => sameCountryCity.id === city.id) &&
          !sameTimezoneCities.some(sameTimezoneCity => sameTimezoneCity.id === city.id)
        )
        .slice(0, limit)
      
      return {
        sameCountryCities: filteredSameCountryCities,
        sameTimezoneCities,
        popularCities: filteredPopularCities
      }
    } catch (error) {
      console.error('获取相关城市失败:', error)
      throw error
    }
  }
} 