#!/usr/bin/env node

/**
 * Google Analytics 快速设置脚本
 * 使用方法：node setup-analytics.js G-YOUR-MEASUREMENT-ID
 */

const fs = require('fs');
const path = require('path');

function setupAnalytics(measurementId) {
  // 验证测量ID格式
  if (!measurementId || !measurementId.match(/^G-[A-Z0-9]+$/)) {
    console.error('❌ 错误：请提供有效的 GA4 测量ID (格式: G-XXXXXXXXXX)');
    console.log('📖 使用方法：node setup-analytics.js G-YOUR-MEASUREMENT-ID');
    process.exit(1);
  }

  const envPath = path.join(process.cwd(), '.env.local');
  
  try {
    // 读取现有的 .env.local 文件
    let envContent = '';
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }

    // 更新或添加 GA 测量ID
    const gaLine = `NEXT_PUBLIC_GA_MEASUREMENT_ID=${measurementId}`;
    
    if (envContent.includes('NEXT_PUBLIC_GA_MEASUREMENT_ID=')) {
      // 替换现有的测量ID
      envContent = envContent.replace(
        /NEXT_PUBLIC_GA_MEASUREMENT_ID=.*/,
        gaLine
      );
    } else {
      // 添加新的测量ID
      if (envContent && !envContent.endsWith('\n')) {
        envContent += '\n';
      }
      envContent += `\n# Google Analytics 配置\n${gaLine}\n`;
    }

    // 写入文件
    fs.writeFileSync(envPath, envContent);

    console.log('✅ Google Analytics 设置成功！');
    console.log(`📊 测量ID: ${measurementId}`);
    console.log(`📁 配置文件: ${envPath}`);
    console.log('');
    console.log('🚀 下一步：');
    console.log('1. 重启开发服务器：npm run dev');
    console.log('2. 访问 http://localhost:3000');
    console.log('3. 在 Google Analytics 实时报告中验证数据');
    console.log('');
    console.log('📖 详细设置指南：查看 GOOGLE_ANALYTICS_SETUP.md');

  } catch (error) {
    console.error('❌ 设置失败：', error.message);
    process.exit(1);
  }
}

// 获取命令行参数
const measurementId = process.argv[2];

if (!measurementId) {
  console.log('🔧 Google Analytics 快速设置工具');
  console.log('');
  console.log('📖 使用方法：');
  console.log('  node setup-analytics.js G-YOUR-MEASUREMENT-ID');
  console.log('');
  console.log('📋 步骤：');
  console.log('1. 在 Google Analytics 中创建 GA4 属性');
  console.log('2. 获取测量ID (格式: G-XXXXXXXXXX)');
  console.log('3. 运行此脚本设置环境变量');
  console.log('');
  console.log('📖 详细指南：查看 GOOGLE_ANALYTICS_SETUP.md');
  process.exit(0);
}

setupAnalytics(measurementId);
