# Google Analytics 设置指南

## 📊 完整的 Google Analytics 4 设置步骤

### 第一步：创建 Google Analytics 账户和属性

1. **访问 Google Analytics**
   - 打开 https://analytics.google.com/
   - 使用您的 Google 账户登录

2. **创建账户**（如果还没有）
   - 点击"开始测量"
   - 输入账户名称（如：WorldTimeApp）
   - 选择数据共享设置
   - 点击"下一步"

3. **创建属性**
   - 属性名称：WorldTimeApp
   - 报告时区：选择您的时区
   - 货币：选择您的货币
   - 点击"下一步"

4. **业务信息**
   - 行业类别：选择"技术"或"其他"
   - 业务规模：选择适合的规模
   - 使用目标：选择"获取基准数据"
   - 点击"创建"

5. **接受服务条款**
   - 阅读并接受 Google Analytics 服务条款

### 第二步：获取测量ID

1. **找到测量ID**
   - 在 GA4 属性中，点击左下角的"管理"⚙️
   - 在"属性"列中，点击"数据流"
   - 点击您的网站数据流
   - 复制"测量ID"（格式：G-XXXXXXXXXX）

### 第三步：在项目中配置

1. **设置环境变量**
   ```bash
   # 编辑 .env.local 文件
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-您的实际测量ID
   ```

2. **示例配置**
   ```bash
   # .env.local
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-1234567890
   NEXT_PUBLIC_ENABLE_ANALYTICS=true
   ```

### 第四步：验证设置

1. **启动开发服务器**
   ```bash
   npm run dev
   ```

2. **检查浏览器控制台**
   - 打开 http://localhost:3000
   - 按 F12 打开开发者工具
   - 查看控制台是否有 GA 相关日志

3. **实时报告验证**
   - 在 Google Analytics 中，转到"报告" > "实时"
   - 访问您的网站
   - 应该能看到实时用户数据

### 第五步：高级配置（可选）

#### 1. 设置转化目标
在 Google Analytics 中：
- 管理 > 转化 > 创建转化事件
- 添加自定义事件（如：city_search, language_change）

#### 2. 配置自定义维度
- 管理 > 自定义定义 > 自定义维度
- 添加：
  - 语言 (language)
  - 时区 (timezone)
  - 设备类型 (device_type)

#### 3. 设置受众群体
- 管理 > 受众群体定义
- 创建基于语言、地理位置的受众群体

### 第六步：生产环境部署

#### Vercel 部署
1. **设置环境变量**
   ```bash
   # 在 Vercel 仪表板中
   Settings > Environment Variables
   
   Name: NEXT_PUBLIC_GA_MEASUREMENT_ID
   Value: G-您的测量ID
   ```

#### Netlify 部署
1. **设置环境变量**
   ```bash
   # 在 Netlify 仪表板中
   Site settings > Environment variables
   
   Key: NEXT_PUBLIC_GA_MEASUREMENT_ID
   Value: G-您的测量ID
   ```

#### 其他平台
- 在部署平台的环境变量设置中添加 `NEXT_PUBLIC_GA_MEASUREMENT_ID`

### 第七步：数据验证和监控

#### 1. 检查数据收集
- Google Analytics > 报告 > 实时
- 验证页面浏览量、事件、用户数据

#### 2. 设置告警
- Google Analytics > 管理 > 自定义提醒
- 设置流量异常、错误率告警

#### 3. 定期检查
- 每周检查数据质量
- 监控 Core Web Vitals
- 分析用户行为模式

## 🔧 项目中的 Analytics 功能

### 已实现的跟踪功能

1. **页面浏览跟踪**
   - 自动跟踪所有页面访问
   - 多语言页面分别统计

2. **用户行为跟踪**
   - 语言切换：`trackLanguageChange()`
   - 城市搜索：`trackCitySearch()`
   - 城市查看：`trackCityView()`
   - 城市对比：`trackCityComparison()`

3. **PWA 事件跟踪**
   - 应用安装：`trackPWAInstall()`
   - 离线使用：`trackOfflineUsage()`

4. **性能监控**
   - Core Web Vitals：CLS, FID, FCP, LCP, TTFB
   - 页面加载时间
   - 错误跟踪

5. **用户属性**
   - 语言偏好
   - 时区信息
   - 设备类型

### 使用示例

```javascript
// 在组件中使用
import { trackCitySearch, trackLanguageChange } from '@/lib/analytics'

// 跟踪搜索
trackCitySearch('Tokyo', 5, 'en')

// 跟踪语言切换
trackLanguageChange('en', 'zh')
```

## 🚨 注意事项

### 1. 隐私合规
- 项目已包含 Cookie 同意管理
- 符合 GDPR 要求
- 用户可以选择拒绝分析 Cookie

### 2. 开发环境
- 开发环境中可以禁用分析：
  ```bash
  NEXT_PUBLIC_ENABLE_ANALYTICS=false
  ```

### 3. 数据保护
- 不要在客户端代码中硬编码测量ID
- 使用环境变量管理敏感配置
- 定期检查数据收集合规性

## 📞 支持

如果在设置过程中遇到问题：

1. **检查控制台错误**：浏览器开发者工具
2. **验证测量ID**：确保格式正确（G-XXXXXXXXXX）
3. **检查网络请求**：确保 GA 请求正常发送
4. **查看实时报告**：Google Analytics 实时数据

---

**设置完成后，您将拥有完整的用户行为分析和性能监控系统！** 📊✨
