import { NextResponse } from 'next/server'

// 健康检查 API - 用于 Dokploy 和其他监控系统
export async function GET() {
  try {
    // 检查应用基本状态
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'WorldTimeApp',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
      },
      checks: {
        database: 'healthy', // 如果有数据库连接可以在这里检查
        cache: 'healthy',    // 如果有缓存可以在这里检查
        api: 'healthy'       // API 服务状态
      }
    }

    // 返回健康状态
    return NextResponse.json(healthStatus, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error) {
    // 如果出现错误，返回不健康状态
    const errorStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'WorldTimeApp',
      error: error instanceof Error ? error.message : 'Unknown error',
      checks: {
        database: 'unknown',
        cache: 'unknown',
        api: 'unhealthy'
      }
    }

    return NextResponse.json(errorStatus, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}

// 支持 HEAD 请求用于简单的健康检查
export async function HEAD() {
  try {
    return new NextResponse(null, { status: 200 })
  } catch (error) {
    return new NextResponse(null, { status: 503 })
  }
}
