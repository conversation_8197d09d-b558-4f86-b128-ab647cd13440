'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import '@/lib/i18n' // 确保 i18n 被初始化

export function useI18n() {
  const { t, i18n } = useTranslation()
  const [language, setLanguage] = useState('en')
  const [isInitialized, setIsInitialized] = useState(typeof window === 'undefined') // 服务端立即标记为已初始化

  useEffect(() => {
    if (typeof window !== 'undefined' && !isInitialized) { // 客户端且未初始化时才执行初始化逻辑
      // 等待 i18n 准备就绪
      const initLanguage = () => {
        try {
          // 获取当前语言
          const currentLang = i18n.language || 'en'
          setLanguage(currentLang)
          setIsInitialized(true)
        } catch (error) {
          console.warn('Language initialization failed:', error)
          setLanguage('en')
          setIsInitialized(true)
        }
      }

      if (i18n.isInitialized) {
        initLanguage()
      } else {
        i18n.on('initialized', initLanguage)
        return () => i18n.off('initialized', initLanguage)
      }
    }
  }, [i18n, isInitialized])

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      setLanguage(lng)
    }

    i18n.on('languageChanged', handleLanguageChange)
    return () => i18n.off('languageChanged', handleLanguageChange)
  }, [i18n, isInitialized])

  const changeLanguage = async (newLanguage: string) => {
    try {
      await i18n.changeLanguage(newLanguage)
      setLanguage(newLanguage)
    } catch (error) {
      console.warn('Language change failed:', error)
    }
  }

  // 安全的翻译函数
  const safeT = (key: string, defaultValue?: string) => {
    try {
      const translation = t(key)
      return translation && translation !== key ? translation : (defaultValue || key)
    } catch (error) {
      console.warn('Translation failed for key:', key, error)
      return defaultValue || key
    }
  }

  return {
    language,
    changeLanguage,
    t: safeT,
    isInitialized
  }
} 