// React useEffect 清理函数执行时机演示
// 重点：状态更新导致重新渲染的机制

function TimerComponent() {
  const [currentTime, setCurrentTime] = useState(new Date())  // 👈 状态
  
  useEffect(() => {
    console.log('🚀 组件挂载 - useEffect 开始执行')
    
    const timer = setInterval(() => {
      console.log('⏰ 定时器执行:', new Date().toLocaleTimeString())
      console.log('📝 准备更新状态...')
      
      setCurrentTime(new Date())  // 👈 状态更新 = 触发重新渲染！
      
      console.log('✅ 状态更新完成，React 将重新渲染组件')
    }, 1000)
    
    console.log('📝 定时器已创建，返回清理函数（但不执行）')
    
    return () => {
      console.log('🧹 清理函数执行 - 清除定时器')
      clearInterval(timer)
    }
  }, [])
  
  console.log('🔄 组件渲染中... 当前时间:', currentTime.toLocaleTimeString())
  return <div>当前时间: {currentTime.toLocaleTimeString()}</div>
}

// ==========================================
// 详细执行日志分析：
// ==========================================
 
🚀 组件挂载 - useEffect 开始执行
📝 定时器已创建，返回清理函数（但不执行）
🔄 组件渲染中... 当前时间: 14:30:24

--- 1秒后 ---
⏰ 定时器执行: 14:30:25
📝 准备更新状态...
✅ 状态更新完成，React 将重新渲染组件
🔄 组件渲染中... 当前时间: 14:30:25  👈 新的时间！

--- 1秒后 ---
⏰ 定时器执行: 14:30:26
📝 准备更新状态...
✅ 状态更新完成，React 将重新渲染组件
🔄 组件渲染中... 当前时间: 14:30:26  👈 又更新了！

--- 1秒后 ---
⏰ 定时器执行: 14:30:27
📝 准备更新状态...
✅ 状态更新完成，React 将重新渲染组件
🔄 组件渲染中... 当前时间: 14:30:27  👈 继续更新！

... (用户离开页面)

🧹 清理函数执行 - 清除定时器
*/

// ==========================================
// 核心原理解释：
// ==========================================

/*
Q: 为什么定时器每执行一秒，组件就渲染一次？

A: 因为 setCurrentTime(new Date()) 每次都在更新状态！

1. setCurrentTime 是状态更新函数
2. 每次调用都会触发 React 重新渲染
3. 即使新的 Date() 对象在毫秒级别都不同
4. React 认为状态变了，所以重新渲染组件

这就是为什么时钟能够"动起来"的原因！
*/ 