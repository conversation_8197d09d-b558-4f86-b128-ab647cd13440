import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// 支持的语言列表
const locales = ['en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'ru']
const defaultLocale = 'en'

// 从Accept-Language头部获取用户偏好语言
function getLocaleFromHeaders(request: NextRequest): string {
  const acceptLanguage = request.headers.get('accept-language')
  
  if (!acceptLanguage) return defaultLocale
  
  // 解析Accept-Language头部
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [code, q = '1'] = lang.trim().split(';q=')
      return {
        code: code.toLowerCase().split('-')[0], // 只取语言代码，忽略地区
        quality: parseFloat(q)
      }
    })
    .sort((a, b) => b.quality - a.quality)
  
  // 找到第一个支持的语言
  for (const lang of languages) {
    if (locales.includes(lang.code)) {
      return lang.code
    }
  }
  
  return defaultLocale
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  
  // 跳过API路由、静态文件和特殊路径
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/robots.txt') ||
    pathname.startsWith('/sitemap.xml') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }
  
  // 检查路径是否已经包含语言前缀
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )
  
  // 如果路径没有语言前缀，需要重定向
  if (!pathnameHasLocale) {
    // 获取用户偏好语言
    const locale = getLocaleFromHeaders(request)
    
    // 如果是默认语言(en)，重定向到 /en
    const newPathname = `/${locale}${pathname}`
    
    return NextResponse.redirect(new URL(newPathname, request.url))
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - robots.txt
     * - sitemap.xml
     */
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
} 