"use client"

import React from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { Clock, Home, Search, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { LanguageSwitchI18n } from '@/components/LanguageSwitchI18n'

export default function NotFound() {
  const params = useParams()
  const locale = (params?.locale as string) || 'en'

  // 多语言错误信息
  const messages = {
    en: {
      title: "Page Not Found",
      subtitle: "Sorry, we couldn't find the page you're looking for.",
      description: "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.",
      backHome: "Back to Home",
      searchCities: "Search Cities",
      or: "or",
      helpText: "If you think this is an error, please contact our support team."
    },
    zh: {
      title: "页面未找到",
      subtitle: "抱歉，我们找不到您要访问的页面。",
      description: "您要访问的页面可能已被删除、更名或暂时不可用。",
      backHome: "返回首页",
      searchCities: "搜索城市",
      or: "或",
      helpText: "如果您认为这是一个错误，请联系我们的支持团队。"
    },
    ja: {
      title: "ページが見つかりません",
      subtitle: "申し訳ございませんが、お探しのページが見つかりませんでした。",
      description: "お探しのページは削除されたか、名前が変更されたか、一時的に利用できない可能性があります。",
      backHome: "ホームに戻る",
      searchCities: "都市を検索",
      or: "または",
      helpText: "これがエラーだと思われる場合は、サポートチームにお問い合わせください。"
    },
    ko: {
      title: "페이지를 찾을 수 없습니다",
      subtitle: "죄송합니다. 찾으시는 페이지를 찾을 수 없습니다.",
      description: "찾으시는 페이지가 제거되었거나 이름이 변경되었거나 일시적으로 사용할 수 없을 수 있습니다.",
      backHome: "홈으로 돌아가기",
      searchCities: "도시 검색",
      or: "또는",
      helpText: "이것이 오류라고 생각되시면 지원팀에 문의해 주세요."
    },
    fr: {
      title: "Page non trouvée",
      subtitle: "Désolé, nous n'avons pas pu trouver la page que vous recherchez.",
      description: "La page que vous recherchez a peut-être été supprimée, renommée ou est temporairement indisponible.",
      backHome: "Retour à l'accueil",
      searchCities: "Rechercher des villes",
      or: "ou",
      helpText: "Si vous pensez qu'il s'agit d'une erreur, veuillez contacter notre équipe de support."
    },
    de: {
      title: "Seite nicht gefunden",
      subtitle: "Entschuldigung, wir konnten die gesuchte Seite nicht finden.",
      description: "Die gesuchte Seite wurde möglicherweise entfernt, umbenannt oder ist vorübergehend nicht verfügbar.",
      backHome: "Zurück zur Startseite",
      searchCities: "Städte suchen",
      or: "oder",
      helpText: "Wenn Sie glauben, dass dies ein Fehler ist, wenden Sie sich bitte an unser Support-Team."
    },
    es: {
      title: "Página no encontrada",
      subtitle: "Lo sentimos, no pudimos encontrar la página que buscas.",
      description: "La página que buscas puede haber sido eliminada, renombrada o estar temporalmente no disponible.",
      backHome: "Volver al inicio",
      searchCities: "Buscar ciudades",
      or: "o",
      helpText: "Si crees que esto es un error, por favor contacta a nuestro equipo de soporte."
    },
    ru: {
      title: "Страница не найдена",
      subtitle: "Извините, мы не смогли найти страницу, которую вы ищете.",
      description: "Страница, которую вы ищете, могла быть удалена, переименована или временно недоступна.",
      backHome: "Вернуться на главную",
      searchCities: "Поиск городов",
      or: "или",
      helpText: "Если вы считаете, что это ошибка, обратитесь в нашу службу поддержки."
    }
  }

  const t = messages[locale as keyof typeof messages] || messages.en

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-800">
                  WorldTimeApp
                </h1>
              </div>
            </div>
            <LanguageSwitchI18n />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center">
          {/* 404 Illustration */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-32 h-32 bg-blue-100 rounded-full mb-6">
              <span className="text-6xl font-bold text-blue-600">404</span>
            </div>
          </div>

          {/* Error Message */}
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {t.title}
          </h1>
          
          <p className="text-xl text-gray-600 mb-6 max-w-2xl mx-auto">
            {t.subtitle}
          </p>
          
          <p className="text-gray-500 mb-12 max-w-xl mx-auto">
            {t.description}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button asChild size="lg" className="min-w-[160px]">
              <Link href={`/${locale}`} className="flex items-center gap-2">
                <Home className="w-4 h-4" />
                {t.backHome}
              </Link>
            </Button>
            
            <span className="text-gray-400 hidden sm:inline">{t.or}</span>
            
            <Button asChild variant="outline" size="lg" className="min-w-[160px]">
              <Link href={`/${locale}?search=`} className="flex items-center gap-2">
                <Search className="w-4 h-4" />
                {t.searchCities}
              </Link>
            </Button>
          </div>

          {/* Popular Cities Quick Links */}
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '热门城市' : 
               locale === 'ja' ? '人気都市' :
               locale === 'ko' ? '인기 도시' :
               locale === 'fr' ? 'Villes populaires' :
               locale === 'de' ? 'Beliebte Städte' :
               locale === 'es' ? 'Ciudades populares' :
               locale === 'ru' ? 'Популярные города' :
               'Popular Cities'}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { id: 1, name: locale === 'zh' ? '北京' : 'Beijing', flag: '🇨🇳' },
                { id: 2, name: locale === 'zh' ? '东京' : 'Tokyo', flag: '🇯🇵' },
                { id: 3, name: locale === 'zh' ? '纽约' : 'New York', flag: '🇺🇸' },
                { id: 4, name: locale === 'zh' ? '伦敦' : 'London', flag: '🇬🇧' },
                { id: 5, name: locale === 'zh' ? '巴黎' : 'Paris', flag: '🇫🇷' },
                { id: 6, name: locale === 'zh' ? '悉尼' : 'Sydney', flag: '🇦🇺' },
                { id: 7, name: locale === 'zh' ? '迪拜' : 'Dubai', flag: '🇦🇪' },
                { id: 8, name: locale === 'zh' ? '新加坡' : 'Singapore', flag: '🇸🇬' },
              ].map((city) => (
                <Link
                  key={city.id}
                  href={`/${locale}/city/${city.id}`}
                  className="flex items-center gap-2 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <span className="text-lg">{city.flag}</span>
                  <span className="text-sm font-medium text-gray-700">{city.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Help Text */}
          <p className="text-sm text-gray-400">
            {t.helpText}
          </p>
        </div>
      </main>
    </div>
  )
}
