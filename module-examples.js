// 模块导入导出生活例子对应

// ==========================================
// 餐厅模块 (react 库的简化版)
// ==========================================

// react/index.js (餐厅内部)
const React = {
  createElement: function() { /* 制作招牌菜的秘方 */ },
  Component: class { /* 招牌菜的基础做法 */ }
}

const useState = function() { /* 宫保鸡丁的做法 */ }
const useEffect = function() { /* 麻婆豆腐的做法 */ }

// 默认导出 = 招牌菜
export default React

// 命名导出 = 菜单上的其他菜
export { useState, useEffect }

// ==========================================
// 顾客点菜 (在你的组件中导入)
// ==========================================

// 默认导入 = "给我招牌菜"
import React from "react"
// 顾客得到: React 对象 (招牌菜)

// 命名导入 = "给我指定的菜"
import { useState, useEffect } from "react"
// 顾客得到: useState 函数 和 useEffect 函数

// 混合点菜 = "招牌菜 + 指定菜品"
import React, { useState, useEffect } from "react"
// 顾客得到: React 对象 + useState 函数 + useEffect 函数

// ==========================================
// 工具箱模块 (next 库的简化版)
// ==========================================

// next/index.js (工具箱内部)
const NextApp = function() { /* 主要工具：应用框架 */ }

const Metadata = { /* 螺丝刀规格 */ }
const NextConfig = { /* 扳手规格 */ }

// 默认导出 = 主要工具
export default NextApp

// 命名导出 = 其他工具的说明书(类型)
export type { Metadata, NextConfig }

// ==========================================
// 工人取工具 (在你的项目中导入)
// ==========================================

// 默认导入 = "给我主要工具"
import NextApp from "next"
// 工人得到: NextApp 函数

// 类型命名导入 = "给我工具说明书"
import type { Metadata } from "next"
// 工人得到: Metadata 类型定义 (说明书)

// ==========================================
// 生活对照表
// ==========================================

/*
代码写法                    生活例子
─────────────────────────────────────────────
import React from "react"   → "要招牌菜"
import { useState } from "react" → "要{宫保鸡丁}"
import type { Metadata } from "next" → "要{工具说明书}"

export default React        → "这是我们的招牌菜"
export { useState }         → "菜单：{宫保鸡丁可点}"
export type { Metadata }   → "说明书：{螺丝刀规格可查}"
*/ 