"use client"

import React from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { Clock, Home, RefreshCw, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { LanguageSwitchI18n } from '@/components/LanguageSwitchI18n'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  const params = useParams()
  const locale = (params?.locale as string) || 'en'

  // 多语言错误信息
  const messages = {
    en: {
      title: "Something went wrong!",
      subtitle: "We encountered an unexpected error.",
      description: "Don't worry, our team has been notified and is working to fix this issue.",
      tryAgain: "Try Again",
      backHome: "Back to Home",
      or: "or",
      errorDetails: "Error Details",
      reportIssue: "Report Issue",
      helpText: "If this problem persists, please contact our support team."
    },
    zh: {
      title: "出现了问题！",
      subtitle: "我们遇到了一个意外错误。",
      description: "别担心，我们的团队已经收到通知，正在努力解决这个问题。",
      tryAgain: "重试",
      backHome: "返回首页",
      or: "或",
      errorDetails: "错误详情",
      reportIssue: "报告问题",
      helpText: "如果问题持续存在，请联系我们的支持团队。"
    },
    ja: {
      title: "問題が発生しました！",
      subtitle: "予期しないエラーが発生しました。",
      description: "ご心配なく、私たちのチームに通知され、この問題の解決に取り組んでいます。",
      tryAgain: "再試行",
      backHome: "ホームに戻る",
      or: "または",
      errorDetails: "エラーの詳細",
      reportIssue: "問題を報告",
      helpText: "この問題が続く場合は、サポートチームにお問い合わせください。"
    },
    ko: {
      title: "문제가 발생했습니다!",
      subtitle: "예상치 못한 오류가 발생했습니다.",
      description: "걱정하지 마세요. 저희 팀이 알림을 받았으며 이 문제를 해결하기 위해 노력하고 있습니다.",
      tryAgain: "다시 시도",
      backHome: "홈으로 돌아가기",
      or: "또는",
      errorDetails: "오류 세부사항",
      reportIssue: "문제 신고",
      helpText: "이 문제가 지속되면 지원팀에 문의해 주세요."
    },
    fr: {
      title: "Quelque chose s'est mal passé !",
      subtitle: "Nous avons rencontré une erreur inattendue.",
      description: "Ne vous inquiétez pas, notre équipe a été notifiée et travaille à résoudre ce problème.",
      tryAgain: "Réessayer",
      backHome: "Retour à l'accueil",
      or: "ou",
      errorDetails: "Détails de l'erreur",
      reportIssue: "Signaler un problème",
      helpText: "Si ce problème persiste, veuillez contacter notre équipe de support."
    },
    de: {
      title: "Etwas ist schief gelaufen!",
      subtitle: "Wir sind auf einen unerwarteten Fehler gestoßen.",
      description: "Keine Sorge, unser Team wurde benachrichtigt und arbeitet daran, dieses Problem zu beheben.",
      tryAgain: "Erneut versuchen",
      backHome: "Zurück zur Startseite",
      or: "oder",
      errorDetails: "Fehlerdetails",
      reportIssue: "Problem melden",
      helpText: "Wenn dieses Problem weiterhin besteht, wenden Sie sich bitte an unser Support-Team."
    },
    es: {
      title: "¡Algo salió mal!",
      subtitle: "Encontramos un error inesperado.",
      description: "No te preocupes, nuestro equipo ha sido notificado y está trabajando para solucionar este problema.",
      tryAgain: "Intentar de nuevo",
      backHome: "Volver al inicio",
      or: "o",
      errorDetails: "Detalles del error",
      reportIssue: "Reportar problema",
      helpText: "Si este problema persiste, por favor contacta a nuestro equipo de soporte."
    },
    ru: {
      title: "Что-то пошло не так!",
      subtitle: "Мы столкнулись с неожиданной ошибкой.",
      description: "Не волнуйтесь, наша команда уведомлена и работает над устранением этой проблемы.",
      tryAgain: "Попробовать снова",
      backHome: "Вернуться на главную",
      or: "или",
      errorDetails: "Детали ошибки",
      reportIssue: "Сообщить о проблеме",
      helpText: "Если эта проблема не исчезает, обратитесь в нашу службу поддержки."
    }
  }

  const t = messages[locale as keyof typeof messages] || messages.en

  // 记录错误到控制台（在生产环境中，这里应该发送到错误监控服务）
  React.useEffect(() => {
    console.error('Application Error:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-800">
                  WorldTimeApp
                </h1>
              </div>
            </div>
            <LanguageSwitchI18n />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center">
          {/* Error Illustration */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-32 h-32 bg-red-100 rounded-full mb-6">
              <AlertTriangle className="w-16 h-16 text-red-600" />
            </div>
          </div>

          {/* Error Message */}
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {t.title}
          </h1>
          
          <p className="text-xl text-gray-600 mb-6 max-w-2xl mx-auto">
            {t.subtitle}
          </p>
          
          <p className="text-gray-500 mb-12 max-w-xl mx-auto">
            {t.description}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button onClick={reset} size="lg" className="min-w-[160px]">
              <RefreshCw className="w-4 h-4 mr-2" />
              {t.tryAgain}
            </Button>
            
            <span className="text-gray-400 hidden sm:inline">{t.or}</span>
            
            <Button asChild variant="outline" size="lg" className="min-w-[160px]">
              <Link href={`/${locale}`} className="flex items-center gap-2">
                <Home className="w-4 h-4" />
                {t.backHome}
              </Link>
            </Button>
          </div>

          {/* Error Details (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8 text-left">
              <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-red-500" />
                {t.errorDetails}
              </h2>
              <div className="bg-gray-50 rounded p-4 overflow-auto">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                  {error.message}
                  {error.stack && (
                    <>
                      {'\n\nStack Trace:\n'}
                      {error.stack}
                    </>
                  )}
                  {error.digest && (
                    <>
                      {'\n\nError Digest: '}
                      {error.digest}
                    </>
                  )}
                </pre>
              </div>
            </div>
          )}

          {/* Help Text */}
          <p className="text-sm text-gray-400 mb-4">
            {t.helpText}
          </p>

          {/* Report Issue Button */}
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => {
              // 在实际应用中，这里应该打开错误报告表单或发送错误报告
              console.log('Report issue clicked', { error: error.message, locale })
            }}
          >
            {t.reportIssue}
          </Button>
        </div>
      </main>
    </div>
  )
}
