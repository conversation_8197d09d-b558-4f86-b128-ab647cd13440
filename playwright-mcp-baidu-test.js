#!/usr/bin/env node

/**
 * 使用 @playwright/mcp 测试百度网站
 * 
 * 基于探索结果，这个脚本将实际使用 @playwright/mcp 的功能
 */

console.log('🚀 使用 @playwright/mcp 测试百度网站...\n');

async function testBaiduWithMCP() {
  try {
    console.log('📦 导入 @playwright/mcp...');
    const playwrightMcp = await import('@playwright/mcp');
    
    console.log('🔧 创建 MCP 连接...');
    const connection = await playwrightMcp.createConnection();
    
    console.log('✅ MCP 连接创建成功');
    console.log('📋 服务器信息:', connection.server._serverInfo);
    console.log('🛠️  可用工具数量:', connection.context.tools.length);
    
    // 显示可用的工具
    console.log('\n🔍 可用的工具:');
    connection.context.tools.forEach((tool, index) => {
      console.log(`  ${index + 1}. ${tool.name}: ${tool.description}`);
    });
    
    // 尝试使用一些基本工具
    console.log('\n🌐 开始百度网站测试...');
    
    // 查找导航相关的工具
    const navigateTool = connection.context.tools.find(tool => 
      tool.name.includes('navigate') || tool.name.includes('goto')
    );
    
    if (navigateTool) {
      console.log('🧭 找到导航工具:', navigateTool.name);
      console.log('📝 工具描述:', navigateTool.description);
      
      // 尝试调用导航工具
      try {
        console.log('🚀 导航到百度首页...');
        
        // 这里需要根据实际的工具接口来调用
        // 由于我们不知道确切的调用方式，先尝试通过 server 调用
        const result = await connection.server.request({
          method: 'tools/call',
          params: {
            name: navigateTool.name,
            arguments: {
              url: 'https://www.baidu.com'
            }
          }
        });
        
        console.log('✅ 导航成功:', result);
        
      } catch (error) {
        console.log('❌ 导航失败:', error.message);
        console.log('💡 可能需要不同的调用方式');
      }
    }
    
    // 查找截图工具
    const screenshotTool = connection.context.tools.find(tool => 
      tool.name.includes('screenshot') || tool.name.includes('capture')
    );
    
    if (screenshotTool) {
      console.log('📸 找到截图工具:', screenshotTool.name);
      
      try {
        const result = await connection.server.request({
          method: 'tools/call',
          params: {
            name: screenshotTool.name,
            arguments: {}
          }
        });
        
        console.log('✅ 截图成功:', result);
        
      } catch (error) {
        console.log('❌ 截图失败:', error.message);
      }
    }
    
    // 查找页面内容获取工具
    const contentTool = connection.context.tools.find(tool => 
      tool.name.includes('content') || tool.name.includes('text') || tool.name.includes('snapshot')
    );
    
    if (contentTool) {
      console.log('📄 找到内容工具:', contentTool.name);
      
      try {
        const result = await connection.server.request({
          method: 'tools/call',
          params: {
            name: contentTool.name,
            arguments: {}
          }
        });
        
        console.log('✅ 获取内容成功');
        console.log('📋 内容预览:', typeof result === 'string' ? result.substring(0, 200) + '...' : result);
        
      } catch (error) {
        console.log('❌ 获取内容失败:', error.message);
      }
    }
    
    console.log('\n🎉 @playwright/mcp 测试完成！');
    
  } catch (error) {
    console.error('❌ MCP 测试失败:', error.message);
    console.error('📋 错误详情:', error);
  }
}

async function listAllTools() {
  try {
    console.log('📋 获取所有可用工具的详细信息...\n');
    
    const playwrightMcp = await import('@playwright/mcp');
    const connection = await playwrightMcp.createConnection();
    
    connection.context.tools.forEach((tool, index) => {
      console.log(`🛠️  工具 ${index + 1}: ${tool.name}`);
      console.log(`   📝 描述: ${tool.description}`);
      
      if (tool.inputSchema && tool.inputSchema.properties) {
        console.log(`   📥 输入参数:`);
        Object.entries(tool.inputSchema.properties).forEach(([param, schema]) => {
          console.log(`     - ${param}: ${schema.type || 'unknown'} ${schema.description ? '(' + schema.description + ')' : ''}`);
        });
      }
      
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 获取工具列表失败:', error.message);
  }
}

// 主函数
async function main() {
  console.log('🎯 @playwright/mcp 百度测试开始...\n');
  
  // 首先列出所有工具
  await listAllTools();
  
  // 然后进行实际测试
  await testBaiduWithMCP();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testBaiduWithMCP, listAllTools };
