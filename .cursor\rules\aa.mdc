---
description: 
globs: 
alwaysApply: true
---


1. 每次验证的时候，只在端口3000上调试运行 npm run dev 结果，如果端口 被占用， 就关闭node进程  ，重新运行程序
2. 每次用 playwright 调试后 不用关闭 playwright 浏览器 
3. 如果playwright mcp 无法运行，那么你就停止
4. 尽量用 playwright 测试网页加载结果
5. 测试过程中 遇到问题就继续修复


powershell 访问的时候
Invoke-WebRequest -Uri "http://localhost:3000/sitemap.xml" -UseBasicParsing


cmd  
curl.exe -s http://localhost:3000/sitemap.xml


当你执行命令卡着时间超过60秒的时候 就 终止 所有 node，重启node

用playwright 测试网页源码 
用playwright mcp 测试你刚刚做的，测试的时候 一看要看控制台错误 ，  测试完后 不用 关闭 playwright网页 
如果playwright mcp 无法运行，那么你就停止
 



测试的时候 要先判断 如果3000端口 被占用 ，就终止占用此端口的进程， 用playwright测试的时候一定要用3000端口   f