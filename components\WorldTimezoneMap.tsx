"use client"

import { useState, useEffect } from "react"
import { useI18n } from "@/hooks/useI18n"
import { useTranslation } from "react-i18next"

interface TimezoneRegion {
  name: string
  utcOffset: string
  color: string
  cities: {
    en: string[]
    zh: string[]
    ja: string[]
    ko: string[]
    fr: string[]
    de: string[]
    es: string[]
    ru: string[]
  }
}

const timezoneRegions: TimezoneRegion[] = [
  {
    name: "UTC-12 to UTC-10",
    utcOffset: "-12 to -10",
    color: "#FF6B6B",
    cities: {
      en: ["Honolulu", "Anchorage"],
      zh: ["檀香山", "安克雷奇"],
      ja: ["ホノルル", "アンカレッジ"],
      ko: ["호놀룰루", "앵커리지"],
      fr: ["Honolulu", "Anchorage"],
      de: ["Honolulu", "Anchorage"],
      es: ["Honolulu", "Anchorage"],
      ru: ["Гонолулу", "Анкоридж"]
    }
  },
  {
    name: "UTC-9 to UTC-6",
    utcOffset: "-9 to -6",
    color: "#4ECDC4",
    cities: {
      en: ["Los Angeles", "Denver", "Chicago"],
      zh: ["洛杉矶", "丹佛", "芝加哥"],
      ja: ["ロサンゼルス", "デンバー", "シカゴ"],
      ko: ["로스앤젤레스", "덴버", "시카고"],
      fr: ["Los Angeles", "Denver", "Chicago"],
      de: ["Los Angeles", "Denver", "Chicago"],
      es: ["Los Ángeles", "Denver", "Chicago"],
      ru: ["Лос-Анджелес", "Денвер", "Чикаго"]
    }
  },
  {
    name: "UTC-5 to UTC-3",
    utcOffset: "-5 to -3",
    color: "#45B7D1",
    cities: {
      en: ["New York", "São Paulo", "Buenos Aires"],
      zh: ["纽约", "圣保罗", "布宜诺斯艾利斯"],
      ja: ["ニューヨーク", "サンパウロ", "ブエノスアイレス"],
      ko: ["뉴욕", "상파울루", "부에노스아이레스"],
      fr: ["New York", "São Paulo", "Buenos Aires"],
      de: ["New York", "São Paulo", "Buenos Aires"],
      es: ["Nueva York", "São Paulo", "Buenos Aires"],
      ru: ["Нью-Йорк", "Сан-Паулу", "Буэнос-Айрес"]
    }
  },
  {
    name: "UTC-2 to UTC+0",
    utcOffset: "-2 to +0",
    color: "#96CEB4",
    cities: {
      en: ["London", "Casablanca", "Reykjavik"],
      zh: ["伦敦", "卡萨布兰卡", "雷克雅未克"],
      ja: ["ロンドン", "カサブランカ", "レイキャビク"],
      ko: ["런던", "카사블랑카", "레이캬비크"],
      fr: ["Londres", "Casablanca", "Reykjavik"],
      de: ["London", "Casablanca", "Reykjavik"],
      es: ["Londres", "Casablanca", "Reykjavik"],
      ru: ["Лондон", "Касабланка", "Рейкьявик"]
    }
  },
  {
    name: "UTC+1 to UTC+3",
    utcOffset: "+1 to +3",
    color: "#FFEAA7",
    cities: {
      en: ["Paris", "Cairo", "Moscow"],
      zh: ["巴黎", "开罗", "莫斯科"],
      ja: ["パリ", "カイロ", "モスクワ"],
      ko: ["파리", "카이로", "모스크바"],
      fr: ["Paris", "Le Caire", "Moscou"],
      de: ["Paris", "Kairo", "Moskau"],
      es: ["París", "El Cairo", "Moscú"],
      ru: ["Париж", "Каир", "Москва"]
    }
  },
  {
    name: "UTC+4 to UTC+6",
    utcOffset: "+4 to +6",
    color: "#DDA0DD",
    cities: {
      en: ["Dubai", "Mumbai", "Almaty"],
      zh: ["迪拜", "孟买", "阿拉木图"],
      ja: ["ドバイ", "ムンバイ", "アルマトイ"],
      ko: ["두바이", "뭄바이", "알마티"],
      fr: ["Dubaï", "Mumbai", "Almaty"],
      de: ["Dubai", "Mumbai", "Almaty"],
      es: ["Dubái", "Mumbai", "Almaty"],
      ru: ["Дубай", "Мумбаи", "Алматы"]
    }
  },
  {
    name: "UTC+7 to UTC+9",
    utcOffset: "+7 to +9",
    color: "#FFB6C1",
    cities: {
      en: ["Bangkok", "Beijing", "Tokyo"],
      zh: ["曼谷", "北京", "东京"],
      ja: ["バンコク", "北京", "東京"],
      ko: ["방콕", "베이징", "도쿄"],
      fr: ["Bangkok", "Pékin", "Tokyo"],
      de: ["Bangkok", "Peking", "Tokio"],
      es: ["Bangkok", "Pekín", "Tokio"],
      ru: ["Бангкок", "Пекин", "Токио"]
    }
  },
  {
    name: "UTC+10 to UTC+12",
    utcOffset: "+10 to +12",
    color: "#98D8C8",
    cities: {
      en: ["Sydney", "Auckland", "Fiji"],
      zh: ["悉尼", "奥克兰", "斐济"],
      ja: ["シドニー", "オークランド", "フィジー"],
      ko: ["시드니", "오클랜드", "피지"],
      fr: ["Sydney", "Auckland", "Fidji"],
      de: ["Sydney", "Auckland", "Fidschi"],
      es: ["Sídney", "Auckland", "Fiyi"],
      ru: ["Сидней", "Окленд", "Фиджи"]
    }
  }
]

export function WorldTimezoneMap() {
  const { language } = useI18n()
  const { t } = useTranslation()
  const [selectedRegion, setSelectedRegion] = useState<TimezoneRegion | null>(null)
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const getTimeForOffset = (offsetRange: string) => {
    const now = new Date()
    // 取中间值作为示例
    let offset = 0
    if (offsetRange.includes("to")) {
      const [start, end] = offsetRange.split(" to ").map(s => parseInt(s.replace("UTC", "").replace("+", "")))
      offset = Math.floor((start + end) / 2)
    }
    
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000)
    const targetTime = new Date(utc + (offset * 3600000))
    
    return targetTime.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  const getCitiesForLanguage = (region: TimezoneRegion) => {
    const langKey = language as keyof typeof region.cities
    return region.cities[langKey] || region.cities.en
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          {t('worldTimezoneMap')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('clickForDetails')}
        </p>
      </div>

      {/* 简化的世界地图 */}
      <div className="relative mb-6">
        <div className="aspect-[2/1] bg-gradient-to-b from-blue-50 to-blue-100 rounded-lg border-2 border-gray-200 overflow-hidden">
          {/* 时区条带 */}
          <div className="h-full flex">
            {timezoneRegions.map((region, index) => (
              <div
                key={index}
                className="flex-1 cursor-pointer transition-all duration-200 hover:opacity-80 relative group"
                style={{ backgroundColor: region.color }}
                onClick={() => setSelectedRegion(region)}
                onMouseEnter={() => setSelectedRegion(region)}
              >
                {/* 时区标签 */}
                <div className="absolute inset-0 flex flex-col items-center justify-center p-2">
                  <div className="text-xs font-semibold text-gray-800 text-center">
                    {region.utcOffset}
                  </div>
                  <div className="text-xs text-gray-700 mt-1 text-center">
                    {getTimeForOffset(region.utcOffset)}
                  </div>
                </div>
                
                {/* 悬停提示 */}
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 transform translate-y-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  {region.name}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 时区详情 */}
      {selectedRegion && (
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <h4 className="font-semibold text-gray-800 mb-2">
            {selectedRegion.name}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600 mb-2">
                <span className="font-medium">
                  {t('utcOffsetLabel')}
                </span> {selectedRegion.utcOffset}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">
                  {t('currentTimeLabel')}
                </span> {getTimeForOffset(selectedRegion.utcOffset)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 mb-2 font-medium">
                {t('majorCitiesLabel')}
              </p>
              <div className="flex flex-wrap gap-2">
                {getCitiesForLanguage(selectedRegion).map((city, index) => (
                  <span
                    key={index}
                    className="inline-block bg-white px-2 py-1 rounded text-xs text-gray-700 border"
                  >
                    {city}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 图例 */}
      <div className="border-t pt-4">
        <h4 className="text-sm font-semibold text-gray-800 mb-3">
          {t('timezoneLegend')}
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {timezoneRegions.map((region, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded"
                style={{ backgroundColor: region.color }}
              ></div>
              <span className="text-xs text-gray-600">
                {region.utcOffset}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* 说明文字 */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        {t('timezoneDisclaimer')}
      </div>
    </div>
  )
} 