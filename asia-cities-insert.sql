
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'guangzhou', '广州', 'Guangzhou', '中国', 'China', 
  '🇨🇳', 'Asia/Shanghai', '+08:00', '亚洲', 'Asia',
  'Asia/Shanghai', '不实行夏令时', 'No DST', 23.1291, 113.2644,
  '06:30', '18:30', 15300000, 'CNY', 'Chinese',
  '+86', 50, 'https://www.guangzhou.gov', '1000', 'City Mayor',
  765000000000, 1000, 15300, '花城', 'Flower City', 
  '中国重要城市，花城。', 'An important city in China, known as Flower City.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'shenzhen', '深圳', 'Shenzhen', '中国', 'China', 
  '🇨🇳', 'Asia/Shanghai', '+08:00', '亚洲', 'Asia',
  'Asia/Shanghai', '不实行夏令时', 'No DST', 22.5431, 114.0579,
  '06:30', '18:30', 12590000, 'CNY', 'Chinese',
  '+86', 50, 'https://www.shenzhen.gov', '1000', 'City Mayor',
  629500000000, 1000, 12590, '鹏城', 'Peng City', 
  '中国重要城市，鹏城。', 'An important city in China, known as Peng City.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'chengdu', '成都', 'Chengdu', '中国', 'China', 
  '🇨🇳', 'Asia/Shanghai', '+08:00', '亚洲', 'Asia',
  'Asia/Shanghai', '不实行夏令时', 'No DST', 30.5728, 104.0668,
  '06:30', '18:30', 16330000, 'CNY', 'Chinese',
  '+86', 50, 'https://www.chengdu.gov', '1000', 'City Mayor',
  816500000000, 1000, 16330, '天府之国', 'Land of Abundance', 
  '中国重要城市，天府之国。', 'An important city in China, known as Land of Abundance.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'hangzhou', '杭州', 'Hangzhou', '中国', 'China', 
  '🇨🇳', 'Asia/Shanghai', '+08:00', '亚洲', 'Asia',
  'Asia/Shanghai', '不实行夏令时', 'No DST', 30.2741, 120.1551,
  '06:30', '18:30', 11940000, 'CNY', 'Chinese',
  '+86', 50, 'https://www.hangzhou.gov', '1000', 'City Mayor',
  597000000000, 1000, 11940, '人间天堂', 'Paradise on Earth', 
  '中国重要城市，人间天堂。', 'An important city in China, known as Paradise on Earth.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'xian', '西安', 'Xi'an', '中国', 'China', 
  '🇨🇳', 'Asia/Shanghai', '+08:00', '亚洲', 'Asia',
  'Asia/Shanghai', '不实行夏令时', 'No DST', 34.3416, 108.9398,
  '06:30', '18:30', 12950000, 'CNY', 'Chinese',
  '+86', 50, 'https://www.xian.gov', '1000', 'City Mayor',
  647500000000, 1000, 12950, '古都', 'Ancient Capital', 
  '中国重要城市，古都。', 'An important city in China, known as Ancient Capital.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'osaka', '大阪', 'Osaka', '日本', 'Japan', 
  '🇯🇵', 'Asia/Tokyo', '+09:00', '亚洲', 'Asia',
  'Asia/Tokyo', '不实行夏令时', 'No DST', 34.6937, 135.5023,
  '06:30', '18:30', 2690000, 'JPY', 'Japanese',
  '+81', 50, 'https://www.osaka.gov', '1000', 'City Mayor',
  134500000000, 1000, 2690, '天下の台所', 'Nation's Kitchen', 
  '日本重要城市，天下の台所。', 'An important city in Japan, known as Nation's Kitchen.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'kyoto', '京都', 'Kyoto', '日本', 'Japan', 
  '🇯🇵', 'Asia/Tokyo', '+09:00', '亚洲', 'Asia',
  'Asia/Tokyo', '不实行夏令时', 'No DST', 35.0116, 135.7681,
  '06:30', '18:30', 1460000, 'JPY', 'Japanese',
  '+81', 50, 'https://www.kyoto.gov', '1000', 'City Mayor',
  73000000000, 1000, 1460, '千年古都', 'Ancient Capital', 
  '日本重要城市，千年古都。', 'An important city in Japan, known as Ancient Capital.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'yokohama', '横滨', 'Yokohama', '日本', 'Japan', 
  '🇯🇵', 'Asia/Tokyo', '+09:00', '亚洲', 'Asia',
  'Asia/Tokyo', '不实行夏令时', 'No DST', 35.4437, 139.638,
  '06:30', '18:30', 3750000, 'JPY', 'Japanese',
  '+81', 50, 'https://www.yokohama.gov', '1000', 'City Mayor',
  187500000000, 1000, 3750, '港湾都市', 'Port City', 
  '日本重要城市，港湾都市。', 'An important city in Japan, known as Port City.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'seoul', '首尔', 'Seoul', '韩国', 'South Korea', 
  '🇰🇷', 'Asia/Seoul', '+09:00', '亚洲', 'Asia',
  'Asia/Seoul', '不实行夏令时', 'No DST', 37.5665, 126.978,
  '06:30', '18:30', 9720000, 'KRW', 'Korean',
  '+82', 50, 'https://www.seoul.gov', '1000', 'City Mayor',
  486000000000, 1000, 9720, '汉江奇迹', 'Miracle on the Han River', 
  '韩国重要城市，汉江奇迹。', 'An important city in South Korea, known as Miracle on the Han River.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'busan', '釜山', 'Busan', '韩国', 'South Korea', 
  '🇰🇷', 'Asia/Seoul', '+09:00', '亚洲', 'Asia',
  'Asia/Seoul', '不实行夏令时', 'No DST', 35.1796, 129.0756,
  '06:30', '18:30', 3420000, 'KRW', 'Korean',
  '+82', 50, 'https://www.busan.gov', '1000', 'City Mayor',
  171000000000, 1000, 3420, '海洋之都', 'Maritime Capital', 
  '韩国重要城市，海洋之都。', 'An important city in South Korea, known as Maritime Capital.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'delhi', '德里', 'Delhi', '印度', 'India', 
  '🇮🇳', 'Asia/Kolkata', '+05:30', '亚洲', 'Asia',
  'Asia/Kolkata', '不实行夏令时', 'No DST', 28.7041, 77.1025,
  '06:30', '18:30', 32900000, 'INR', 'Hindi, English',
  '+91', 50, 'https://www.delhi.gov', '1000', 'City Mayor',
  1645000000000, 1000, 32900, '印度心脏', 'Heart of India', 
  '印度重要城市，印度心脏。', 'An important city in India, known as Heart of India.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'bangalore', '班加罗尔', 'Bangalore', '印度', 'India', 
  '🇮🇳', 'Asia/Kolkata', '+05:30', '亚洲', 'Asia',
  'Asia/Kolkata', '不实行夏令时', 'No DST', 12.9716, 77.5946,
  '06:30', '18:30', 13600000, 'INR', 'Hindi, English',
  '+91', 50, 'https://www.bangalore.gov', '1000', 'City Mayor',
  ************, 1000, 13600, '印度硅谷', 'Silicon Valley of India', 
  '印度重要城市，印度硅谷。', 'An important city in India, known as Silicon Valley of India.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'kuala-lumpur', '吉隆坡', 'Kuala Lumpur', '马来西亚', 'Malaysia', 
  '🇲🇾', 'Asia/Kuala_Lumpur', '+08:00', '亚洲', 'Asia',
  'Asia/Kuala_Lumpur', '不实行夏令时', 'No DST', 3.139, 101.6869,
  '06:30', '18:30', 8400000, 'MYR', 'English',
  '+1', 50, 'https://www.kuala-lumpur.gov', '1000', 'City Mayor',
  ************, 1000, 8400, '花园城市', 'Garden City', 
  '马来西亚重要城市，花园城市。', 'An important city in Malaysia, known as Garden City.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'manila', '马尼拉', 'Manila', '菲律宾', 'Philippines', 
  '🇵🇭', 'Asia/Manila', '+08:00', '亚洲', 'Asia',
  'Asia/Manila', '不实行夏令时', 'No DST', 14.5995, 120.9842,
  '06:30', '18:30', 13480000, 'PHP', 'English',
  '+1', 50, 'https://www.manila.gov', '1000', 'City Mayor',
  674000000000, 1000, 13480, '东方明珠', 'Pearl of the Orient', 
  '菲律宾重要城市，东方明珠。', 'An important city in Philippines, known as Pearl of the Orient.'
);
INSERT OR REPLACE INTO cities (
  id, city, cityEn, country, countryEn, flag, timezone, utcOffset, 
  continent, continentEn, iana, dstStatus, dstStatusEn, latitude, longitude,
  sunrise, sunset, population, currency, language, areaCode, elevation,
  website, established, mayor, gdp, area, density, nickname, nicknameEn, description, descriptionEn
) VALUES (
  'ho-chi-minh', '胡志明市', 'Ho Chi Minh City', '越南', 'Vietnam', 
  '🇻🇳', 'Asia/Ho_Chi_Minh', '+07:00', '亚洲', 'Asia',
  'Asia/Ho_Chi_Minh', '不实行夏令时', 'No DST', 10.8231, 106.6297,
  '06:30', '18:30', 9400000, 'VND', 'English',
  '+1', 50, 'https://www.ho-chi-minh.gov', '1000', 'City Mayor',
  470000000000, 1000, 9400, '东方巴黎', 'Paris of the East', 
  '越南重要城市，东方巴黎。', 'An important city in Vietnam, known as Paris of the East.'
);