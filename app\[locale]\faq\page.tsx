import React from "react"
import { <PERSON>, ChevronRight, ArrowLeft } from "lucide-react"
import Link from "next/link"
import type { Locale } from "../layout"
import { generateLocalizedMetadata } from "@/lib/metadata"

// 生成静态参数
export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'zh' },
    { locale: 'ja' },
    { locale: 'ko' },
    { locale: 'fr' },
    { locale: 'de' },
    { locale: 'es' },
    { locale: 'ru' },
  ]
}

// 生成多语言元数据
export async function generateMetadata({ params }: { params: Promise<{ locale: Locale }> }) {
  const { locale } = await params
  return generateLocalizedMetadata('faq', locale)
}

export default function FAQPage({ params }: { params: Promise<{ locale: Locale }> }) {
  const { locale } = React.use(params)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <Link href={`/${locale}`} className="text-xl font-semibold text-gray-800 hover:text-blue-600">
                  WorldTimeApp
                </Link>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Link href={`/${locale}/api-demo`} className="text-gray-600 hover:text-gray-800 text-sm">
                API Demo
              </Link>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <Link href={`/${locale}`} className="text-lg font-semibold text-gray-800 hover:text-blue-600">
                WTA
              </Link>
            </div>

            <div className="flex items-center gap-2">
              <Link href={`/${locale}/api-demo`} className="text-gray-600 hover:text-gray-800 text-sm px-2 py-1">
                API
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-100 px-4 py-2">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center gap-2 text-sm text-gray-600">
                         <Link href={`/${locale}`} className="hover:text-blue-600 flex items-center gap-1">
               <ArrowLeft className="w-3 h-3" />
               {locale === 'zh' ? '首页' : 'Home'}
             </Link>
             <ChevronRight className="w-4 h-4" />
             <span className="text-gray-800 font-medium">{locale === 'zh' ? '常见问题' : 'FAQ'}</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        <div className="text-center mb-12">
                     <h1 className="text-3xl font-bold text-gray-800 mb-4">
             {locale === 'zh' ? '常见问题' : 'Frequently Asked Questions'}
           </h1>
           <p className="text-gray-600 text-lg">
             {locale === 'zh' ? '关于WorldTimeApp时间查询工具的常见问题解答，帮助您更好地使用我们的服务。' : 'Get answers to common questions about WorldTimeApp timezone checker. Learn how to use our global time tool effectively.'}
           </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-8">
          {/* FAQ Item 1 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '什么是WorldTimeApp？' : 'What is WorldTimeApp?'}
            </h2>
            <p className="text-gray-600 leading-relaxed">
              {locale === 'zh' 
                ? 'WorldTimeApp是一个免费的全球时区查询工具，可以帮助您快速查看世界各地主要城市的当前时间。支持时区转换、夏令时信息查询，以及多语言界面。'
                : 'WorldTimeApp is a free global timezone checker tool that helps you quickly view current time in major cities worldwide. It supports timezone conversion, daylight saving time information, and multi-language interface.'
              }
            </p>
          </div>

          {/* FAQ Item 2 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '如何添加城市到我的收藏列表？' : 'How do I add cities to my favorites list?'}
            </h2>
            <p className="text-gray-600 leading-relaxed">
              {locale === 'zh' 
                ? '您可以通过搜索框搜索城市名称，然后点击搜索结果中的"+"按钮将城市添加到主页。您也可以在城市详情页面点击"添加到主页"按钮。'
                : 'You can search for city names using the search box, then click the "+" button in the search results to add cities to your homepage. You can also click the "Add to Homepage" button on the city detail page.'
              }
            </p>
          </div>

          {/* FAQ Item 3 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '时间显示是否准确？' : 'Is the time display accurate?'}
            </h2>
            <p className="text-gray-600 leading-relaxed">
              {locale === 'zh' 
                ? '是的，我们的时间显示基于您设备的系统时间和标准时区数据库，确保时间的准确性。时间每秒更新一次，包括夏令时的自动调整。'
                : 'Yes, our time display is based on your device\'s system time and standard timezone database, ensuring accuracy. The time updates every second, including automatic daylight saving time adjustments.'
              }
            </p>
          </div>

          {/* FAQ Item 4 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '支持哪些语言？' : 'What languages are supported?'}
            </h2>
            <p className="text-gray-600 leading-relaxed">
              {locale === 'zh' 
                ? 'WorldTimeApp支持8种语言：英语、中文、日语、韩语、法语、德语、西班牙语和俄语。您可以通过右上角的语言切换器选择您偏好的语言。'
                : 'WorldTimeApp supports 8 languages: English, Chinese, Japanese, Korean, French, German, Spanish, and Russian. You can select your preferred language using the language switcher in the top right corner.'
              }
            </p>
          </div>

          {/* FAQ Item 5 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '数据是否会保存在本地？' : 'Is data saved locally?'}
            </h2>
            <p className="text-gray-600 leading-relaxed">
              {locale === 'zh' 
                ? '是的，您的城市收藏列表和偏好设置会保存在浏览器的本地存储中。这意味着您的数据不会发送到服务器，完全保护您的隐私。'
                : 'Yes, your favorite cities list and preference settings are saved in your browser\'s local storage. This means your data is not sent to servers, completely protecting your privacy.'
              }
            </p>
          </div>

          {/* FAQ Item 6 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '如何设置默认时区？' : 'How to set default timezone?'}
            </h2>
            <p className="text-gray-600 leading-relaxed">
              {locale === 'zh' 
                ? '在任何城市的详情页面，您可以点击"设为默认时区"按钮。设置后，所有时间对比都将以该时区为参考。您随时可以更改或清除默认时区设置。'
                : 'On any city\'s detail page, you can click the "Set as Default Timezone" button. Once set, all time comparisons will use that timezone as reference. You can change or clear the default timezone setting at any time.'
              }
            </p>
          </div>
        </div>

        {/* Related Links */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
                     <h2 className="text-xl font-semibold text-gray-800 mb-4">
             {locale === 'zh' ? '相关链接' : 'Related Links'}
           </h2>
           <div className="grid md:grid-cols-2 gap-4">
             <Link href={`/${locale}/city/beijing`} className="text-blue-600 hover:text-blue-800 hover:underline">
               {locale === 'zh' ? '北京时间' : 'Beijing Time'}
             </Link>
             <Link href={`/${locale}/city/tokyo`} className="text-blue-600 hover:text-blue-800 hover:underline">
               {locale === 'zh' ? '东京时间' : 'Tokyo Time'}
             </Link>
             <Link href={`/${locale}/city/new-york`} className="text-blue-600 hover:text-blue-800 hover:underline">
               {locale === 'zh' ? '纽约时间' : 'New York Time'}
             </Link>
             <Link href={`/${locale}/city/london`} className="text-blue-600 hover:text-blue-800 hover:underline">
               {locale === 'zh' ? '伦敦时间' : 'London Time'}
             </Link>
             <Link href={`/${locale}/city/paris`} className="text-blue-600 hover:text-blue-800 hover:underline">
               {locale === 'zh' ? '巴黎时间' : 'Paris Time'}
             </Link>
             <Link href={`/${locale}/api-demo`} className="text-blue-600 hover:text-blue-800 hover:underline">
               API Demo
             </Link>
           </div>
        </div>
      </main>
    </div>
  )
} 