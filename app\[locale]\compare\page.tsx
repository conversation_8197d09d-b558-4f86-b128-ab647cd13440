"use client"

import React, { useState, useEffect } from 'react'
import { Search, Clock, MapPin, Plus, X, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { LanguageSwitchI18n } from '@/components/LanguageSwitchI18n'
import { CityDataLoading } from '@/components/LoadingStates'
import { CitiesService, CityDetail } from '@/lib/cities-service'
import type { Locale } from '../layout'

interface ComparePageProps {
  params: Promise<{ locale: Locale }>
}

export default function ComparePage({ params }: ComparePageProps) {
  const { locale } = React.use(params)
  
  const [cities, setCities] = useState<CityDetail[]>([])
  const [selectedCities, setSelectedCities] = useState<CityDetail[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [currentTimes, setCurrentTimes] = useState<{ [key: string]: string }>({})

  // 多语言文本
  const texts = {
    en: {
      title: "Compare Cities",
      subtitle: "Compare time zones and current time across different cities",
      searchPlaceholder: "Search for cities to compare...",
      addCity: "Add City",
      removeCity: "Remove",
      noResults: "No cities found",
      selectCities: "Select cities to compare",
      selectCitiesDesc: "Choose up to 4 cities to compare their current times and time zones",
      currentTime: "Current Time",
      timezone: "Timezone",
      utcOffset: "UTC Offset",
      timeDifference: "Time Difference",
      sameTime: "Same time",
      ahead: "ahead",
      behind: "behind",
      hours: "hours",
      minutes: "minutes",
      compare: "Compare",
      clear: "Clear All"
    },
    zh: {
      title: "城市对比",
      subtitle: "对比不同城市的时区和当前时间",
      searchPlaceholder: "搜索要对比的城市...",
      addCity: "添加城市",
      removeCity: "移除",
      noResults: "未找到城市",
      selectCities: "选择要对比的城市",
      selectCitiesDesc: "选择最多4个城市来对比它们的当前时间和时区",
      currentTime: "当前时间",
      timezone: "时区",
      utcOffset: "UTC偏移",
      timeDifference: "时差",
      sameTime: "相同时间",
      ahead: "提前",
      behind: "落后",
      hours: "小时",
      minutes: "分钟",
      compare: "对比",
      clear: "清除全部"
    },
    ja: {
      title: "都市比較",
      subtitle: "異なる都市のタイムゾーンと現在時刻を比較",
      searchPlaceholder: "比較する都市を検索...",
      addCity: "都市を追加",
      removeCity: "削除",
      noResults: "都市が見つかりません",
      selectCities: "比較する都市を選択",
      selectCitiesDesc: "最大4つの都市を選択して、現在時刻とタイムゾーンを比較",
      currentTime: "現在時刻",
      timezone: "タイムゾーン",
      utcOffset: "UTCオフセット",
      timeDifference: "時差",
      sameTime: "同じ時間",
      ahead: "進んでいる",
      behind: "遅れている",
      hours: "時間",
      minutes: "分",
      compare: "比較",
      clear: "すべてクリア"
    },
    ko: {
      title: "도시 비교",
      subtitle: "다른 도시의 시간대와 현재 시간 비교",
      searchPlaceholder: "비교할 도시 검색...",
      addCity: "도시 추가",
      removeCity: "제거",
      noResults: "도시를 찾을 수 없습니다",
      selectCities: "비교할 도시 선택",
      selectCitiesDesc: "최대 4개 도시를 선택하여 현재 시간과 시간대를 비교하세요",
      currentTime: "현재 시간",
      timezone: "시간대",
      utcOffset: "UTC 오프셋",
      timeDifference: "시차",
      sameTime: "같은 시간",
      ahead: "앞서",
      behind: "뒤처짐",
      hours: "시간",
      minutes: "분",
      compare: "비교",
      clear: "모두 지우기"
    }
  }

  const t = texts[locale] || texts.en

  // 加载城市数据
  useEffect(() => {
    const loadCities = async () => {
      setIsLoading(true)
      try {
        const allCities = await CitiesService.getAllCities()
        setCities(allCities)
      } catch (error) {
        console.error('Failed to load cities:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadCities()
  }, [])

  // 更新当前时间
  useEffect(() => {
    const updateTimes = () => {
      const times: { [key: string]: string } = {}
      selectedCities.forEach(city => {
        try {
          times[city.id] = new Intl.DateTimeFormat(locale, {
            timeZone: city.timezone,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).format(new Date())
        } catch {
          times[city.id] = '--:--:--'
        }
      })
      setCurrentTimes(times)
    }

    updateTimes()
    const interval = setInterval(updateTimes, 1000)
    return () => clearInterval(interval)
  }, [selectedCities, locale])

  // 搜索城市
  const filteredCities = cities.filter(city => {
    const cityName = locale === 'zh' ? city.city : city.cityEn
    const countryName = locale === 'zh' ? city.country : city.countryEn
    return (
      cityName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      countryName?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }).slice(0, 10)

  // 添加城市
  const addCity = (city: CityDetail) => {
    if (selectedCities.length < 4 && !selectedCities.find(c => c.id === city.id)) {
      setSelectedCities([...selectedCities, city])
      setSearchQuery('')
    }
  }

  // 移除城市
  const removeCity = (cityId: string) => {
    setSelectedCities(selectedCities.filter(c => c.id !== cityId))
  }

  // 清除所有城市
  const clearAll = () => {
    setSelectedCities([])
  }

  // 计算时差
  const calculateTimeDifference = (city1: CityDetail, city2: CityDetail) => {
    try {
      const now = new Date()
      const time1 = new Date(now.toLocaleString('en-US', { timeZone: city1.timezone }))
      const time2 = new Date(now.toLocaleString('en-US', { timeZone: city2.timezone }))
      const diffMs = time2.getTime() - time1.getTime()
      const diffHours = Math.floor(Math.abs(diffMs) / (1000 * 60 * 60))
      const diffMinutes = Math.floor((Math.abs(diffMs) % (1000 * 60 * 60)) / (1000 * 60))
      
      if (diffMs === 0) {
        return t.sameTime
      }
      
      const direction = diffMs > 0 ? t.ahead : t.behind
      if (diffMinutes === 0) {
        return `${diffHours} ${t.hours} ${direction}`
      }
      return `${diffHours}:${diffMinutes.toString().padStart(2, '0')} ${direction}`
    } catch {
      return '--'
    }
  }

  // 获取UTC偏移
  const getUTCOffset = (timezone: string) => {
    try {
      const now = new Date()
      const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000))
      const local = new Date(utc.toLocaleString('en-US', { timeZone: timezone }))
      const offset = (local.getTime() - utc.getTime()) / (1000 * 60 * 60)
      return offset >= 0 ? `UTC+${offset}` : `UTC${offset}`
    } catch {
      return 'UTC+0'
    }
  }

  if (isLoading) {
    return <CityDataLoading locale={locale} />
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="w-6 h-6 text-blue-600" />
                <a href={`/${locale}/`} className="text-xl font-semibold text-gray-800 hover:text-blue-600 transition-colors">
                  WorldTimeApp
                </a>
              </div>
            </div>
            <LanguageSwitchI18n />
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <a href={`/${locale}/`} className="text-lg font-semibold text-gray-800 hover:text-blue-600 transition-colors">
                WTA
              </a>
            </div>
            <LanguageSwitchI18n />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Title */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {t.title}
          </h1>
          <p className="text-xl text-gray-600">
            {t.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Search Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                {t.addCity}
              </h3>
              
              {/* Search Input */}
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder={t.searchPlaceholder}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Search Results */}
              {searchQuery && (
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {filteredCities.length > 0 ? (
                    filteredCities.map((city) => (
                      <button
                        key={city.id}
                        onClick={() => addCity(city)}
                        disabled={selectedCities.length >= 4 || selectedCities.find(c => c.id === city.id) !== undefined}
                        className="w-full text-left p-3 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200"
                      >
                        <div className="flex items-center gap-3">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-800">
                              {locale === 'zh' ? city.city : city.cityEn}
                            </div>
                            <div className="text-sm text-gray-500">
                              {locale === 'zh' ? city.country : city.countryEn}
                            </div>
                          </div>
                        </div>
                      </button>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      {t.noResults}
                    </div>
                  )}
                </div>
              )}

              {/* Selected Cities Count */}
              <div className="mt-4 text-sm text-gray-500">
                {selectedCities.length}/4 cities selected
              </div>
            </div>
          </div>

          {/* Comparison Panel */}
          <div className="lg:col-span-2">
            {selectedCities.length === 0 ? (
              <div className="bg-white rounded-lg shadow-md p-12 text-center">
                <Clock className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  {t.selectCities}
                </h3>
                <p className="text-gray-500">
                  {t.selectCitiesDesc}
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Clear All Button */}
                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    onClick={clearAll}
                    className="text-red-600 border-red-600 hover:bg-red-50"
                  >
                    <X className="w-4 h-4 mr-2" />
                    {t.clear}
                  </Button>
                </div>

                {/* City Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {selectedCities.map((city, index) => (
                    <div key={city.id} className="bg-white rounded-lg shadow-md p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <MapPin className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-800">
                              {locale === 'zh' ? city.city : city.cityEn}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {locale === 'zh' ? city.country : city.countryEn}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCity(city.id)}
                          className="text-red-600 hover:bg-red-50"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="space-y-3">
                        <div className="text-center">
                          <div className="text-3xl font-bold text-gray-800 mb-1">
                            {currentTimes[city.id] || '--:--:--'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {t.currentTime}
                          </div>
                        </div>

                        <div className="border-t pt-3 space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">{t.timezone}:</span>
                            <span className="text-sm font-medium text-gray-800">{city.timezone}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">{t.utcOffset}:</span>
                            <span className="text-sm font-medium text-gray-800">{getUTCOffset(city.timezone)}</span>
                          </div>
                        </div>

                        {/* Time Differences */}
                        {selectedCities.length > 1 && index === 0 && (
                          <div className="border-t pt-3">
                            <div className="text-sm font-medium text-gray-700 mb-2">{t.timeDifference}:</div>
                            <div className="space-y-1">
                              {selectedCities.slice(1).map((otherCity) => (
                                <div key={otherCity.id} className="flex items-center justify-between text-sm">
                                  <span className="text-gray-600">
                                    {locale === 'zh' ? otherCity.city : otherCity.cityEn}:
                                  </span>
                                  <span className="font-medium text-gray-800">
                                    {calculateTimeDifference(city, otherCity)}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Comparison Matrix */}
                {selectedCities.length > 2 && (
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      {t.timeDifference} Matrix
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr>
                            <th className="text-left p-2 border-b"></th>
                            {selectedCities.map((city) => (
                              <th key={city.id} className="text-center p-2 border-b font-medium text-gray-700">
                                {locale === 'zh' ? city.city : city.cityEn}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {selectedCities.map((city1, i) => (
                            <tr key={city1.id}>
                              <td className="p-2 border-b font-medium text-gray-700">{city1.name}</td>
                              {selectedCities.map((city2, j) => (
                                <td key={city2.id} className="text-center p-2 border-b">
                                  {i === j ? '-' : calculateTimeDifference(city1, city2)}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
