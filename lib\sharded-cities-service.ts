// 方案三：分片JSON文件 - 适用于超大数据集
import { promises as fs } from 'fs'
import path from 'path'
import { CityDetail } from './cities-service'

interface ShardIndex {
  [key: string]: {
    shard: string
    offset: number
    length: number
  }
}

export class ShardedCitiesService {
  private static shardCache = new Map<string, CityDetail[]>()
  private static indexCache: ShardIndex | null = null

  // 加载分片索引
  static async loadShardIndex(): Promise<ShardIndex> {
    if (this.indexCache) {
      return this.indexCache
    }

    try {
      const indexPath = path.join(process.cwd(), 'data', 'cities-index.json')
      const indexContent = await fs.readFile(indexPath, 'utf8')
      this.indexCache = JSON.parse(indexContent)
      return this.indexCache!
    } catch (error) {
      console.error('加载分片索引失败:', error)
      throw error
    }
  }

  // 加载指定分片
  static async loadShard(shardName: string): Promise<CityDetail[]> {
    if (this.shardCache.has(shardName)) {
      return this.shardCache.get(shardName)!
    }

    try {
      const shardPath = path.join(process.cwd(), 'data', 'shards', `${shardName}.json`)
      const shardContent = await fs.readFile(shardPath, 'utf8')
      const cities = JSON.parse(shardContent)
      
      // 缓存分片数据（可以设置LRU策略）
      this.shardCache.set(shardName, cities)
      
      // 如果缓存过多，清理最旧的
      if (this.shardCache.size > 10) {
        const firstKey = this.shardCache.keys().next().value
        this.shardCache.delete(firstKey)
      }
      
      return cities
    } catch (error) {
      console.error(`加载分片 ${shardName} 失败:`, error)
      throw error
    }
  }

  // 根据城市ID获取城市（使用索引）
  static async getCityById(id: string): Promise<CityDetail | null> {
    try {
      const index = await this.loadShardIndex()
      const cityIndex = index[id]
      
      if (!cityIndex) {
        return null
      }

      const shard = await this.loadShard(cityIndex.shard)
      return shard.find(city => city.id === id) || null
    } catch (error) {
      console.error('获取城市失败:', error)
      return null
    }
  }

  // 搜索城市（需要搜索多个分片）
  static async searchCities(query: string, limit: number = 50): Promise<CityDetail[]> {
    try {
      const index = await this.loadShardIndex()
      const results: CityDetail[] = []
      const queryLower = query.toLowerCase()

      // 获取所有分片名称
      const shardNames = new Set(Object.values(index).map(item => item.shard))

      for (const shardName of shardNames) {
        if (results.length >= limit) break

        const shard = await this.loadShard(shardName)
        const matches = shard.filter(city =>
          city.city.toLowerCase().includes(queryLower) ||
          city.cityEn.toLowerCase().includes(queryLower) ||
          city.country.toLowerCase().includes(queryLower) ||
          city.countryEn.toLowerCase().includes(queryLower)
        )

        results.push(...matches.slice(0, limit - results.length))
      }

      return results
    } catch (error) {
      console.error('搜索城市失败:', error)
      return []
    }
  }

  // 按国家获取城市
  static async getCitiesByCountry(country: string): Promise<CityDetail[]> {
    try {
      const index = await this.loadShardIndex()
      const results: CityDetail[] = []

      // 可以优化：在索引中添加国家信息来减少需要加载的分片
      const shardNames = new Set(Object.values(index).map(item => item.shard))

      for (const shardName of shardNames) {
        const shard = await this.loadShard(shardName)
        const matches = shard.filter(city =>
          city.country === country || city.countryEn === country
        )
        results.push(...matches)
      }

      return results
    } catch (error) {
      console.error('获取国家城市失败:', error)
      return []
    }
  }

  // 获取热门城市（从预定义的热门分片）
  static async getPopularCities(limit: number = 20): Promise<CityDetail[]> {
    try {
      const popularShard = await this.loadShard('popular')
      return popularShard.slice(0, limit)
    } catch (error) {
      console.error('获取热门城市失败:', error)
      return []
    }
  }
}

// 工具函数：生成分片文件
export class ShardGenerator {
  static async generateShards(allCities: CityDetail[], citiesPerShard: number = 1000) {
    const shards: { [key: string]: CityDetail[] } = {}
    const index: ShardIndex = {}

    // 按字母顺序分片
    allCities.sort((a, b) => a.cityEn.localeCompare(b.cityEn))

    let currentShard = 0
    let currentShardCities: CityDetail[] = []

    for (let i = 0; i < allCities.length; i++) {
      const city = allCities[i]
      currentShardCities.push(city)

      // 添加到索引
      index[city.id] = {
        shard: `shard-${currentShard}`,
        offset: currentShardCities.length - 1,
        length: 1
      }

      // 如果当前分片已满，创建新分片
      if (currentShardCities.length >= citiesPerShard || i === allCities.length - 1) {
        shards[`shard-${currentShard}`] = [...currentShardCities]
        currentShardCities = []
        currentShard++
      }
    }

    // 创建热门城市分片
    const popularCities = allCities
      .filter(city => city.population && city.population > 1000000)
      .sort((a, b) => (b.population || 0) - (a.population || 0))
      .slice(0, 100)
    
    shards['popular'] = popularCities

    return { shards, index }
  }

  static async saveShards(shards: { [key: string]: CityDetail[] }, index: ShardIndex) {
    const shardsDir = path.join(process.cwd(), 'data', 'shards')
    
    // 确保目录存在
    await fs.mkdir(shardsDir, { recursive: true })

    // 保存每个分片
    for (const [shardName, cities] of Object.entries(shards)) {
      const shardPath = path.join(shardsDir, `${shardName}.json`)
      await fs.writeFile(shardPath, JSON.stringify(cities, null, 2))
    }

    // 保存索引
    const indexPath = path.join(process.cwd(), 'data', 'cities-index.json')
    await fs.writeFile(indexPath, JSON.stringify(index, null, 2))

    console.log(`生成了 ${Object.keys(shards).length} 个分片，包含 ${Object.keys(index).length} 个城市`)
  }
}
