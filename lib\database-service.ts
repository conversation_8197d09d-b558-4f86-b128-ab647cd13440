// 方案二：使用外部数据库服务（如 PlanetScale, Supabase, MongoDB Atlas）

import { CityDetail } from './cities-service'

// 示例：使用 Supabase
export class DatabaseService {
  private static supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  private static supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  static async getAllCities(): Promise<CityDetail[]> {
    try {
      const response = await fetch(`${this.supabaseUrl}/rest/v1/cities`, {
        headers: {
          'apikey': this.supabaseKey!,
          'Authorization': `Bearer ${this.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error('数据库查询失败')
      }
      
      return await response.json()
    } catch (error) {
      console.error('数据库服务错误:', error)
      throw error
    }
  }

  static async searchCities(query: string): Promise<CityDetail[]> {
    try {
      const response = await fetch(
        `${this.supabaseUrl}/rest/v1/cities?or=(city.ilike.%${query}%,cityEn.ilike.%${query}%,country.ilike.%${query}%,countryEn.ilike.%${query}%)`,
        {
          headers: {
            'apikey': this.supabaseKey!,
            'Authorization': `Bearer ${this.supabaseKey}`,
            'Content-Type': 'application/json'
          }
        }
      )
      
      if (!response.ok) {
        throw new Error('搜索失败')
      }
      
      return await response.json()
    } catch (error) {
      console.error('搜索服务错误:', error)
      throw error
    }
  }

  static async getCitiesByCountry(country: string): Promise<CityDetail[]> {
    try {
      const response = await fetch(
        `${this.supabaseUrl}/rest/v1/cities?or=(country.eq.${country},countryEn.eq.${country})`,
        {
          headers: {
            'apikey': this.supabaseKey!,
            'Authorization': `Bearer ${this.supabaseKey}`,
            'Content-Type': 'application/json'
          }
        }
      )
      
      if (!response.ok) {
        throw new Error('查询国家城市失败')
      }
      
      return await response.json()
    } catch (error) {
      console.error('国家查询服务错误:', error)
      throw error
    }
  }
}

// 示例：使用 MongoDB Atlas
export class MongoService {
  private static apiUrl = process.env.MONGODB_DATA_API_URL
  private static apiKey = process.env.MONGODB_DATA_API_KEY
  private static database = process.env.MONGODB_DATABASE
  private static collection = 'cities'

  static async getAllCities(): Promise<CityDetail[]> {
    try {
      const response = await fetch(`${this.apiUrl}/action/find`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': this.apiKey!
        },
        body: JSON.stringify({
          dataSource: 'Cluster0',
          database: this.database,
          collection: this.collection,
          filter: {}
        })
      })

      const result = await response.json()
      return result.documents || []
    } catch (error) {
      console.error('MongoDB查询错误:', error)
      throw error
    }
  }

  static async searchCities(query: string): Promise<CityDetail[]> {
    try {
      const response = await fetch(`${this.apiUrl}/action/find`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': this.apiKey!
        },
        body: JSON.stringify({
          dataSource: 'Cluster0',
          database: this.database,
          collection: this.collection,
          filter: {
            $or: [
              { city: { $regex: query, $options: 'i' } },
              { cityEn: { $regex: query, $options: 'i' } },
              { country: { $regex: query, $options: 'i' } },
              { countryEn: { $regex: query, $options: 'i' } }
            ]
          }
        })
      })

      const result = await response.json()
      return result.documents || []
    } catch (error) {
      console.error('MongoDB搜索错误:', error)
      throw error
    }
  }
}

// 示例：使用 PlanetScale (MySQL)
export class PlanetScaleService {
  private static connectionString = process.env.DATABASE_URL

  static async getAllCities(): Promise<CityDetail[]> {
    try {
      // 使用 @planetscale/database 包
      const { connect } = await import('@planetscale/database')
      const connection = connect({
        url: this.connectionString
      })

      const results = await connection.execute('SELECT * FROM cities')
      return results.rows as CityDetail[]
    } catch (error) {
      console.error('PlanetScale查询错误:', error)
      throw error
    }
  }

  static async searchCities(query: string): Promise<CityDetail[]> {
    try {
      const { connect } = await import('@planetscale/database')
      const connection = connect({
        url: this.connectionString
      })

      const results = await connection.execute(
        'SELECT * FROM cities WHERE city LIKE ? OR cityEn LIKE ? OR country LIKE ? OR countryEn LIKE ?',
        [`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`]
      )
      
      return results.rows as CityDetail[]
    } catch (error) {
      console.error('PlanetScale搜索错误:', error)
      throw error
    }
  }
}
