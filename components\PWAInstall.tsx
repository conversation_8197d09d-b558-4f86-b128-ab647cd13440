"use client"

import React, { useState, useEffect } from 'react'
import { Download, X, Smartphone, Monitor } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface PWAInstallProps {
  locale?: string
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export function PWAInstall({ locale = 'en' }: PWAInstallProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallBanner, setShowInstallBanner] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isIOS, setIsIOS] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  // 多语言文本
  const texts = {
    en: {
      installApp: "Install App",
      installTitle: "Install WorldTimeApp",
      installDescription: "Get quick access to world time zones right from your home screen",
      installButton: "Install Now",
      laterButton: "Maybe Later",
      iosInstructions: "To install this app on your iOS device:",
      iosStep1: "Tap the share button",
      iosStep2: "Scroll down and tap 'Add to Home Screen'",
      iosStep3: "Tap 'Add' to confirm",
      benefits: {
        title: "Why install?",
        offline: "Works offline",
        fast: "Lightning fast",
        homeScreen: "Home screen access",
        notifications: "Push notifications"
      },
      alreadyInstalled: "App is already installed",
      installSuccess: "App installed successfully!",
      installError: "Installation failed. Please try again."
    },
    zh: {
      installApp: "安装应用",
      installTitle: "安装世界时钟应用",
      installDescription: "从主屏幕快速访问世界时区",
      installButton: "立即安装",
      laterButton: "稍后再说",
      iosInstructions: "在iOS设备上安装此应用：",
      iosStep1: "点击分享按钮",
      iosStep2: "向下滚动并点击'添加到主屏幕'",
      iosStep3: "点击'添加'确认",
      benefits: {
        title: "为什么要安装？",
        offline: "离线工作",
        fast: "闪电般快速",
        homeScreen: "主屏幕访问",
        notifications: "推送通知"
      },
      alreadyInstalled: "应用已安装",
      installSuccess: "应用安装成功！",
      installError: "安装失败，请重试。"
    },
    ja: {
      installApp: "アプリをインストール",
      installTitle: "WorldTimeAppをインストール",
      installDescription: "ホーム画面から世界のタイムゾーンに素早くアクセス",
      installButton: "今すぐインストール",
      laterButton: "後で",
      iosInstructions: "iOSデバイスにこのアプリをインストールするには：",
      iosStep1: "共有ボタンをタップ",
      iosStep2: "下にスクロールして'ホーム画面に追加'をタップ",
      iosStep3: "'追加'をタップして確認",
      benefits: {
        title: "なぜインストール？",
        offline: "オフラインで動作",
        fast: "超高速",
        homeScreen: "ホーム画面アクセス",
        notifications: "プッシュ通知"
      },
      alreadyInstalled: "アプリは既にインストールされています",
      installSuccess: "アプリのインストールが成功しました！",
      installError: "インストールに失敗しました。もう一度お試しください。"
    },
    ko: {
      installApp: "앱 설치",
      installTitle: "WorldTimeApp 설치",
      installDescription: "홈 화면에서 세계 시간대에 빠르게 액세스",
      installButton: "지금 설치",
      laterButton: "나중에",
      iosInstructions: "iOS 기기에 이 앱을 설치하려면:",
      iosStep1: "공유 버튼 탭",
      iosStep2: "아래로 스크롤하여 '홈 화면에 추가' 탭",
      iosStep3: "'추가'를 탭하여 확인",
      benefits: {
        title: "왜 설치해야 할까요?",
        offline: "오프라인 작동",
        fast: "번개처럼 빠름",
        homeScreen: "홈 화면 액세스",
        notifications: "푸시 알림"
      },
      alreadyInstalled: "앱이 이미 설치되어 있습니다",
      installSuccess: "앱이 성공적으로 설치되었습니다!",
      installError: "설치에 실패했습니다. 다시 시도해 주세요."
    }
  }

  const t = texts[locale as keyof typeof texts] || texts.en

  useEffect(() => {
    // 检查是否已安装
    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)
    
    // 检查是否为iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    setIsIOS(iOS)

    // 检查是否已安装PWA
    if ('getInstalledRelatedApps' in navigator) {
      (navigator as any).getInstalledRelatedApps().then((relatedApps: any[]) => {
        setIsInstalled(relatedApps.length > 0)
      })
    }

    // 监听beforeinstallprompt事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      
      // 检查是否应该显示安装横幅
      const lastDismissed = localStorage.getItem('pwa-install-dismissed')
      const lastInstallAttempt = localStorage.getItem('pwa-install-attempt')
      const now = Date.now()
      
      // 如果用户在24小时内没有拒绝或尝试安装，显示横幅
      if (!lastDismissed || (now - parseInt(lastDismissed) > 24 * 60 * 60 * 1000)) {
        if (!lastInstallAttempt || (now - parseInt(lastInstallAttempt) > 7 * 24 * 60 * 60 * 1000)) {
          setShowInstallBanner(true)
        }
      }
    }

    // 监听appinstalled事件
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowInstallBanner(false)
      setDeferredPrompt(null)
      localStorage.setItem('pwa-installed', Date.now().toString())
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      localStorage.setItem('pwa-install-attempt', Date.now().toString())
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt')
      } else {
        console.log('User dismissed the install prompt')
      }
      
      setDeferredPrompt(null)
      setShowInstallBanner(false)
    } catch (error) {
      console.error('Error during installation:', error)
    }
  }

  const handleDismiss = () => {
    setShowInstallBanner(false)
    localStorage.setItem('pwa-install-dismissed', Date.now().toString())
  }

  // 不显示横幅的条件
  if (isInstalled || isStandalone || !showInstallBanner) {
    return null
  }

  // iOS安装指导
  if (isIOS) {
    return (
      <div className="fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50 max-w-sm mx-auto">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <Smartphone className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-800">{t.installTitle}</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="p-1 h-auto"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <p className="text-sm text-gray-600 mb-3">{t.iosInstructions}</p>
        
        <ol className="text-sm text-gray-700 space-y-1 mb-4">
          <li>1. {t.iosStep1} 📤</li>
          <li>2. {t.iosStep2}</li>
          <li>3. {t.iosStep3}</li>
        </ol>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleDismiss}
          className="w-full"
        >
          {t.laterButton}
        </Button>
      </div>
    )
  }

  // 标准PWA安装横幅
  return (
    <div className="fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50 max-w-sm mx-auto">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Download className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-800">{t.installTitle}</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="p-1 h-auto"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
      
      <p className="text-sm text-gray-600 mb-4">{t.installDescription}</p>
      
      <div className="grid grid-cols-2 gap-2 mb-4 text-xs">
        <div className="flex items-center gap-1 text-gray-600">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          {t.benefits.offline}
        </div>
        <div className="flex items-center gap-1 text-gray-600">
          <div className="w-2 h-2 bg-blue-500 rounded-full" />
          {t.benefits.fast}
        </div>
        <div className="flex items-center gap-1 text-gray-600">
          <div className="w-2 h-2 bg-purple-500 rounded-full" />
          {t.benefits.homeScreen}
        </div>
        <div className="flex items-center gap-1 text-gray-600">
          <div className="w-2 h-2 bg-orange-500 rounded-full" />
          {t.benefits.notifications}
        </div>
      </div>
      
      <div className="flex gap-2">
        <Button
          onClick={handleInstallClick}
          className="flex-1"
          size="sm"
        >
          <Download className="w-4 h-4 mr-2" />
          {t.installButton}
        </Button>
        <Button
          variant="outline"
          onClick={handleDismiss}
          size="sm"
        >
          {t.laterButton}
        </Button>
      </div>
    </div>
  )
}

// PWA状态指示器组件
export function PWAStatus({ locale = 'en' }: PWAInstallProps) {
  const [isInstalled, setIsInstalled] = useState(false)
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    setIsInstalled(window.matchMedia('(display-mode: standalone)').matches)
    setIsOnline(navigator.onLine)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  if (!isInstalled) return null

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
        isOnline 
          ? 'bg-green-100 text-green-800' 
          : 'bg-orange-100 text-orange-800'
      }`}>
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${
            isOnline ? 'bg-green-500' : 'bg-orange-500'
          }`} />
          {isOnline 
            ? (locale === 'zh' ? '在线' : locale === 'ja' ? 'オンライン' : locale === 'ko' ? '온라인' : 'Online')
            : (locale === 'zh' ? '离线' : locale === 'ja' ? 'オフライン' : locale === 'ko' ? '오프라인' : 'Offline')
          }
        </div>
      </div>
    </div>
  )
}
