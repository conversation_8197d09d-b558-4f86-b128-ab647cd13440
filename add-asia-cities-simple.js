// 简单的亚洲城市添加脚本
const asiaCities = [
  // 中国主要城市
  {
    id: 'guangzhou', city: '广州', cityEn: 'Guangzhou', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 23.1291, longitude: 113.2644, population: 15300000, currency: 'CNY',
    nickname: '花城', nicknameEn: 'Flower City'
  },
  {
    id: 'shenzhen', city: '深圳', cityEn: 'Shenzhen', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 22.5431, longitude: 114.0579, population: 12590000, currency: 'CNY',
    nickname: '鹏城', nicknameEn: 'Peng City'
  },
  {
    id: 'chengdu', city: '成都', cityEn: 'Chengdu', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 30.5728, longitude: 104.0668, population: 16330000, currency: 'CNY',
    nickname: '天府之国', nicknameEn: 'Land of Abundance'
  },
  {
    id: 'hangzhou', city: '杭州', cityEn: 'Hangzhou', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 30.2741, longitude: 120.1551, population: 11940000, currency: 'CNY',
    nickname: '人间天堂', nicknameEn: 'Paradise on Earth'
  },
  {
    id: 'xian', city: '西安', cityEn: 'Xi\'an', country: '中国', countryEn: 'China', flag: '🇨🇳',
    timezone: 'Asia/Shanghai', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 34.3416, longitude: 108.9398, population: 12950000, currency: 'CNY',
    nickname: '古都', nicknameEn: 'Ancient Capital'
  },

  // 日本城市
  {
    id: 'osaka', city: '大阪', cityEn: 'Osaka', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 34.6937, longitude: 135.5023, population: 2690000, currency: 'JPY',
    nickname: '天下の台所', nicknameEn: 'Nation\'s Kitchen'
  },
  {
    id: 'kyoto', city: '京都', cityEn: 'Kyoto', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 35.0116, longitude: 135.7681, population: 1460000, currency: 'JPY',
    nickname: '千年古都', nicknameEn: 'Ancient Capital'
  },
  {
    id: 'yokohama', city: '横滨', cityEn: 'Yokohama', country: '日本', countryEn: 'Japan', flag: '🇯🇵',
    timezone: 'Asia/Tokyo', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 35.4437, longitude: 139.6380, population: 3750000, currency: 'JPY',
    nickname: '港湾都市', nicknameEn: 'Port City'
  },

  // 韩国城市
  {
    id: 'seoul', city: '首尔', cityEn: 'Seoul', country: '韩国', countryEn: 'South Korea', flag: '🇰🇷',
    timezone: 'Asia/Seoul', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 37.5665, longitude: 126.9780, population: 9720000, currency: 'KRW',
    nickname: '汉江奇迹', nicknameEn: 'Miracle on the Han River'
  },
  {
    id: 'busan', city: '釜山', cityEn: 'Busan', country: '韩国', countryEn: 'South Korea', flag: '🇰🇷',
    timezone: 'Asia/Seoul', utcOffset: '+09:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 35.1796, longitude: 129.0756, population: 3420000, currency: 'KRW',
    nickname: '海洋之都', nicknameEn: 'Maritime Capital'
  },

  // 印度城市
  {
    id: 'delhi', city: '德里', cityEn: 'Delhi', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    latitude: 28.7041, longitude: 77.1025, population: 32900000, currency: 'INR',
    nickname: '印度心脏', nicknameEn: 'Heart of India'
  },
  {
    id: 'bangalore', city: '班加罗尔', cityEn: 'Bangalore', country: '印度', countryEn: 'India', flag: '🇮🇳',
    timezone: 'Asia/Kolkata', utcOffset: '+05:30', continent: '亚洲', continentEn: 'Asia',
    latitude: 12.9716, longitude: 77.5946, population: 13600000, currency: 'INR',
    nickname: '印度硅谷', nicknameEn: 'Silicon Valley of India'
  },

  // 东南亚城市
  {
    id: 'kuala-lumpur', city: '吉隆坡', cityEn: 'Kuala Lumpur', country: '马来西亚', countryEn: 'Malaysia', flag: '🇲🇾',
    timezone: 'Asia/Kuala_Lumpur', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 3.1390, longitude: 101.6869, population: 8400000, currency: 'MYR',
    nickname: '花园城市', nicknameEn: 'Garden City'
  },
  {
    id: 'manila', city: '马尼拉', cityEn: 'Manila', country: '菲律宾', countryEn: 'Philippines', flag: '🇵🇭',
    timezone: 'Asia/Manila', utcOffset: '+08:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 14.5995, longitude: 120.9842, population: 13480000, currency: 'PHP',
    nickname: '东方明珠', nicknameEn: 'Pearl of the Orient'
  },
  {
    id: 'ho-chi-minh', city: '胡志明市', cityEn: 'Ho Chi Minh City', country: '越南', countryEn: 'Vietnam', flag: '🇻🇳',
    timezone: 'Asia/Ho_Chi_Minh', utcOffset: '+07:00', continent: '亚洲', continentEn: 'Asia',
    latitude: 10.8231, longitude: 106.6297, population: 9400000, currency: 'VND',
    nickname: '东方巴黎', nicknameEn: 'Paris of the East'
  }
];

console.log(`准备添加 ${asiaCities.length} 个亚洲城市`);
asiaCities.forEach((city, index) => {
  console.log(`${index + 1}. ${city.city} (${city.cityEn}) - ${city.country}`);
});

module.exports = asiaCities; 