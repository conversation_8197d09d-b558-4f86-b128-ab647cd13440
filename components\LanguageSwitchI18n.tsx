"use client"

import { useState } from "react"
import { Globe, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useI18n } from "@/hooks/useI18n"
import { useRouter, usePathname, useSearchParams } from "next/navigation"

export function LanguageSwitchI18n() {
  const { language, changeLanguage } = useI18n()
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  // 支持的语言列表
  const languages = [
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' }
  ]
  
  // 从URL路径获取当前语言
  const getCurrentLangFromUrl = () => {
    const pathSegments = pathname.split('/')
    const urlLang = pathSegments[1]
    return languages.find(lang => lang.code === urlLang) || languages[1] // 默认英文
  }
  
  const currentLang = getCurrentLangFromUrl()

  const handleLanguageChange = (langCode: string) => {
    // 更新 i18n 语言
    changeLanguage(langCode)
    setIsOpen(false)
    
    // 构建新的路径：将当前路径中的语言代码替换为新的语言代码
    const pathSegments = pathname.split('/')
    if (pathSegments[1] && languages.some(lang => lang.code === pathSegments[1])) {
      // 如果第一个段是语言代码，替换它
      pathSegments[1] = langCode
    } else {
      // 如果没有语言代码，添加它
      pathSegments.splice(1, 0, langCode)
    }
    
    // 保留查询参数
    const newPath = pathSegments.join('/')
    const searchParamsString = searchParams.toString()
    const finalUrl = searchParamsString ? `${newPath}?${searchParamsString}` : newPath
    
    router.push(finalUrl)
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-2 py-2 text-sm hover:bg-gray-100 transition-all duration-200 md:gap-2 md:px-3"
      >
        <Globe className="w-4 h-4 transition-transform duration-200 hover:rotate-12" />
        <span className="hidden sm:inline transition-opacity duration-200">{currentLang.nativeName}</span>
        <span className="sm:hidden transition-opacity duration-200 text-xs font-medium">{currentLang.code.toUpperCase()}</span>
        <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* 下拉菜单 */}
          <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20 max-h-64 overflow-y-auto animate-in slide-in-from-top-2 duration-200">
            <div className="py-1">
              {languages.map((lang, index) => (
                <button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center justify-between transition-all duration-150 ${
                    currentLang.code === lang.code ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                  }`}
                  style={{
                    animationDelay: `${index * 20}ms`,
                    animation: isOpen ? `fadeInUp 0.3s ease-out ${index * 20}ms both` : undefined
                  }}
                >
                  <span className="flex items-center gap-3">
                    <span className="font-medium transition-colors duration-150">{lang.nativeName}</span>
                    <span className="text-xs text-gray-500 transition-colors duration-150">{lang.name}</span>
                  </span>
                  {currentLang.code === lang.code && (
                    <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse" />
                  )}
                </button>
              ))}
            </div>
            
            {/* 底部说明 */}
            <div className="border-t border-gray-100 px-4 py-2">
              <p className="text-xs text-gray-500 text-center">
                {currentLang.code === 'zh' ? '选择您的语言' : 
                 currentLang.code === 'ja' ? '言語を選択' :
                 currentLang.code === 'ko' ? '언어 선택' :
                 currentLang.code === 'fr' ? 'Choisir la langue' :
                 currentLang.code === 'de' ? 'Sprache wählen' :
                 currentLang.code === 'es' ? 'Elegir idioma' :
                 currentLang.code === 'ru' ? 'Выбрать язык' :
                 'Choose your language'}
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  )
} 