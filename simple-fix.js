const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔧 修复 @playwright/mcp 依赖问题...\n');

// 简单的 SSE 实现
const sseContent = `// SSE Server Transport for Model Context Protocol
// Auto-generated fix for missing server/sse.js file

class SSEServerTransport {
  constructor(endpoint, response) {
    this.endpoint = endpoint;
    this.response = response;
    this.sessionId = Math.random().toString(36).substring(7);
    this.closed = false;
  }

  async handleRequest(req, res, body) {
    if (req.method === 'GET') {
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control, mcp-session-id',
        'Mcp-Session-Id': this.sessionId
      });
      
      this.response = res;
      const eventData = JSON.stringify({ type: 'connection', data: { sessionId: this.sessionId } });
      res.write('data: ' + eventData + '\\n\\n');
      
      req.on('close', () => {
        this.close();
      });
      
      return this;
    } else if (req.method === 'POST') {
      if (body) {
        this.emit('message', body);
      }
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ status: 'ok' }));
    }
    
    return this;
  }

  send(message) {
    if (this.closed || !this.response) return;
    
    try {
      const eventData = JSON.stringify({ type: 'message', data: message });
      this.response.write('data: ' + eventData + '\\n\\n');
    } catch (error) {
      console.error('Error sending SSE event:', error);
    }
  }

  close() {
    if (this.closed) return;
    
    this.closed = true;
    if (this.response) {
      try {
        this.response.end();
      } catch (error) {
        // Ignore errors when closing
      }
      this.response = null;
    }
  }
}

module.exports = { SSEServerTransport };
`;

// 查找并修复文件
function fixMissingFile() {
  const userHome = os.homedir();
  const npmCacheDir = path.join(userHome, 'AppData', 'Local', 'npm-cache', '_npx');
  
  console.log('查找 npx 缓存目录:', npmCacheDir);
  
  if (!fs.existsSync(npmCacheDir)) {
    console.log('❌ npx 缓存目录不存在');
    return false;
  }
  
  const dirs = fs.readdirSync(npmCacheDir);
  let fixed = false;
  
  for (const dir of dirs) {
    const sdkPath = path.join(npmCacheDir, dir, 'node_modules', '@modelcontextprotocol', 'sdk');
    
    if (fs.existsSync(sdkPath)) {
      console.log('找到 SDK 路径:', sdkPath);
      
      const serverDir = path.join(sdkPath, 'server');
      const ssePath = path.join(serverDir, 'sse.js');
      
      if (!fs.existsSync(serverDir)) {
        console.log('创建 server 目录...');
        fs.mkdirSync(serverDir, { recursive: true });
      }
      
      if (!fs.existsSync(ssePath)) {
        console.log('创建 sse.js 文件...');
        fs.writeFileSync(ssePath, sseContent);
        console.log('✅ 成功创建:', ssePath);
        fixed = true;
      } else {
        console.log('✅ sse.js 已存在:', ssePath);
        fixed = true;
      }
    }
  }
  
  return fixed;
}

// 执行修复
if (fixMissingFile()) {
  console.log('\n🎉 修复完成！现在可以尝试运行:');
  console.log('npx @playwright/mcp@latest --help');
} else {
  console.log('\n❌ 修复失败，请手动检查');
  console.log('或者尝试: npm install @modelcontextprotocol/sdk@latest');
}
